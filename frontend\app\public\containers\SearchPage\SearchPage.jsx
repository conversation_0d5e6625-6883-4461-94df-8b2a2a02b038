import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Columns, Column } from 'kf-bui';
import { connect } from 'react-redux';

import { SearchPagination, SearchResult } from '../../../common/components/Search';

import { SearchResultShape, ChapterShape, SectionShape } from '../../shapes';
import { selectChapterEntities, selectSectionEntities } from '../HandbookPage/selectors';
import { selectResult, selectQuery } from './selectors';
import { search } from './actions';

// Given a chapters and a chapterId, we find the root chapter
function getRootParentId(chapters, id) {
  let current = chapters[id];
  while (current.parentId) {
    current = chapters[current.parentId];
  }
  return current.id;
}

class SearchPage extends Component {
  onPaginationClick = page => this.props.search(page);

  createLink = hit => {
    const { chapters, sections, externalOrgId, handbookId } = this.props;
    if (hit.isChapter) {
      const rootChapterId = getRootParentId(chapters, hit.id);
      // The links are different if the chapter is a root chapter
      return {
        pathname: `/${externalOrgId}/${handbookId}/chapter/${rootChapterId}`,
        search: hit.id !== rootChapterId ? `id=${hit.id}` : null
      };
    }

    const rootChapterId = getRootParentId(chapters, chapters[sections[hit.id].parentId].id);

    return {
      pathname: `/${externalOrgId}/${handbookId}/chapter/${rootChapterId}`,
      search: `id=${hit.id}`
    };
  };

  render() {
    const { result, query } = this.props;

    document.title = 'Søk - KF Håndbøker';

    return (
      <div>
        <Columns>
          <Column>
            {result && <SearchResult query={query} result={result} linkFunc={this.createLink} />}
          </Column>
        </Columns>
        <Column>
          {result && <SearchPagination onPageClick={this.onPaginationClick} result={result} />}
        </Column>
      </div>
    );
  }
}

SearchPage.propTypes = {
  handbookId: PropTypes.string.isRequired,
  externalOrgId: PropTypes.string.isRequired,
  result: SearchResultShape,
  query: PropTypes.string.isRequired,
  chapters: PropTypes.objectOf(ChapterShape).isRequired,
  sections: PropTypes.objectOf(SectionShape).isRequired,
  search: PropTypes.func.isRequired
};

SearchPage.defaultProps = {
  result: null
};

export default connect(
  state => ({
    result: selectResult(state),
    query: selectQuery(state),
    chapters: selectChapterEntities(state),
    sections: selectSectionEntities(state)
  }),
  { search }
)(SearchPage);
