import React from 'react';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { Link } from 'react-router-dom';
import { connect } from 'react-redux';
import queryString from 'query-string';
import {
  Button,
  Delete,
  Icon,
  Help,
  Title,
  Columns,
  Column,
  Field,
  Input,
  Label,
  Subtitle
} from 'kf-bui';

import injectT from '../../../../common/i18n';
import BooleanToggler from '../../../components/BooleanToggler';
import { saveLocalChapter } from '../../EditorPage/actions';
import { ChapterShape, HandbookShape } from '../../../shapes';
import CentralTreeModal from '../../CentralTree';
import { selectChapterEntities } from '../../EditorPage/selectors';
import { selectHandbooks as selectCentralHandbooks } from '../../CentralTree/selectors';

function getChapter(props) {
  return props.chapters[props.match.params.chapterId];
}

class EditChapterScreen extends React.Component {
  state = {
    selectedCentralChapter: null,
    isSaving: false,
    title: getChapter(this.props) ? getChapter(this.props).title : ''
  };

  onChange = e => this.setState({ title: e.target.value });

  onSubmit = event => {
    this.setState({ isSaving: true });
    event.preventDefault();

    const chapter = getChapter(this.props);
    // If we are updating
    if (chapter) {
      this.props.saveLocalChapter({
        chapter: {
          ...chapter,
          title: this.state.title,

          // Always set localChange to true when editing something that was based on a central chapter
          localChange: Boolean(chapter.importedHandbookChapterId)
        }
      });
      return;
    }

    const { selectedCentralChapter } = this.state;
    this.props.saveLocalChapter({
      chapter: {
        title: this.state.title,
        handbookId: this.context.handbook.id,
        parentId: queryString.parse(this.props.location.search).parent,
        importedHandbookChapterId: selectedCentralChapter ? selectedCentralChapter.id : null,
        importedHandbookId: selectedCentralChapter ? selectedCentralChapter.handbookId : null
      }
    });
  };

  selectCentralChapter = chapter => {
    this.setState({ selectedCentralChapter: chapter });
    if (chapter) {
      this.setState({ title: chapter.title });
    }
  };

  isValid() {
    const { title } = this.state;
    return title.trim() !== '';
  }

  render() {
    const { centralHandbooks, location, t } = this.props;
    const { title, selectedCentralChapter, isSaving } = this.state;
    const { handbook } = this.context;
    const chapter = getChapter(this.props);

    // The abort button either takes us to the chapter, the parent of the chapter or the handbook
    let abortLink;
    if (chapter) {
      abortLink = `/editor/${chapter.handbookId}/chapter/${chapter.id}`;
    } else if (queryString.parse(location.search).parent) {
      abortLink = `/editor/${handbook.id}/chapter/${queryString.parse(location.search).parent}`;
    } else {
      abortLink = `/editor/${handbook.id}`;
    }

    return (
      <div>
        <Title>{chapter ? t('editTitle') : t('createTitle')}</Title>
        {chapter && <Subtitle>{chapter.title}</Subtitle>}
        <hr />

        <form onSubmit={this.onSubmit}>
          {!chapter && centralHandbooks.length > 0 && (
            <SelectCentralChapter
              t={t}
              selectChapter={this.selectCentralChapter}
              chapter={selectedCentralChapter}
            />
          )}
          <Field>
            <Label>{t('titleLabel')}</Label>
            <Input
              autoFocus
              readOnly={isSaving}
              placeholder={t('titleLabel')}
              name="title"
              onChange={this.onChange}
              value={title}
              required
            />
            {selectedCentralChapter && (
              <Help>
                {selectedCentralChapter.title === title ? (
                  t('autoSync')
                ) : (
                  <span>
                    <Icon icon="warning" size="small" /> {t('manualSync')}
                  </span>
                )}
              </Help>
            )}
          </Field>
          <Columns responsive="mobile">
            <Column>
              <Button as={Link} to={abortLink}>
                {t('cancelButton')}
              </Button>
            </Column>
            <Column narrow>
              <Button loading={isSaving} disabled={!this.isValid()} type="submit" color="primary">
                {t('saveButton')}
              </Button>
            </Column>
          </Columns>
        </form>
      </div>
    );
  }
}

EditChapterScreen.propTypes = {
  centralHandbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      chapterId: PropTypes.string
    })
  }).isRequired,
  location: PropTypes.shape({
    search: PropTypes.string
  }).isRequired,
  /* eslint-disable react/no-unused-prop-types */
  chapters: PropTypes.objectOf(ChapterShape).isRequired,
  saveLocalChapter: PropTypes.func.isRequired,

  t: PropTypes.func.isRequired
};

EditChapterScreen.contextTypes = {
  handbook: HandbookShape.isRequired
};

export default compose(
  connect(
    state => ({
      chapters: selectChapterEntities(state),
      centralHandbooks: selectCentralHandbooks(state)
    }),
    { saveLocalChapter }
  ),
  injectT('editor.containers.EditChapter')
)(EditChapterScreen);

const SelectCentralChapter = ({ chapter, selectChapter, t }) => (
  <BooleanToggler>
    {(toggle, value) => (
      <Field>
        <Label>{t('centralTitle')}</Label>
        <Columns responsive="mobile" vCentered>
          <Column narrow>
            <Button key="button" onClick={toggle}>
              {chapter ? chapter.title : t('centralButton')}
            </Button>
          </Column>

          {chapter && (
            <Column narrow>
              <Delete
                onClick={() => selectChapter(null)}
                title={t('removeCentralSelection')}
                aria-label={t('removeCentralSelection')}
                size="medium"
              />
            </Column>
          )}
        </Columns>
        {value ? (
          <CentralTreeModal
            title={t('centralTitle')}
            onChapterClick={selectChapter}
            key="modal"
            onHide={toggle}
          />
        ) : null}
      </Field>
    )}
  </BooleanToggler>
);

SelectCentralChapter.propTypes = {
  selectChapter: PropTypes.func.isRequired,
  chapter: ChapterShape,
  t: PropTypes.func.isRequired
};

SelectCentralChapter.defaultProps = {
  chapter: null
};
