// @flow

import React from 'react';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';

import SectionItem from './SectionItem';
import { selectSubChaptersAndSections } from './selectors';
import type { ChapterType, ChildrenType, SectionType } from '../../../types';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

type Props = {
  chapter: ChapterType,
  onChapterClick: (chapter: ChapterType) => void,
  onSectionClick: (section: SectionType) => void,
  chaptersAndSections: ChildrenType[]
};

const ChapterItem = ({ chapter, chaptersAndSections, onChapterClick, onSectionClick }: Props) => {
  const items = chaptersAndSections.map(child =>
    child.type === 'LOCAL_CHAPTER' ? (
      <ConnectedChapterNode
        chapter={child}
        key={child.id}
        onChapterClick={onChapterClick}
        onSectionClick={onSectionClick}
      />
    ) : (
      <SectionItem key={child.id} section={child} onSectionClick={onSectionClick} />
    )
  );

  return (
    <Tree.Item id={chapter.id} items={items} onClick={() => onChapterClick(chapter)}>
      <Icon icon="bookmark-o" size="small" /> {chapter.title}
    </Tree.Item>
  );
};

const makeMapStateToProps = (_, ownProps) => {
  const { chapter } = ownProps;
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(
    chapter.handbookId,
    chapter.id
  );

  const mapStateToProps = state => ({
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  });

  return mapStateToProps;
};

const ConnectedChapterNode = connect(makeMapStateToProps)(ChapterItem);
export default ConnectedChapterNode;
