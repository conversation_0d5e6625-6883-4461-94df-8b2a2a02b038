/* Purpose: Change link color from bulma-default to blue #0000ff*/
a {
  color: #0000ff;
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: none 86ms ease-out;
  transition: none 86ms ease-out;
}

footer a,
footer a:hover,
p > a {
  color: #0000ff;
  text-decoration: underline;
}

.hero.is-info .tabs.is-boxed li.is-active a {
  color: #050037 !important;
}

.content-header.field.is-grouped {
  justify-content: space-between;
}

.button.public-attachment-btn.button {
  border-color: #ffffff;
  transition: ease-in-out 0.2s;
}

.button.public-attachment-btn.button svg {
  height: 28px;
  width: auto;
}

.button.public-attachment-btn.button:hover {
  border-color: #050037;
  background-color: #050037;
  color: #ffffff;
}

.public-attachment-container {
  position: relative;
}

.attachment-popup {
  position: absolute;
  right: 0;
  top: 50px;
  background: #ffffff;
  border: 1px solid #cccccc;
  max-width: 350px;
  min-width: 200px;
  text-align: center;
  border-radius: 6px;
  z-index: 1000;
  min-height: 41px;
}

.attachment-link {
  padding: 8px 16px;
  display: block;
  text-align: left;
  transition: ease-in-out 0.1s;
  background-color: transparent;
  outline: none;
  border: none;
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 15px;
  color: #050037;
}

.attachment-link:hover {
  background-color: #050037;
  color: #ffffff;
}

.attachment-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 100px;
  flex-direction: column;
  gap: 8px;
}

.attachment-link svg {
  width: 25px;
  height: 25px;
  min-width: 25px;
  max-width: 25px;
}

.attachment-link span {
  width: max-content;
}

.chapter-title {
  margin-bottom: 0 !important;
}

.attachment-loading-message .icon {
  background-color: #b4c6e7;
  border-radius: 50%;
  padding: 16px;
  color: #4673b2;
}

.section-content {
  margin-right: 0;
}

.chapter-attachments {
  right: 26px;
}

.attachment-popup .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
  top: calc((.5rem + 1px)* -1);
  right: 7px;
}

.attachment-popup .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-bottom-color: #a7a7a7;
}

.attachment-popup .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  top: 1px;
  border-bottom-color: #fff;
}

.attachment-public-content {
  max-height: 205px;
  overflow-y: auto;
  border-radius: 6px;
}

.attachment-popup ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.attachment-popup ::-webkit-scrollbar-track {
  background: #ffffff;
}

.attachment-popup ::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 9px;
}

.attachment-popup ::-webkit-scrollbar-thumb:hover {
  background: #d1d1d1;
}

.attachment-popup ::-webkit-scrollbar-thumb:active {
  background: #d1d1d1;
}

/* Cookie Consent */

.toggle-cookies-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  width: fit-content;
  transition: background-color 0.1s ease;
  padding-right: 6px;
  border-radius: 4px;
}

.toggle-cookies-container:hover {
  background-color: #d6d6e5;
}

.toggle-cookies-container i {
  font-size: 14px;
  transition: transform 0.6s ease;
  cursor: pointer;
}

.toggle-cookies-container i.rotate {
  transform: rotate(180deg);
}

.cookies-wrapper {
  overflow: hidden;
  transition: max-height 0.6s ease;
}

.cookie-type-title {
  margin: 24px 0;
  font-size: 16px;
  font-weight: 700;
  text-align: left;
}

.cookie-type-section-container label {
  display: flex;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.cookie-type-section-container label input {
  position: absolute;
  left: -9999px;
}

.cookie-type-section-container label input:checked + span {
  background-color: #050037;
  color: #ffffff;
}

.cookie-type-section-container label input:checked + span:before {
  box-shadow: inset 0 0 0 0.4375em #00005c;
}

.cookie-type-section-container label span {
  display: flex;
  align-items: center;
  border-radius: 99em;
  transition: 0.25s ease;
  padding: 8px 32px;
  justify-content: center;
  background-color: #d9d9d9;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  color: #021815;
}

.cookie-type-section-container label span:hover {
  background-color: #d6d6e5;
}

.cookie-type-section-container {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  justify-content: center;
}

.cookie-desc {
  margin-bottom: 12px;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc {
  text-decoration: underline;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc:hover {
  color: #3273dc;
}

.toggle-cookies-button {
  font-size: 16px;
  font-weight: 700;
  line-height: 19.36px;
  text-align: left;
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #363636;
}

.cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
}


/* Toggle Button */
.toggle-button {
  position: relative;
  display: inline-block;
  color: #fff;
}

.toggle-button label {
  display: inline-block;
  text-transform: uppercase;
  cursor: pointer;
  text-align: left;
}

.toggle-button input {
  display: none;
}

.toggle-button__icon {
  cursor: pointer;
  pointer-events: none;
}

.toggle-button__icon:before,
.toggle-button__icon:after {
  content: "";
  position: absolute;
  top: 45%;
  left: 35%;
  transition: 0.2s ease-out;
}

.toggle-button--aava label {
  height: 44px;
  line-height: 44px;
  transition: all 0.2s;
  border-radius: 2rem;
}

.toggle-button--aava label:before,
.toggle-button--aava label:after {
  position: absolute;
  right: 1.5rem;
  transition: all 0.2s 0.1s ease-out;
}

.toggle-button--aava label:before {
  content: attr(data-on-text);
}

.toggle-button--aava label:after {
  content: attr(data-off-text);
}

.toggle-button--aava input[type="checkbox"] + label {
  width: 100px;
  background: #ff5335;
}

.toggle-button--aava input[type="checkbox"] + label:before {
  opacity: 0;
  transform: translate(0, 20px);
}

.toggle-button--aava input[type="checkbox"] + label:after {
  opacity: 1;
  transform: translate(0, 0);
}

.toggle-button--aava input[type="checkbox"]:checked ~ label {
  width: 100px;
  background: #61b136;
}

.toggle-button--aava input[type="checkbox"]:checked ~ label:before {
  opacity: 1;
  transform: translate(0, 0);
}

.toggle-button--aava input[type="checkbox"]:checked ~ label:after {
  opacity: 0;
  transform: translate(0, -20px);
}

.toggle-button--aava
  input[type="checkbox"]:checked
  ~ .toggle-button__icon:before {
  transform: translate(-10%, 100%) rotate(45deg);
  width: 12.66667px;
}

.toggle-button--aava
  input[type="checkbox"]:checked
  ~ .toggle-button__icon:after {
  transform: translate(30%) rotate(-45deg);
}

.toggle-button--aava .toggle-button__icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
}

.toggle-button--aava .toggle-button__icon:before,
.toggle-button--aava .toggle-button__icon:after {
  height: 3px;
  border-radius: 3px;
  background: #fff;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

.toggle-button--aava .toggle-button__icon:before {
  width: 20px;
  transform: rotate(45deg);
}

.toggle-button--aava .toggle-button__icon:after {
  width: 20px;
  transform: rotate(-45deg);
}

/* Disabled state */
.toggle-button--aava input[type="checkbox"]:disabled + label {
  background: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

.toggle-button--aava input[type="checkbox"]:disabled ~ .toggle-button__icon {
  cursor: not-allowed;
}

.toggle-button--aava
  input[type="checkbox"]:disabled
  ~ .toggle-button__icon:before,
.toggle-button--aava
  input[type="checkbox"]:disabled
  ~ .toggle-button__icon:after {
  background: #666666;
}
/* End Toggle Button */

.cookie-item {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.cookie-title .cookie-name {
  font-weight: 700;
}

.cookie-provider {
  font-weight: 500;
}

.cookie-sub-item {
  margin-top: 4px;
}

.cookie-sub-name {
  font-weight: 600;
  margin-right: 6px;
}

.cookie-sub-details {
  color: #666666;
}

.modal-card-body.comments {
  max-height: 380px;
}
.modal-card-body.comments label {
  font-weight: bold;
}