// @flow

import React from 'react';
import { Icon, Dropdown, Button } from 'kf-bui';
import type { LinkType } from '../../../../../types';
import ColorForcedDropdown from './ColorForcedDropdown';
import LinkItems from './LinkItems';

type LinkCollectionType = {
  id: string,
  title: string,
  links: LinkType[]
};

type State = {
  expanded: boolean
};

type Props = {
  linkCollections: [LinkCollectionType]
};

class DropDownMobile extends React.Component<Props, State> {
  state = {
    expanded: false
  };

  expand = () => {
    this.setState(prevState => ({ expanded: !prevState.expanded }));
  };

  collapse = () => {
    this.setState({ expanded: false });
  };

  onChildClick = (event: Event) => {
    event.stopPropagation();
  };

  render() {
    const { linkCollections } = this.props;
    const { expanded } = this.state;

    const activeStyle = {
      color: 'black',
      borderWidth: '1px',
      borderColor: 'black'
    };

    return (
      <ColorForcedDropdown
        hiddenDesktop
        right
        title="Lenker"
        active={expanded}
        onClick={this.expand}
        trigger={
          <Button style={expanded ? activeStyle : {}}>
            <p>Lenker </p> <Icon style={{ color: 'black' }} icon="chevron-down" size="small" />
          </Button>
        }
      >
        <Dropdown.Menu>
          {linkCollections.map(linkCollection => (
            <div key={linkCollection.id}>
              <Dropdown.Item onClick={this.onChildClick}>{linkCollection.title}</Dropdown.Item>
              <Dropdown.Divider />
              <LinkItems links={linkCollection.links} />
              <Dropdown.Divider />
            </div>
          ))}
        </Dropdown.Menu>
      </ColorForcedDropdown>
    );
  }
}

export default DropDownMobile;
