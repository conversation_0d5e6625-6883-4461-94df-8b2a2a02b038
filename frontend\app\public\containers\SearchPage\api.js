/* eslint-disable no-underscore-dangle */
import axios from 'axios';

const BASE_URL = '/search';

export default function getSearchResult(externalOrgId, handbookId, query, page, handbookTitle) {
  return axios
    .get(BASE_URL, {
      params: {
        externalOrgId,
        query,
        page,
        handbookId
      }
    })
    .then(res => {
      if (window._paq) {
        window._paq.push(['trackSiteSearch', query, handbookTitle, res.data.results.length]);
      }
      return res.data;
    });
}
