// @flow
import React from 'react';
import { Field, Label } from 'kf-bui';

type Props = {
  name: string,
  label: string,
  error: ?{ message: string },
  innerRef: () => void
};

const InputHookField = ({ name, label, error, innerRef, ...rest }: Props) => {
  return (
    <Field>
      <Label htmlFor={name}>{label}</Label>
      <input className="input" id={name} name={name} ref={innerRef} {...rest} />
      {error && <span>{error.message}</span>}
    </Field>
  );
};

export default InputHookField;
