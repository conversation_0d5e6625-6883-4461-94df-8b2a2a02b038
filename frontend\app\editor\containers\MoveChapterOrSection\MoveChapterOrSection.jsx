import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { FormattedMessage } from 'react-intl';
import { Link } from 'react-router-dom';

import { Button, Columns, Column, Title, Subtitle } from 'kf-bui';

import injectT from '../../../common/i18n';
import { selectElement } from '../LocalTree/actions';
import { selectSelectedElement } from '../LocalTree/selectors';
import { selectChapterEntities, selectSectionEntities } from '../EditorPage/selectors';
import { saveLocalChapter, saveLocalSection } from '../EditorPage/actions';
import { ChapterShape, HandbookShape, SectionShape } from '../../shapes';

class MoveChapterOrSection extends React.Component {
  state = {
    loading: false
  };

  // Make sure we clear the selected item when we unmount the page
  componentWillUnmount() {
    // eslint-disable-next-line react/destructuring-assignment
    this.props.selectElement(null);
  }

  onMoveClick = () => {
    this.setState({ loading: true });
    const { section, chapter, selectedElement } = this.props;
    if (chapter) {
      const updatedChapter = {
        ...chapter,
        // based on whether the selected element is handbook or a chapter
        handbookId: selectedElement.handbookId ? selectedElement.handbookId : selectedElement.id,
        // if we are setting it as a root of a handbook, the parent should be undefined
        parentId: selectedElement.handbookId ? selectedElement.id : null
      };
      this.props.saveLocalChapter({ chapter: updatedChapter });
    } else if (section) {
      const updatedSection = {
        ...section,
        handbookId: selectedElement.handbookId,
        parentId: selectedElement.id
      };
      this.props.saveLocalSection({ section: updatedSection });
    }
  };

  render() {
    const { section, chapter, selectedElement, t } = this.props;
    const { loading } = this.state;

    if (!chapter && !section) return null;

    const element = chapter || section;

    const abortLink = section
      ? `/editor/${element.handbookId}/section/${element.id}`
      : `/editor/${element.handbookId}/chapter/${element.id}`;

    return (
      <div>
        <Title>Flytt</Title>
        <Subtitle>
          <FormattedMessage
            id="editor.containers.MoveChapterOrSelection.moveElement"
            values={{
              title: <em>{element.title}</em>
            }}
          />
        </Subtitle>
        <hr />
        <Columns>
          <Column>
            {selectedElement && (
              <Subtitle textCentered>
                <FormattedMessage
                  id="editor.containers.MoveChapterOrSelection.moveTo"
                  values={{
                    title: <em>{selectedElement.title}</em>
                  }}
                />
              </Subtitle>
            )}
          </Column>
        </Columns>
        <Columns responsive="mobile">
          <Column>
            <Button disabled={loading} as={Link} to={abortLink} size="medium">
              {t('cancelButton')}
            </Button>
          </Column>
          <Column narrow>
            <Button
              disabled={!selectedElement || loading}
              loading={loading}
              color="primary"
              onClick={this.onMoveClick}
              size="medium"
            >
              {t('moveButton')}
            </Button>
          </Column>
        </Columns>
      </div>
    );
  }
}

MoveChapterOrSection.propTypes = {
  t: PropTypes.func.isRequired,
  chapter: ChapterShape,
  section: SectionShape,
  selectedElement: PropTypes.oneOfType([ChapterShape, HandbookShape]),
  selectElement: PropTypes.func.isRequired,
  saveLocalChapter: PropTypes.func.isRequired,
  saveLocalSection: PropTypes.func.isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      sectionId: PropTypes.string,
      chapterId: PropTypes.string
    }).isRequired
  }).isRequired
};

MoveChapterOrSection.defaultProps = {
  selectedElement: null,
  chapter: null,
  section: null
};

const mapStateToProps = (state, ownProps) => ({
  selectedElement: selectSelectedElement(state),
  chapter: selectChapterEntities(state)[ownProps.match.params.chapterId],
  section: selectSectionEntities(state)[ownProps.match.params.sectionId]
});

const mapDispatchToProps = {
  saveLocalChapter,
  saveLocalSection,
  selectElement
};

export default compose(
  injectT('editor.containers.MoveChapterOrSelection'),
  connect(mapStateToProps, mapDispatchToProps)
)(MoveChapterOrSection);
