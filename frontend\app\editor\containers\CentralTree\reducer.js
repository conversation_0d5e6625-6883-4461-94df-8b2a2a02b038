import { handleActions } from 'redux-actions';
import {
  fetchHandbooksSuccess,
  fetchHandbookContentSuccess,
  fetchHandbookContent
} from './actions';
import { setOrganizationSuccess } from '../App/actions';

// Chapters and sections are maps, because we lookup em up by handbookId.
export const initialState = {
  selectedHandbook: null,
  handbooks: [],
  chapters: {},
  sections: {}
};

export default handleActions(
  {
    [setOrganizationSuccess]: () => initialState,

    [fetchHandbookContent]: (state, action) => ({
      ...state,
      selectedHandbook: action.payload
    }),

    [fetchHandbooksSuccess]: (state, action) => ({
      ...state,
      handbooks: action.payload
    }),

    [fetchHandbookContentSuccess]: (state, { payload }) => ({
      ...state,
      chapters: {
        ...state.chapters,
        [payload.handbookId]: payload.chapters.map(c => ({ ...c, type: 'LOCAL_CHAPTER' }))
      },
      sections: {
        ...state.sections,
        [payload.handbookId]: payload.sections.map(s => ({ ...s, type: 'LOCAL_SECTION' }))
      }
    })
  },
  initialState
);
