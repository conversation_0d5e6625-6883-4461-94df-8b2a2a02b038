// @flow
import React, { useEffect, useState, useRef } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Title, Subtitle, Button, Columns, Column, Field, Label } from 'kf-bui';
import Wysiwyg from '../../../components/Wysiwyg';
import InputField from '../../../components/InputHookField';
import PageLeaveConfirmationModal from '../PageLeaveConfirmationModal';
import injectT from '../../../../common/i18n';

import type { CentralSectionType } from '../../../../types';

type Props = {
  centralSection: ?CentralSectionType,
  onSubmit: (values: {}) => void,
  abortLink: string,
  t: string => string,
  // eslint-disable-next-line react/no-unused-prop-types
  saveCentralSectionFunction: CentralSectionType => void,
  spellCheckUrl: string,
  history: Object
};

const defaultValues = {
  title: '',
  html: ''
};

const CentralSectionHooksForm = ({
  centralSection,
  onSubmit,
  abortLink,
  t,
  spellCheckUrl,
  history
}: Props) => {
  const initValues = { ...defaultValues, ...centralSection };
  const { handleSubmit, register, errors } = useForm({
    defaultValues: initValues
  });
  const [htmlWysiwyg, setHtmlWysiwyg] = useState<?Wysiwyg>(undefined);
  const [isDirty, setIsDirty] = useState(false);
  const [isIntendedNavigation, setIsIntendedNavigation] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [nextLocation, setNextLocation] = useState(null);
  const unblockRef = useRef(null);

  const disableButton = Object.keys(errors).length > 0;

  const getWysiwygAndSubmit = values => {
    if (htmlWysiwyg) {
      htmlWysiwyg.uploadImagesAndGetContent().then(content => {
        setIsDirty(false);
        setIsIntendedNavigation(true);
        onSubmit({ ...values, html: content });
      });
    }
  };

  const onEditorContentChange = hasChanged => {
    setIsDirty(hasChanged);
  };

  const handleCancel = () => {
    setIsIntendedNavigation(true);
  };

  const handlePageLeavePopupConfirm = () => {
    setIsIntendedNavigation(true);
    setShowPopup(false);
    if (unblockRef.current) {
      unblockRef.current();
    }
    if (nextLocation) {
      history.push(nextLocation.pathname);
    }
  };

  const handlePageLeavePopupCancel = () => {
    setShowPopup(false);
  };

  useEffect(() => {
    unblockRef.current = history.block(location => {
      if (isDirty && !isIntendedNavigation) {
        setShowPopup(true);
        setNextLocation(location);
        return false;
      }
      return true;
    });

    return () => {
      if (unblockRef.current) {
        unblockRef.current();
      }
    };
  }, [isDirty, isIntendedNavigation, history]);

  return (
    <>
      <form onSubmit={handleSubmit(getWysiwygAndSubmit)}>
        <Title>{centralSection ? 'Rediger avsnitt' : 'Opprett nytt avsnitt'}</Title>
        {centralSection && <Subtitle>{centralSection.title}</Subtitle>}
        <hr />
        <InputField
          name="title"
          label="Tittel"
          error={errors.title}
          innerRef={register({ required: 'Avsnittet må ha en tittel' })}
          onChange={() => setIsDirty(true)}
        />
        <Field>
          <Label htmlFor="html">{t('textLabel')}</Label>
          <Wysiwyg
            id="html"
            name="html"
            value={initValues.html}
            ref={c => {
              setHtmlWysiwyg(c);
            }}
            spellCheckerRpcUrl={spellCheckUrl}
            onChange={onEditorContentChange}
          />
        </Field>
        <Columns responsive="mobile">
          <Column>
            <Button as={Link} to={abortLink} onClick={handleCancel}>
              {t('cancelButton')}
            </Button>
          </Column>
          <Column narrow>
            <Button disabled={disableButton} type="submit" color="primary">
              {t('saveButton')}
            </Button>
          </Column>
        </Columns>
      </form>

      {showPopup && (
        <PageLeaveConfirmationModal
          onConfirm={handlePageLeavePopupConfirm}
          onCancel={handlePageLeavePopupCancel}
          message={t('leaveEditorConfirmationMessage')}
          title={t('leaveEditorConfirmationModalTitle')}
          t={t}
        />
      )}
    </>
  );
};

export default injectT('editor.containers.EditSection')(withRouter(CentralSectionHooksForm));
