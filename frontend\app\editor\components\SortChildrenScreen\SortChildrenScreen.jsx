// @flow
import React from 'react';
import { compose } from 'redux';
import { arrayMove, SortableContainer, SortableElement } from 'react-sortable-hoc';
import { Button, Card, Column, Icon, Columns, Group } from 'kf-bui';

import injectT from '../../../common/i18n';
import type { ChildrenType } from '../../../types';

type Props = {
  t: string => void,
  items: ChildrenType[],
  sortFunction: (itemIds: string[]) => void,
  onCancel: () => void
};

type State = {
  items: ChildrenType[],
  isSaving: boolean
};

class SortChildrenScreen extends React.Component<Props, State> {
  state = {
    items: this.props.items,
    isSaving: false
  };

  onSortEnd = ({ oldIndex, newIndex }) => {
    this.setState(state => ({
      items: arrayMove(state.items, oldIndex, newIndex)
    }));
  };

  handleSave = () => {
    const { items } = this.state;
    this.setState({ isSaving: true });
    this.props.sortFunction(items.map(i => i.id));
    this.props.onCancel();
  };

  render() {
    const { onCancel, t } = this.props;
    const { items } = this.state;
    const { isSaving } = this.state;
    return (
      <div>
        <Columns>
          <Column>
            <SortableList items={items} isSaving={isSaving} onSortEnd={this.onSortEnd} />
          </Column>
        </Columns>
        <Columns>
          <Column>
            <Group right>
              <Button
                control
                onClick={onCancel}
                disabled={isSaving}
                title={t('cancelButton.title')}
              >
                {t('cancelButton')}
              </Button>
              <Button
                control
                onClick={this.handleSave}
                color="primary"
                title={t('saveButton.title')}
                loading={isSaving}
              >
                {t('saveButton')}
              </Button>
            </Group>
          </Column>
        </Columns>
      </div>
    );
  }
}

export default compose(injectT('editor.components.SortChildren'))(SortChildrenScreen);

/* eslint-disable react/no-array-index-key */
const SortableList = SortableContainer(({ items, isSaving }) => (
  <div>
    {items.map((value, index) => (
      <SortableItem key={index} index={index} value={value} disabled={isSaving} />
    ))}
  </div>
));

const SortableItem = SortableElement(({ value }) => (
  <Card>
    <Card.Content style={{ padding: '1rem' }}>
      <Icon
        icon={
          value.type === 'CHAPTER' || value.type === 'LOCAL_CHAPTER' ? 'bookmark-o' : 'file-text-o'
        }
      />{' '}
      {value.title}
    </Card.Content>
  </Card>
));
