// @flow
/* eslint-disable react/prop-types */

import { withFormik } from 'formik';
import React from 'react';
import { compose } from 'redux';
import * as Yup from 'yup';
import { Addons, Button, Field, Form, Input, Label, SrOnly, Help } from 'kf-bui';
import injectT from '../../../../common/i18n';
import type { LinkType } from '../../../../types';

type Props = {
  // eslint-disable-next-line react/no-unused-prop-types
  sortOrder: number,
  t: string => string,
  // eslint-disable-next-line react/no-unused-prop-types
  onSave: (link: LinkType) => void,
  onCancel: () => void,
  id?: string
};

type FormProps = {
  handleSubmit: () => void,
  handleChange: () => void,
  errors: { [string]: string },
  values: { [string]: string }
};

const LinkForm = ({
  handleSubmit,
  handleChange,
  errors,
  values,
  t,
  onCancel,
  id
}: Props & FormProps) => {
  const isEditing = !!id;

  return (
    <Form onSubmit={handleSubmit}>
      <Addons style={{ paddingTop: '12px' }}>
        <Field>
          <SrOnly>
            <Label>{t('srTitleLabel')}</Label>
          </SrOnly>
          <Input
            style={errors.title ? { borderColor: 'red' } : {}}
            size="small"
            value={values.title}
            placeholder={t('titleInputPlaceholder')}
            onChange={handleChange}
            id="title"
          />
          {errors.title && <Help color="danger">{errors.title}</Help>}
        </Field>
        <Field>
          <SrOnly>
            <Label>{t('srUrlLabel')}</Label>
          </SrOnly>
          <Input
            style={errors.url ? { borderColor: 'red' } : {}}
            size="small"
            value={values.url}
            placeholder="http://"
            onChange={handleChange}
            id="url"
          />
          {errors.url && <Help color="danger">{errors.url}</Help>}
        </Field>
        {isEditing && (
          <Button control className="is-danger" size="small" onClick={onCancel}>
            {t('cancelButtonText')}
          </Button>
        )}
        <Button control size="small" color="primary" type="submit">
          {isEditing ? 'Lagre' : t('addButtonText')}
        </Button>
      </Addons>
    </Form>
  );
};

LinkForm.defaultProps = {
  id: undefined
};

export default compose(
  injectT('editor.containers.LinkPage.components.LinkForm'),
  withFormik({
    enableReinitialize: true,
    mapPropsToValues: ({ id, title, url, sortOrder }) => ({
      id,
      title,
      url,
      sortOrder
    }),
    validationSchema: Yup.object().shape({
      title: Yup.string().required('Lenken må ha en tittel'),
      url: Yup.string().required('Lenken må ha en url')
    }),
    handleSubmit: (payload, { props, resetForm }) => {
      const { id, title, url, sortOrder } = payload;

      const link = { id, title, url, sortOrder };
      props.onSave(link);
      resetForm({ title: '', url: '', id: '' });
    }
  })
)(LinkForm);
