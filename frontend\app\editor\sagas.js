import { fork, all } from 'redux-saga/effects';
import appSagas from './containers/App/saga';
import handbooksSaga from './containers/EditorPage/saga';
import centralHandbooksSaga from './containers/CentralHandbooksPage/saga';
import centralHandbookSaga from './containers/CentralHandbookEditorPage/saga';
import centralTreeSaga from './containers/CentralTree/saga';
import readingLinkSaga from '../common/components/ReadingLink/saga';

export default function*() {
  yield all([
    fork(appSagas),
    fork(handbooksSaga),
    fork(centralHandbooksSaga),
    fork(centralHandbookSaga),
    fork(centralTreeSaga),
    fork(readingLinkSaga)
  ]);
}
