<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee/"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         version="3.1">

    <display-name>handboker</display-name>
    <session-config>
        <session-timeout>120</session-timeout>
        <tracking-mode>COOKIE</tracking-mode>
    </session-config>

    <filter>
        <filter-name>ReadingLinkValidFilter</filter-name>
        <filter-class>no.kf.handboker.ReadingLinkValidFilter</filter-class>
    </filter>
    <filter>
        <filter-name>CharacterEncodingFilter</filter-name>
        <filter-class>no.kf.handboker.CharacterEncodingFilter</filter-class>
    </filter>
    <filter>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <filter-class>no.kf.handboker.cas.SingleSignOutFilter</filter-class>
    </filter>
    <filter>
        <filter-name>CAS Authentication Filter</filter-name>
        <filter-class>no.kf.handboker.cas.WrappedAuthenticationFilter</filter-class>
    </filter>

    <filter>
        <filter-name>CAS Validation Filter</filter-name>
        <filter-class>no.kf.handboker.cas.WrappedCas20ProxyReceivingTicketValidationFilter</filter-class>
    </filter>

    <filter>
        <filter-name>CAS HttpServletRequest Wrapper Filter</filter-name>
        <filter-class>no.kf.handboker.cas.WrappedAssertionThreadLocalFilter</filter-class>
    </filter>

    <filter>
        <filter-name>monitoring</filter-name>
        <filter-class>net.bull.javamelody.MonitoringFilter</filter-class>
        <init-param>
            <param-name>authorized-users</param-name>
            <param-value>kfadmin:nzj657o7i4t4</param-value>
        </init-param>
        <init-param>
            <param-name>storage-directory</param-name>
            <param-value>monitoring/</param-value>
        </init-param>
        <init-param>
            <param-name>sql-transform-pattern</param-name>
            <param-value>\([\s\?,]+\)</param-value>
        </init-param>
        <init-param>
            <param-name>system-actions-enabled</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>

    <login-config>
        <auth-method>BASIC</auth-method>
        <realm-name>Monitoring</realm-name>
    </login-config>
    <security-role>
        <role-name>monitoring</role-name>
    </security-role>
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Monitoring</web-resource-name>
            <url-pattern>/monitoring</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>monitoring</role-name>
        </auth-constraint>
        <!-- if SSL enabled (SSL and certificate must then be configured in the server)
        <user-data-constraint>
            <transport-guarantee>CONFIDENTIAL</transport-guarantee>
        </user-data-constraint>
        -->
    </security-constraint>

    <filter-mapping>
        <filter-name>ReadingLinkValidFilter</filter-name>
        <url-pattern>/readinglink/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CharacterEncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- CAS:START - Java Client Filter Mappings -->
    <filter-mapping>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CAS Authentication Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CAS Validation Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CAS HttpServletRequest Wrapper Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <!-- CAS:END -->

    <filter-mapping>
        <filter-name>monitoring</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <listener>
        <listener-class>org.jasig.cas.client.session.SingleSignOutHttpSessionListener</listener-class>
    </listener>
    <listener>
        <listener-class>net.bull.javamelody.SessionListener</listener-class>
    </listener>

    <listener>
        <listener-class>org.scalatra.servlet.ScalatraListener</listener-class>
    </listener>

    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/img/*</url-pattern>
        <url-pattern>/css/*</url-pattern>
        <url-pattern>/js/*</url-pattern>
        <url-pattern>/favicon.ico</url-pattern>
    </servlet-mapping>

    <error-page>
        <error-code>404</error-code>
        <location>/404</location>
    </error-page>

</web-app>
