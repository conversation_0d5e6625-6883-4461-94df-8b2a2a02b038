/* Purpose: Change link color from bulma-default to blue #0000ff*/
a {
  color: #0000ff;
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: none 86ms ease-out;
  transition: none 86ms ease-out;
}

footer a,
footer a:hover {
  color: #0000ff;
  text-decoration: underline;
}

i.pendingChange {
  margin-left: 5px;
  color: #fb8200;
}

.modal-card-title {
  overflow-wrap: break-word;
  flex: 1;
}

.modal-card-body.comments {
  max-height: 380px;
}
.modal-card-body.comments label {
  font-weight: bold;
}
div.card.comment {
  margin-bottom: 5px;
}
div.card.comment .card-header {
  display: block;
}
div.card.comment .card-header div {
  display: inline;
  font-weight: bold;
  margin-left: 5px;
}
div.card.comment .card-content {
  padding: "0 0 1em 0";
}
div.card.comment .card-footer-item {
  padding: 5px;
}

.masterResetComplete {
  margin-top: 10px;
  padding: 5px 10px;
  border-radius: 8px;
  background: rgb(5, 0, 55);
  color: white;
}

.central-hb-banner {
  background-color: #c5dbff !important;
  color: #050037 !important;
}

.central-hb-banner .title,
.central-hb-banner .subtitle {
  color: #050037 !important;
}

/* Toggle Button */
.toggle-button {
  position: relative;
  display: inline-block;
  color: #fff;
}

.toggle-button label {
  display: inline-block;
  text-transform: uppercase;
  cursor: pointer;
  text-align: left;
}

.toggle-button input {
  display: none;
}

.toggle-button__icon {
  cursor: pointer;
  pointer-events: none;
}

.toggle-button__icon:before,
.toggle-button__icon:after {
  content: "";
  position: absolute;
  top: 45%;
  left: 35%;
  transition: 0.2s ease-out;
}

.toggle-button--aava label {
  height: 44px;
  line-height: 44px;
  transition: all 0.2s;
  border-radius: 2rem;
}

.toggle-button--aava label:before,
.toggle-button--aava label:after {
  position: absolute;
  right: 1.5rem;
  transition: all 0.2s 0.1s ease-out;
}

.toggle-button--aava label:before {
  content: attr(data-on-text);
}

.toggle-button--aava label:after {
  content: attr(data-off-text);
}

.toggle-button--aava input[type="checkbox"] + label {
  width: 100px;
  background: #ff5335;
}

.toggle-button--aava input[type="checkbox"] + label:before {
  opacity: 0;
  transform: translate(0, 20px);
}

.toggle-button--aava input[type="checkbox"] + label:after {
  opacity: 1;
  transform: translate(0, 0);
}

.toggle-button--aava input[type="checkbox"]:checked ~ label {
  width: 100px;
  background: #61b136;
}

.toggle-button--aava input[type="checkbox"]:checked ~ label:before {
  opacity: 1;
  transform: translate(0, 0);
}

.toggle-button--aava input[type="checkbox"]:checked ~ label:after {
  opacity: 0;
  transform: translate(0, -20px);
}

.toggle-button--aava
  input[type="checkbox"]:checked
  ~ .toggle-button__icon:before {
  transform: translate(-10%, 100%) rotate(45deg);
  width: 12.66667px;
}

.toggle-button--aava
  input[type="checkbox"]:checked
  ~ .toggle-button__icon:after {
  transform: translate(30%) rotate(-45deg);
}

.toggle-button--aava .toggle-button__icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
}

.toggle-button--aava .toggle-button__icon:before,
.toggle-button--aava .toggle-button__icon:after {
  height: 3px;
  border-radius: 3px;
  background: #fff;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

.toggle-button--aava .toggle-button__icon:before {
  width: 20px;
  transform: rotate(45deg);
}

.toggle-button--aava .toggle-button__icon:after {
  width: 20px;
  transform: rotate(-45deg);
}

/* Disabled state */
.toggle-button--aava input[type="checkbox"]:disabled + label {
  background: #cccccc;
  color: #666666;
  cursor: not-allowed;
}

.toggle-button--aava input[type="checkbox"]:disabled ~ .toggle-button__icon {
  cursor: not-allowed;
}

.toggle-button--aava
  input[type="checkbox"]:disabled
  ~ .toggle-button__icon:before,
.toggle-button--aava
  input[type="checkbox"]:disabled
  ~ .toggle-button__icon:after {
  background: #666666;
}
/* End Toggle Button */

.cookie-item {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.cookie-title .cookie-name {
  font-weight: 700;
}

.cookie-provider {
  font-weight: 500;
}

.cookie-sub-item {
  margin-top: 4px;
}

.cookie-sub-name {
  font-weight: 600;
  margin-right: 6px;
}

.cookie-sub-details {
  color: #666666;
}

.pending-timestamp {
  margin-top: 12px;
  color: #363636;
  font-size: 12px;
}

.change-select-wrapper .pending-timestamp {
  margin-top: 0px;
}

.conflict-text {
  margin-left: 6px;
}
