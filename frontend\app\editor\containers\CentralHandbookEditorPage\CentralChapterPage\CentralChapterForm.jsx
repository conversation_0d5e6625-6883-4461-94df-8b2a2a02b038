// @flow
import React from 'react';
import { compose } from 'redux';
import { withFormik } from 'formik';
import { connect } from 'react-redux';
import * as Yup from 'yup';
import { Button, Title, Field, Input, Label, Form, Column, Columns, Help } from 'kf-bui';
import { withRouter } from 'react-router-dom';
import queryString from 'query-string';
import injectT from '../../../../common/i18n';
import { saveCentralChapter } from '../actions';
import type { CentralChapterType } from '../../../../types';
import { makeSelectCentralChapterById } from '../selectors';

type Props = {
  centralChapter: ?CentralChapterType,
  match: {
    url: string,
    params: {
      handbookId: string
    }
  },
  location: {
    search: string
  },
  history: {
    push: (path: string) => void,
    goBack: () => void
  },
  // eslint-disable-next-line react/no-unused-prop-types
  saveCentralChapterFunction: (chapter: CentralChapterType) => void,
  t: string => string
};

type FormProps = {
  handleSubmit: (any, props: Props) => void,
  handleChange: (event: Event) => void,
  errors: { [string]: string },
  values: { [string]: string }
};

type State = {
  isSaving: boolean
};

class CentralChapterForm extends React.Component<Props & FormProps, State> {
  state = {
    isSaving: false
  };

  render() {
    const { centralChapter, t, handleSubmit, handleChange, history, errors, values } = this.props;
    const { isSaving } = this.state;
    const disableButton = values.title === null || errors.title !== undefined;

    return (
      <Form onSubmit={handleSubmit}>
        <Title>{centralChapter ? t('editTitle') : t('createTitle')}</Title>
        <Field>
          <Label htmlFor="title">{t('titleLabel')}</Label>
          <Input
            id="title"
            autoFocus
            readOnly={isSaving}
            placeholder={t('titleLabel')}
            name="title"
            value={values.title}
            onChange={handleChange}
            required
          />
          {errors.title && <Help color="danger">{errors.title}</Help>}
        </Field>
        <Columns responsive="mobile">
          <Column>
            <Button onClick={() => history.goBack()}>{t('cancelButton')}</Button>
          </Column>
          <Column narrow>
            <Button loading={isSaving} disabled={disableButton} type="submit" color="primary">
              {t('saveButton')}
            </Button>
          </Column>
        </Columns>
      </Form>
    );
  }
}

const mapStateToProps = (state, ownProps) => {
  const { chapterId } = ownProps.match.params;
  const selectCentralChapter = makeSelectCentralChapterById(chapterId);
  return {
    centralChapter: selectCentralChapter(state)
  };
};

const mapDispatchToProps = {
  saveCentralChapterFunction: saveCentralChapter
};

export default compose(
  injectT('editor.containers.EditChapter'),
  withRouter,
  connect(mapStateToProps, mapDispatchToProps),
  withFormik({
    enableReinitialize: true,
    mapPropsToValues: props => ({
      title: props.centralChapter ? props.centralChapter.title : ''
    }),
    validationSchema: Yup.object().shape({
      title: Yup.string().required('Kapittelet må ha en tittel')
    }),
    handleSubmit: (values, { props }) => {
      const { title } = values;
      const { handbookId } = props.match.params;
      const { location } = props;
      const parentId = queryString.parse(location.search).parent;
      const chapter =
        props.centralChapter !== undefined
          ? Object.assign(props.centralChapter, { title })
          : { parentId, centralHandbookId: handbookId, title };
      props.saveCentralChapterFunction(chapter);
      props.history.goBack();
    }
  })
)(CentralChapterForm);
