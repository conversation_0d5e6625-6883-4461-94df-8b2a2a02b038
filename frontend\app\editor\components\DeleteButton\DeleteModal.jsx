import React from 'react';
import PropTypes from 'prop-types';
import { Modal, Button, Content, Checkbox } from 'kf-bui';

import injectT from '../../../common/i18n';

class DeleteModal extends React.Component {
  state = {
    submitting: false,
    confirmed: false
  };

  handleDeleteClick = keep => {
    this.setState({ submitting: true });
    this.props.onDelete(keep);
  };

  render() {
    const { text, title, t, keepButton, ...props } = this.props;
    const { submitting, confirmed } = this.state;

    // While we are submitting, the onHide is a noop so we can't
    // close the modal until the server has responded
    const onHide = submitting ? () => {} : props.onHide;

    return (
      <Modal onClose={onHide}>
        <Modal.Header onClose={onHide}>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Content>{text}</Content>
          <Checkbox
            checked={confirmed}
            onChange={e => this.setState({ confirmed: e.target.checked })}
          >
            {' '}
            Bekreft sletting
          </Checkbox>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={onHide} disabled={submitting}>
            {t('cancelButton')}
          </Button>
          {keepButton ? (
            <Button
              onClick={() => this.handleDeleteClick(true)}
              outlined
              disabled={submitting}
              loading={submitting}
            >
              Behold lokalt innhold
            </Button>
          ) : null}
          <Button
            onClick={() => this.handleDeleteClick(false)}
            disabled={!confirmed || submitting}
            color="danger"
            outlined
            loading={submitting}
          >
            {t('deleteButton')}
          </Button>
        </Modal.Footer>
      </Modal>
    );
  }
}

DeleteModal.propTypes = {
  onHide: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  text: PropTypes.node.isRequired,
  keepButton: PropTypes.bool,
  t: PropTypes.func.isRequired
};
DeleteModal.defaultProps = {
  keepButton: false
};

export default injectT('editor.components.DeleteModal')(DeleteModal);
