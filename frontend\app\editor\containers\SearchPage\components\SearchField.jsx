import React from 'react';
import PropTypes from 'prop-types';
import { Input } from 'kf-bui';

const SearchField = ({ onChange, onSubmit, query }) => (
  <form onSubmit={onSubmit}>
    <Input
      aria-label="Søk"
      iconLeft="search"
      autoFocus
      size="medium"
      placeholder="Søk"
      onChange={onChange}
      value={query}
      type="search"
    />
  </form>
);

SearchField.propTypes = {
  onChange: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  query: PropTypes.string.isRequired
};

export default SearchField;
