// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { connect } from 'react-redux';
import queryString from 'query-string';

import { Icon, Title, Column, Group, Button } from 'kf-bui';

import ChildrenList from './components/ChildrenList';
import { selectSubChaptersAndSections, selectChapterEntities } from '../HandbookPage/selectors';
import Attachments from './components/Attachments';
import { Attachment } from '../../../editor/components/Attachments/icons';
import { getAttachmentCount } from '../HandbookPage/api';

import type { ChapterType, ChildrenType, SectionType } from '../../../types';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

const scrollIntoView = id => {
  const element = document.getElementById(`content-${id}`);
  if (element) {
    element.scrollIntoView();
  }
};

type Props = {
  match: {
    params: {
      chapterId: string
    },
    url: string,
    path: string
  },
  location: {
    search: string
  },
  chapters: ChapterType,
  chaptersAndSections: ChildrenType[],
  setSection: (section: SectionType) => void,
  removeSection: (section: SectionType) => void
};

type State = {
  showAttachments: boolean,
  attachmentCount: number
};

class ChapterPage extends React.Component<Props, State> {
  wrapperRef: { current: null | HTMLDivElement };

  constructor(props: Props) {
    super(props);

    this.wrapperRef = React.createRef();
    this.state = {
      showAttachments: false,
      attachmentCount: 0
    };
  }

  componentDidMount() {
    const { location } = this.props;
    const { id } = queryString.parse(location.search);
    if (id) {
      scrollIntoView(id);
    }

    document.addEventListener('mousedown', this.handleClickOutside);
    this.handleAttachmentCount();
  }

  componentDidUpdate(prevProps) {
    const { location, match } = this.props;

    if (location !== prevProps.location) {
      this.handleAttachmentCount();
      const { id } = queryString.parse(location.search);
      if (id) {
        scrollIntoView(id);
      } else if (match.params.chapterId) {
        scrollIntoView(match.params.chapterId);
      }
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleClickOutside);
  }

  handleAttachmentCount = async () => {
    try {
      const { match } = this.props;
      const attachmentCount = await getAttachmentCount('chapter', match.params.chapterId);
      this.setState({ attachmentCount: attachmentCount.count });
    } catch (e) {
      console.error(e);
    }
  };

  handleClickOutside = (event: MouseEvent) => {
    if (
      this.wrapperRef.current &&
      event.target instanceof Node &&
      !this.wrapperRef.current.contains(event.target)
    ) {
      this.setState({ showAttachments: false });
    }
  };

  handleShowAttachments = (isShow: boolean) => {
    this.setState({ showAttachments: isShow });
  };

  render() {
    const { showAttachments, attachmentCount } = this.state;
    const { chapters, match, chaptersAndSections, setSection, removeSection } = this.props;
    const chapter = chapters[match.params.chapterId];
    if (!chapter) return null;

    return (
      <div>
        <Column>
          <Group className="content-header">
            <Title id={`content-${chapter.id}`} className="chapter-title">
              <Icon icon="bookmark-o" size="small" /> {chapter.title}
            </Title>

            {attachmentCount > 0 && (
              <div className="public-attachment-container chapter-attachments">
                <Button
                  className="public-attachment-btn"
                  onClick={() => this.handleShowAttachments(!showAttachments)}
                >
                  <Attachment />
                </Button>

                {showAttachments && (
                  <div ref={this.wrapperRef}>
                    <Attachments
                      sectionId={chapter.id}
                      type="chapter"
                      onClose={() => this.handleShowAttachments(false)}
                    />
                  </div>
                )}
              </div>
            )}
          </Group>
        </Column>
        <ChildrenList
          chaptersAndSections={chaptersAndSections}
          setSection={setSection}
          removeSection={removeSection}
        />
      </div>
    );
  }
}

const makeMapStateToProps = (state, ownProps) => {
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(
    ownProps.match.params.chapterId
  );
  return {
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state),
    chapters: selectChapterEntities(state)
  };
};

export default connect(makeMapStateToProps)(ChapterPage);
