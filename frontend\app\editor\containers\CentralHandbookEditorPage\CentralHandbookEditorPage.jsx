// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, Route, Switch } from 'react-router-dom';
import {
  Container,
  Title,
  Subtitle,
  ImageHero,
  Hero,
  Column,
  Button,
  Icon,
  Columns,
  Section
} from 'kf-bui';
import { toast } from 'kf-toaster';

import { FormattedMessage } from 'react-intl';
import { selectSession } from '../App/selectors';
import injectT from '../../../common/i18n';

import CentralHandbookScreen from './CentralHandbookScreen';
import CentralHandbookForm from './CentralHandbookModal';
import CentralTree from './CentralTree';
import CentralChapterPage from './CentralChapterPage/CentralChapterPage';
import CentralSectionPage from './CentralSectionPage/CentralSectionPage';
import { fetchCentralHandbooks, fetchCentralChapters, fetchCentralSections } from './actions';

type Props = {
  match: {
    path: string,
    params: {
      handbookId: string
    }
  },
  t: string => string,
  bannerUrl: string,
  fetchCentralHandbooksFunction: () => void,
  fetchCentralChaptersFunction: () => void,
  fetchCentralSectionsFunction: () => void
};

type State = {
  showCreateCentralHandbookModal: boolean
};

class CentralHandbookEditorPage extends React.Component<Props, State> {
  state = {
    showCreateCentralHandbookModal: false
  };

  componentDidMount() {
    /* eslint-disable react/destructuring-assignment */
    this.props.fetchCentralHandbooksFunction();
    this.props.fetchCentralChaptersFunction();
    this.props.fetchCentralSectionsFunction();
    /* eslint-enable react/destructuring-assignment */
  }

  toggleModal = () =>
    this.setState(state => ({
      showCreateCentralHandbookModal: !state.showCreateCentralHandbookModal
    }));

  render() {
    const { t, bannerUrl, match } = this.props;
    const { showCreateCentralHandbookModal } = this.state;

    document.title = `${t('title')}  - KF Sentrale Håndbøker`;

    return (
      <div>
        <ImageHero color="primary" imageUrl={bannerUrl} className="central-hb-banner">
          <Hero.Body>
            <Container>
              <Columns>
                <Column>
                  <Title>
                    <FormattedMessage id="editor.containers.CentralHandbookEditorPage.title" />
                  </Title>
                  <Subtitle>
                    <FormattedMessage id="editor.containers.CentralHandbookEditorPage.header" />
                  </Subtitle>
                </Column>
                <Column narrow>
                  <Button inverted color="primary" onClick={this.toggleModal}>
                    <Icon icon="plus" size="small" />
                    <FormattedMessage id="editor.components.NoSelection.createButton" />
                  </Button>
                  {showCreateCentralHandbookModal && (
                    <CentralHandbookForm onHide={this.toggleModal} />
                  )}
                </Column>
              </Columns>
            </Container>
          </Hero.Body>
        </ImageHero>
        <Section>
          <Container>
            <Columns>
              <Column size="1/3">
                <Switch>
                  <Route
                    exact
                    path={`${match.path}/:handbookId/*/move/:itemId`}
                    render={() => <CentralTree moving />}
                  />
                  <Route render={() => <CentralTree />} />
                </Switch>
              </Column>
              <Column size="2/3">
                <Switch>
                  <Route
                    exact
                    path={`${match.path}/:handbookId`}
                    component={CentralHandbookScreen}
                  />
                  <Route
                    path={`${match.path}/:handbookId/chapter`}
                    component={CentralChapterPage}
                  />
                  <Route
                    path={`${match.path}/:handbookId/section`}
                    component={CentralSectionPage}
                  />
                </Switch>
              </Column>
            </Columns>
          </Container>
        </Section>
      </div>
    );
  }
}

const mapStateToProps = state => ({
  session: selectSession(state)
});

const mapDispatchToProps = {
  toast: msg => toast.success(msg),
  fetchCentralHandbooksFunction: fetchCentralHandbooks,
  fetchCentralChaptersFunction: fetchCentralChapters,
  fetchCentralSectionsFunction: fetchCentralSections
};

export default compose(
  withRouter,
  connect(mapStateToProps, mapDispatchToProps),
  injectT('editor.containers.CentralHandbookEditorPage')
)(CentralHandbookEditorPage);
