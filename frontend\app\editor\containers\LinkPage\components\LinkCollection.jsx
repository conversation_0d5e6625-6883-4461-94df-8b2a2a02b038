// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { Title, Icon, Card, Addons, Field, Button } from 'kf-bui';
import { arrayMove, SortableContainer, SortableElement } from 'react-sortable-hoc';
import styled from 'styled-components';
import injectT from '../../../../common/i18n';
import type { LinkCollectionType, LinkType } from '../../../../types';
import Link from './Link';
import LinkCollectionForm from './LinkCollectionForm';
import LinkForm from './LinkForm';
import { persistLinkCollection } from '../api';

const GrowingDiv = styled.div`
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
`;

/* eslint-disable react/no-array-index-key */
const SortableList = SortableContainer(({ items }) => (
  <div>
    {items.map((item, index) => (
      <SortableItem key={index} index={index} item={item} />
    ))}
  </div>
));

const SortableItem = SortableElement(({ item }) => (
  <Card>
    <Card.Content as={Field} style={{ padding: '1rem', display: 'flex' }}>
      <Icon icon="bars" fw /> {item}
    </Card.Content>
  </Card>
));

/* eslint-disable react/no-array-index-key */
const NonSortableList = ({ items }) => (
  <div>
    {items.map((item, index) => (
      <NonSortableItem key={index} index={index} item={item} />
    ))}
  </div>
);

const NonSortableItem = ({ item }) => (
  <Card>
    <Card.Content as={Field} style={{ padding: '1rem', display: 'flex' }}>
      {item}
    </Card.Content>
  </Card>
);

type Props = {
  // Her burde vi i steden ta imot: linkCollection: LinkCollectionType
  ...LinkCollectionType,
  onSaveLink: (LinkType, string) => void,
  onDeleteLink: string => void,
  onDeleteLinkCollection: string => void,
  refreshLinkCollections: () => void,
  t: string => string
};
type State = {
  links: LinkType[],
  isSorting: boolean,
  isEditing: boolean
};

class LinkCollection extends React.Component<Props, State> {
  state = {
    links: this.props.links.sort((a, b) => a.sortOrder - b.sortOrder),
    isSorting: false,
    isEditing: false
  };

  componentWillReceiveProps(nextProps: Props) {
    const { title, links } = this.props;
    if (nextProps.links !== links) {
      this.setState({
        links: nextProps.links.sort((a, b) => a.sortOrder - b.sortOrder)
      });
    }
    if (nextProps.title !== title) {
      this.setState({ isEditing: false });
    }
  }

  onSortEnd = ({ oldIndex, newIndex }: { oldIndex: number, newIndex: number }) => {
    this.setState(prevState => ({
      links: arrayMove(prevState.links, oldIndex, newIndex)
    }));
  };

  toggleEditing = () => {
    this.setState(state => ({ isEditing: !state.isEditing }));
  };

  saveLinkCollection = (linkCollection?: LinkCollectionType) => {
    const { id, handbookId, sortOrder } = this.props;
    const linksWithoutSortOrder = this.state.links;
    const links = linksWithoutSortOrder.map((link, index) => ({
      ...link,
      sortOrder: index
    }));
    const newLinkCollection = {
      id,
      title: (linkCollection && linkCollection.title) || this.props.title,
      handbookId,
      sortOrder,
      links
    };
    persistLinkCollection(newLinkCollection).then(() => this.props.refreshLinkCollections());
  };

  toggleSorting = (reset?: boolean) => {
    const { isSorting } = this.state;

    if (isSorting) {
      if (reset === true) {
        this.setState({
          links: this.props.links.sort((a, b) => a.sortOrder - b.sortOrder),
          isSorting: false
        });
      } else {
        this.saveLinkCollection();
        this.setState({ isSorting: false });
      }
    } else {
      this.setState({ isSorting: true });
    }
  };

  render() {
    const {
      title,
      id,
      onSaveLink,
      onDeleteLink,
      onDeleteLinkCollection,
      handbookId,
      sortOrder,
      t
    } = this.props;
    const { links, isSorting, isEditing } = this.state;

    const LinkList = isSorting ? (
      <SortableList
        onSortEnd={this.onSortEnd}
        items={links.map(props => (
          <Link
            key={props.id}
            {...props}
            onDelete={onDeleteLink}
            onSave={link => onSaveLink(link, id || '')}
          />
        ))}
      />
    ) : (
      <NonSortableList
        items={links.map(props => (
          <Link
            key={props.id}
            {...props}
            onDelete={onDeleteLink}
            onSave={link => onSaveLink(link, id || '')}
          />
        ))}
      />
    );

    return (
      <Card>
        <Card.Header>
          <GrowingDiv style={{ margin: '10px' }}>
            {isEditing ? (
              <LinkCollectionForm
                id={id}
                handbookId={handbookId}
                sortOrder={sortOrder}
                title={title}
                onCancel={this.toggleEditing}
                onSave={this.saveLinkCollection}
              />
            ) : (
              <Title style={{ marginBottom: '0px', flexShrink: '15' }}>{title}</Title>
            )}
            <Addons pullRight>
              {!isEditing && (
                <Button size="small" onClick={this.toggleEditing}>
                  <span>
                    <Icon icon="pencil" /> <span>{t('changeName')}</span>
                  </span>
                </Button>
              )}
              {isSorting ? (
                <Button color="primary" size="small" onClick={this.toggleSorting}>
                  <span>
                    <Icon icon="save" /> <span>{t('saveSorting')}</span>
                  </span>
                </Button>
              ) : (
                <Button color="primary" size="small" onClick={this.toggleSorting}>
                  <span>
                    <Icon icon="sort" /> <span>{t('startSorting')}</span>
                  </span>
                </Button>
              )}
              {isSorting ? (
                <Button color="primary" size="small" onClick={() => this.toggleSorting(true)}>
                  <span>
                    <Icon icon="refresh" /> <span>{t('resetSorting')}</span>
                  </span>
                </Button>
              ) : (
                <Button
                  color="danger"
                  size="small"
                  onClick={() => onDeleteLinkCollection(id || '')}
                >
                  <span>
                    <Icon icon="trash" /> <span>{t('deleteLinkCollection')}</span>
                  </span>
                </Button>
              )}
            </Addons>
          </GrowingDiv>
        </Card.Header>
        <Card.Content>
          {LinkList}
          {links.length === 0 && <span>{t('noLinksInCollection')}</span>}
          <LinkForm sortOrder={links.length} onSave={link => onSaveLink(link, id || '')} />
        </Card.Content>
      </Card>
    );
  }
}

export default injectT('editor.containers.LinkPage.components.LinkCollection')(LinkCollection);
