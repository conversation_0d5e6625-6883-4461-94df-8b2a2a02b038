// Needed for ES2015 generator support
/* eslint import/first: 0 */

import React from 'react';
import { render } from 'react-dom';
import { Provider } from 'react-redux';
import createHistory from 'history/createBrowserHistory';
import { ConnectedRouter } from 'connected-react-router';
import axios from 'axios';

import App from './containers/App';
import ScrollToTop from '../common/components/ScrollToTop';
import configureStore from './store';
import LanguageProvider from '../common/containers/LanguageProvider';
import { selectLocale } from './containers/App/selectors';

/* eslint-disable no-underscore-dangle */

// Get the basename (WebAppPath in .properties) from the HTML
// so we can configure the router correctly
// eslint-disable-next-line
const basename = window.__BASENAME__;
const history = createHistory({ basename });

// Congiure global axios defaults
let baseUrl = basename;
if (process.env.NODE_ENV === 'development') {
  // If we are using the dev server, we want to proxy these calls to the backend
  baseUrl = '/handboker';
}

axios.defaults.baseURL = `${baseUrl}`;
const LanguageProviderWithLocaleSelector = LanguageProvider(selectLocale);

function renderApp(initialState) {
  render(
    <Provider store={configureStore(initialState, history)}>
      <LanguageProviderWithLocaleSelector>
        <ConnectedRouter history={history}>
          <ScrollToTop>
            <App />
          </ScrollToTop>
        </ConnectedRouter>
      </LanguageProviderWithLocaleSelector>
    </Provider>,
    document.getElementById('app')
  );
}

// Grab the session state from a global variable injected into the HTML on the server
// See http://redux.js.org/docs/recipes/ServerRendering.html#inject-initial-component-html-and-state
let session = window.__PRELOADED_SESSION_STATE__;
session = session || '{}';
// The dev-server doesn't inline the session data in the html, so therefore we fetch it
if (process.env.NODE_ENV === 'development') {
  axios.get('session').then(res => renderApp({ global: res.data }));
} else {
  renderApp({ global: JSON.parse(session) });
}

// Allow the passed state to be garbage-collected
delete window.__PRELOADED_SESSION_STATE__;
