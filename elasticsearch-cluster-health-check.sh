#!/bin/bash

# Elasticsearch Cluster Health Investigation Script
# This script investigates the cluster issues identified in the comprehensive analysis

echo "=========================================="
echo "ELASTICSEARCH CLUSTER HEALTH INVESTIGATION"
echo "=========================================="
echo ""

# Configuration - Update these values for your environment
ELASTICSEARCH_HOST="${ELASTICSEARCH_HOST:-localhost}"
ELASTICSEARCH_PORT="${ELASTICSEARCH_PORT:-9200}"
ES_URL="http://${ELASTICSEARCH_HOST}:${ELASTICSEARCH_PORT}"

echo "Connecting to Elasticsearch at: $ES_URL"
echo ""

# Function to make curl requests with error handling
make_request() {
    local endpoint=$1
    local description=$2
    
    echo "🔍 $description"
    echo "Endpoint: $endpoint"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$ES_URL$endpoint")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo "✅ SUCCESS"
        echo "$body" | python -m json.tool 2>/dev/null || echo "$body"
    else
        echo "❌ FAILED (HTTP $http_code)"
        echo "$body"
    fi
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 1. Check cluster health
make_request "/_cluster/health?pretty" "Cluster Health Status"

# 2. Check cluster settings (for read-only blocks)
make_request "/_cluster/settings?pretty" "Cluster Settings (Read-only blocks)"

# 3. Check disk allocation
make_request "/_cat/allocation?v&h=shards,disk.indices,disk.used,disk.avail,disk.total,disk.percent,host,node" "Disk Allocation"

# 4. Check node information
make_request "/_cat/nodes?v&h=name,heap.percent,ram.percent,cpu,load_1m,disk.used_percent,node.role,master" "Node Information"

# 5. Check indices information
make_request "/_cat/indices?v&h=index,health,status,pri,rep,docs.count,store.size" "Indices Information"

# 6. Check for specific index 5438 (from the logs)
make_request "/5438" "Index 5438 Details"

# 7. Check cluster stats
make_request "/_cluster/stats?pretty" "Cluster Statistics"

# 8. Check pending tasks
make_request "/_cluster/pending_tasks?pretty" "Pending Cluster Tasks"

# 9. Check index lifecycle policies
make_request "/_ilm/policy?pretty" "Index Lifecycle Management Policies"

# 10. Check cluster state (master node, routing table)
make_request "/_cluster/state/master_node,routing_table?pretty" "Cluster State (Master & Routing)"

echo "=========================================="
echo "INVESTIGATION COMPLETE"
echo "=========================================="
echo ""
echo "📋 ANALYSIS CHECKLIST:"
echo ""
echo "1. ✅ Cluster Health: Check if status is 'green', 'yellow', or 'red'"
echo "2. ✅ Read-only Blocks: Look for 'cluster.blocks.read_only_allow_delete'"
echo "3. ✅ Disk Space: Check disk.percent - should be < 85%"
echo "4. ✅ Node Health: Check heap.percent, ram.percent, cpu load"
echo "5. ✅ Index 5438: Check if it exists and its health status"
echo "6. ✅ Pending Tasks: Should be empty or minimal"
echo "7. ✅ ILM Policies: Check if any policies might delete indices"
echo ""
echo "🚨 RED FLAGS TO LOOK FOR:"
echo "- Cluster status: 'red' or 'yellow'"
echo "- Disk usage > 85%"
echo "- Read-only blocks enabled"
echo "- High heap/memory usage (> 80%)"
echo "- Many pending tasks"
echo "- Index 5438 missing or unhealthy"
echo "- ILM policies with short retention"
echo ""
echo "💡 NEXT STEPS:"
echo "1. If disk space > 85%: Free up space or add storage"
echo "2. If read-only blocks: Remove with PUT /_cluster/settings"
echo "3. If high memory: Restart nodes or increase heap size"
echo "4. If index missing: Check application logs for creation failures"
echo "5. If ILM issues: Review and adjust lifecycle policies"
echo ""
