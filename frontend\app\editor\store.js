/**
 * Create the store with asynchronously loaded reducers
 */

import { createStore, applyMiddleware, compose } from 'redux';
import { routerMiddleware, connectRouter } from 'connected-react-router';
import createSagaMiddleware from 'redux-saga';
import thunkMiddleware from 'redux-thunk';
import createReducer from './reducers';
import sagas from './sagas';

// Redux devtools https://github.com/zalmoxisus/redux-devtools-extension
const devtools = window.devToolsExtension || (() => noop => noop);

export default function configureStore(initialState = {}, history) {
  const sagaMiddleware = createSagaMiddleware();
  // Create the store with three middlewares
  // 2. sagaMiddleware: Makes redux-sagas work
  // 3. routerMiddleware: Syncs the location/URL path to the state
  const middlewares = [thunkMiddleware, sagaMiddleware, routerMiddleware(history)];

  const enhancers = [applyMiddleware(...middlewares), devtools()];
  const rootReducer = createReducer();
  const connectedReducer = connectRouter(history)(rootReducer);

  const store = createStore(connectedReducer, initialState, compose(...enhancers));

  // Extensions
  store.runSaga = sagaMiddleware.run;
  store.runSaga(sagas);

  return store;
}
