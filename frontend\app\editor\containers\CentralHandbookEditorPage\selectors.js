// @flow

import { createSelector } from 'reselect';
import { sortBy } from '../../../common/util';
import type {
  CentralChapterType,
  CentralEditorHandbooksType,
  CentralHandbookType,
  CentralSectionType,
  ReadingLinkType
} from '../../../types';

function getValues<T>(o: { [string]: T }): T[] {
  return Object.keys(o).map(key => o[key]);
}

const selectCentralHandbookState = (state): { [string]: CentralEditorHandbooksType } =>
  state.centralEditorHandbooks;

/**
 * CentralHandbooks selectors
 */
export const selectCentralHandbookMap = createSelector(
  selectCentralHandbookState,
  centralHandbookState => centralHandbookState.centralHandbooks
);
export const selectCentralHandbooks = createSelector(selectCentralHandbookMap, handbooks =>
  getValues<CentralHandbookType>(handbooks).sort(sortBy('title'))
);
export const makeSelectCentralHandbookById = (id: string) =>
  createSelector(selectCentralHandbookMap, handbooks => handbooks[id]);

/**
 * CentralSections selectors
 */
export const selectCentralSectionsMap = createSelector(
  selectCentralHandbookState,
  centralHandbookState => centralHandbookState.centralSections
);
export const selectCentralSections = createSelector(selectCentralSectionsMap, sections =>
  getValues<CentralSectionType>(sections)
);
export const makeSelectCentralSectionById = (sectionId: string) =>
  createSelector(selectCentralSectionsMap, sections =>
    getValues<CentralSectionType>(sections).find(section => section.id === sectionId)
  );
export const makeSelectHandbookSections = (handbookId: string) =>
  createSelector(selectCentralSectionsMap, sections =>
    getValues<CentralSectionType>(sections).filter(
      section => section.centralHandbookId === handbookId
    )
  );

/**
 * CentralChapters selectors
 */
export const selectCentralChaptersMap = createSelector(
  selectCentralHandbookState,
  centralHandbookState => centralHandbookState.centralChapters
);
export const selectCentralChapters = createSelector(selectCentralChaptersMap, chapters =>
  getValues<CentralChapterType>(chapters)
);
export const makeSelectCentralChapterById = (chapterId: string) =>
  createSelector(selectCentralChapters, chapters =>
    chapters.find(chapter => {
      return chapter.id === chapterId;
    })
  );

export const makeSelectHandbookDescendants = (handbookId: string) =>
  createSelector(selectCentralChapters, selectCentralSections, (chapters, sections) => {
    const chapterDescendants = chapters.filter(chap => chap.centralHandbookId === handbookId);
    const sectionDescendants = sections.filter(sec => sec.centralHandbookId === handbookId);
    return [...chapterDescendants, ...sectionDescendants];
  });

export const makeSelectHandbookChildren = (handbookId: string) =>
  createSelector(selectCentralChaptersMap, chapters =>
    getValues<CentralChapterType>(chapters).filter(
      chapter => chapter.centralHandbookId === handbookId && chapter.parentId === undefined
    )
  );
export const makeSelectChapterChildren = (chapterId: string) =>
  createSelector([selectCentralChapters, selectCentralSections], (chapters, sections) => {
    const subChapters = chapters.filter(chapter => chapter.parentId === chapterId);
    const subSections = sections.filter(section => section.parentId === chapterId);

    return [...subChapters, ...subSections];
  });

/**
 * ReadingLinks selectors
 */
export const selectCentralReadingLinksMap = createSelector(
  selectCentralHandbookState,
  centralHandbookState => centralHandbookState.readingLinks
);

export const makeSelectReadingLinks = createSelector(selectCentralReadingLinksMap, readingLinks =>
  getValues<ReadingLinkType>(readingLinks)
);

export const makeSelectReadingLinksById = (handbookId: string) =>
  createSelector([makeSelectReadingLinks, selectCentralSections], (readingLinks, sections) => {
    const handbookSections = sections.filter(section => section.centralHandbookId === handbookId);
    return readingLinks.filter(link =>
      handbookSections.find(section => section.id === link.centralSectionId)
    );
  });

export const makeSelectReadingLinksBySectionId = (sectionId: string) =>
  createSelector([makeSelectReadingLinks, selectCentralSections], (readingLinks, sections) => {
    const handbookSections = sections.filter(section => section.id === sectionId);
    return readingLinks.filter(link =>
      handbookSections.find(section => section.id === link.centralSectionId)
    );
  });

export const makeSelectReadingLinksByChapterId = (chapterId: string) =>
  createSelector(
    [makeSelectReadingLinks, selectCentralSections, selectCentralChapters],
    (readingLinks, sections, chapters) => {
      const chaptersInLocalTree: CentralChapterType[] = [];
      const recAddChapters = (thisChapterId: string) => {
        const chapter = chapters.find(c => c.id === thisChapterId);
        if (chapter) chaptersInLocalTree.push(chapter);
        const children = chapters.filter(c => c.parentId === thisChapterId);
        children.forEach(c => recAddChapters(c.id));
      };
      recAddChapters(chapterId);
      const sectionsInLocalTree = sections.filter(s =>
        chaptersInLocalTree.find(c => c.id === s.parentId)
      );
      return readingLinks.filter(link =>
        sectionsInLocalTree.find(s => s.id === link.centralSectionId)
      );
    }
  );
