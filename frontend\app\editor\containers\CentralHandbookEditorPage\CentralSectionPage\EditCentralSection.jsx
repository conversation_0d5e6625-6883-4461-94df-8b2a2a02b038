// @flow
import React from 'react';
import { connect } from 'react-redux';
import queryString from 'query-string';
import CentralSectionHooksForm from './CentralSectionHooksForm';

import { makeSelectCentralSectionById } from '../selectors';
import { saveCentralSection } from '../actions';

import type { CentralSectionType } from '../../../../types';
import { selectSpellcheckUrl } from '../../App/selectors';

type Props = {
  match: {
    url: string,
    params: {
      handbookId: string,
      sectionId: ?string
    }
  },
  location: {
    search: string
  },
  history: {
    push: (path: string) => void,
    goBack: () => void
  },
  centralSection: ?CentralSectionType,
  // eslint-disable-next-line react/no-unused-prop-types
  saveCentralSectionFunction: ({}) => void,
  spellCheckUrl: string
};

const EditCentralSection = ({
  centralSection,
  history,
  location,
  match,
  saveCentralSectionFunction,
  spellCheckUrl
}: Props) => {
  if (match.params.sectionId && !centralSection) {
    return <div />;
  }

  const parentId = queryString.parse(location.search).parent;

  const onSubmit = values => {
    const { title, html } = values;

    if (centralSection) {
      const section = {
        ...centralSection,
        title,
        html
      };
      saveCentralSectionFunction(section);
    } else {
      const section = {
        centralHandbookId: match.params.handbookId,
        title,
        html,
        parentId
      };
      saveCentralSectionFunction(section);
    }
    history.goBack();
  };

  const getAbortLink = section => {
    if (section) {
      return `/central-editor/${section.centralHandbookId}/section/${section.id}/`;
      // If we are adding a new child chapter, take us to the parent on abort
    }
    return `/central-editor/${match.params.handbookId}/chapter/${parentId}`;
  };

  return (
    <CentralSectionHooksForm
      centralSection={centralSection}
      onSubmit={onSubmit}
      abortLink={getAbortLink(centralSection)}
      spellCheckUrl={spellCheckUrl}
    />
  );
};

const mapStateToProps = (state, ownProps) => {
  const { sectionId } = ownProps.match.params;
  const selectCentralSection = makeSelectCentralSectionById(sectionId);

  return {
    centralSection: selectCentralSection(state),
    spellCheckUrl: selectSpellcheckUrl(state)
  };
};

export default connect(mapStateToProps, {
  saveCentralSectionFunction: saveCentralSection
})(EditCentralSection);
