/* eslint-env jest */
import reducer, { initialState } from '../reducer';
import { selectElement } from '../actions';
import { setOrganizationSuccess } from '../../App/actions';

describe('uiReducer', () => {
  let state;

  // This gives us some state to work with. Most of the tests needs data in the store.
  beforeEach(() => {
    state = initialState;
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should return the initial state when changing the external org', () => {
    const action = setOrganizationSuccess();
    expect(reducer(undefined, action)).toEqual(initialState);
  });

  it('should handle the select node success', () => {
    const handbook = { id: 'book1', title: 'HMS' };

    const action = selectElement(handbook);

    const expectedResult = {
      ...state,
      selected: handbook
    };

    expect(reducer(initialState, action)).toEqual(expectedResult);
  });
});
