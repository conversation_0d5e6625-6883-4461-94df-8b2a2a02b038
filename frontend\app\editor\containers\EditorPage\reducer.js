import { handleActions, combineActions } from 'redux-actions';
import { clone, omit, omitBy, merge } from 'lodash';

import * as actions from './actions';
import { setOrganizationSuccess } from '../App/actions';

export const initialState = {
  handbooks: {},
  chapters: {},
  sections: {},
  subscriptions: new Set(),
  localEditorsForOrg: new Set(),
  localEditors: new Set()
};

/*
 * Using merge for sections, to fix issue where fetching of the text would finish
 * before fetching handbook content. This would cause the hand book content (without section texts)
 * to overwrite previously fetched section texts
 */

export default handleActions(
  {
    [setOrganizationSuccess]: () => initialState,

    [actions.fetchLocalHandbooksSuccess]: (state, { payload }) => ({
      ...state,
      handbooks: payload.handbooks.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {}),
      chapters: payload.chapters.reduce((acc, cur) => {
        acc[cur.id] = { ...cur, type: 'LOCAL_CHAPTER' };
        return acc;
      }, {}),
      sections: merge(
        {},
        state.sections,
        payload.sections.reduce((acc, cur) => {
          acc[cur.id] = { ...cur, type: 'LOCAL_SECTION' };
          return acc;
        }, {})
      )
    }),

    [actions.fetchSubscriptionsSuccess]: (state, { payload }) => ({
      ...state,
      subscriptions: payload
    }),

    [actions.fetchLocalEditorsForOrganizationSuccess]: (state, { payload }) => ({
      ...state,
      localEditorsForOrg: payload
    }),

    [actions.fetchLocalEditorsSuccess]: (state, { payload }) => ({
      ...state,
      localEditors: payload
    }),

    [actions.deleteLocalEditorSuccess]: (state, { payload: localEditor }) => {
      const localEditors = clone(state.localEditors);
      localEditors.delete(localEditor);
      return {
        ...state,
        localEditors
      };
    },

    [actions.saveLocalEditorSuccess]: (state, { payload }) => {
      const localEditors = clone(state.localEditors);
      localEditors.add(payload);
      return {
        ...state,
        localEditors
      };
    },

    [actions.deleteLocalHandbookSuccess]: (state, { payload: handbookId }) => ({
      ...state,
      handbooks: omit(state.handbooks, [handbookId]),
      chapters: omitBy(state.chapters, chapter => chapter.handbookId === handbookId),
      sections: omitBy(state.sections, section => section.handbookId === handbookId)
    }),

    [combineActions(actions.saveLocalHandbookSuccess, actions.fetchHandbookSuccess)]: (
      state,
      { payload: handbook }
    ) => ({
      ...state,
      handbooks: {
        ...state.handbooks,
        [handbook.id]: handbook
      }
    }),

    // Note, since we don't want to recursively delete in the frontend,
    // we simply refetch from the backend
    // [actions.deleteLocalChapterSuccess]: state => state,

    [combineActions(actions.saveLocalChapterSuccess, actions.fetchChapterSuccess)]: (
      state,
      { payload: chapter }
    ) => ({
      ...state,
      chapters: {
        ...state.chapters,
        [chapter.id]: { ...chapter, type: 'LOCAL_CHAPTER' }
      }
    }),

    [combineActions(actions.saveLocalSectionSuccess, actions.fetchSectionSuccess)]: (
      state,
      { payload: section }
    ) => ({
      ...state,
      sections: merge({}, state.sections, { [section.id]: { ...section, type: 'LOCAL_SECTION' } })
    }),

    [actions.deleteLocalSectionSuccess]: (state, { payload: sectionId }) => ({
      ...state,
      sections: omit(state.sections, [sectionId])
    }),

    [actions.subscribeSuccess]: (state, { payload }) => {
      const subscriptions = clone(state.subscriptions);
      if (payload.bool) {
        subscriptions.add(payload.handbookId);
      } else {
        subscriptions.delete(payload.handbookId);
      }

      return {
        ...state,
        subscriptions
      };
    }
  },
  initialState
);
