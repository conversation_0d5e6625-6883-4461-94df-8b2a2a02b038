import { createSelector } from 'reselect';

export const selectSession = state => state.global;

export const selectExternalOrganizationId = createSelector(selectSession, state =>
  state.organization ? state.organization.id : null
);

export const selectIsKfAdmin = createSelector(selectSession, state => Boolean(state.isKfAdmin));

export const selectPublicBasename = createSelector(selectSession, state => state.publicBasename);

export const selectSpellcheckUrl = createSelector(selectSession, state => state.spellCheckUrl);

export const selectLogoUrl = createSelector(
  selectSession,
  selectExternalOrganizationId,
  (state, externalOrgId) => (externalOrgId ? `${state.logoUrlStart}${externalOrgId}` : null)
);

export const selectLocale = createSelector(
  selectSession,
  state => state.user.language || (state.organization && state.organization.language) || 'nb'
);

export const selectBannerUrl = createSelector(selectSession, state => state.bannerUrl);

export const selectSelectedCentralItem = createSelector(
  selectSession,
  state => state.selectedCentralItem
);
