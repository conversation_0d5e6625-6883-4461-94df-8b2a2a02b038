// Jest <PERSON>nap<PERSON> v1, https://goo.gl/fbAQLP

exports[`centralHandbookReducer should handle fetch handbook content success 1`] = `
Object {
  "chapters": Object {
    "1": Array [
      Object {
        "id": "1.1",
        "title": "Arbeidsgiverpolitikk",
        "type": "LOCAL_CHAPTER",
      },
      Object {
        "id": "1.2",
        "title": "Permisjon",
        "type": "LOCAL_CHAPTER",
      },
    ],
  },
  "handbooks": Array [],
  "sections": Object {
    "1": Array [
      Object {
        "id": "1.1.1",
        "title": "Varsling",
        "type": "LOCAL_SECTION",
      },
      Object {
        "id": "1.1.2",
        "title": "Etiske retningslinjer",
        "type": "LOCAL_SECTION",
      },
    ],
  },
  "selectedHandbook": null,
}
`;

exports[`centralHandbookReducer should handle fetch handbook content success 2`] = `
Object {
  "chapters": Object {
    "1": Array [
      Object {
        "id": "1.1",
        "title": "Arbeidsgiverpolitikk",
        "type": "LOCAL_CHAPTER",
      },
      Object {
        "id": "1.2",
        "title": "Permisjon",
        "type": "LOCAL_CHAPTER",
      },
    ],
    "2": Array [
      Object {
        "id": "1.1",
        "title": "Arbeidsgiverpolitikk",
        "type": "LOCAL_CHAPTER",
      },
      Object {
        "id": "1.2",
        "title": "Permisjon",
        "type": "LOCAL_CHAPTER",
      },
    ],
  },
  "handbooks": Array [],
  "sections": Object {
    "1": Array [
      Object {
        "id": "1.1.1",
        "title": "Varsling",
        "type": "LOCAL_SECTION",
      },
      Object {
        "id": "1.1.2",
        "title": "Etiske retningslinjer",
        "type": "LOCAL_SECTION",
      },
    ],
    "2": Array [
      Object {
        "id": "1.1.1",
        "title": "Varsling",
        "type": "LOCAL_SECTION",
      },
      Object {
        "id": "1.1.2",
        "title": "Etiske retningslinjer",
        "type": "LOCAL_SECTION",
      },
    ],
  },
  "selectedHandbook": null,
}
`;

exports[`centralHandbookReducer should handle fetch handbooks success 1`] = `
Object {
  "chapters": Object {},
  "handbooks": Array [
    Object {
      "id": "1",
      "title": "HMS",
    },
    Object {
      "id": "2",
      "title": "Avvik",
    },
  ],
  "sections": Object {},
  "selectedHandbook": null,
}
`;
