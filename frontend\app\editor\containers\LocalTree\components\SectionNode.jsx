import React from 'react';
import { Icon, Tree } from 'kf-bui';
import { SectionShape } from '../../../shapes';

const SectionNode = ({ section }) => (
  <Tree.ItemLink to={`/editor/${section.handbookId}/section/${section.id}/`} id={section.id}>
    <Icon icon="file-text-o" size="small" /> {section.title}
  </Tree.ItemLink>
);

SectionNode.propTypes = {
  section: SectionShape.isRequired
};

export default SectionNode;
