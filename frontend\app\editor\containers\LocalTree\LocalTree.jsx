import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router';

import { Card, Tree } from 'kf-bui';

import injectT from '../../../common/i18n';
import HandbookNode from './components/HandbookNode';
import { HandbookShape } from '../../shapes';
import { selectHandbooks } from '../EditorPage/selectors';

/**
 * We need to pass location down on every change
 * Otherwise we are unable to highligh the correct active node
 * because redux implements shouldComponentUpdate
 */

class LocalTree extends React.PureComponent {
  renderTree() {
    const { handbooks, location } = this.props;

    const items = handbooks.map(handbook => (
      <HandbookNode handbook={handbook} key={handbook.id} location={location} />
    ));

    return <Tree>{items}</Tree>;
  }

  render() {
    const { t } = this.props;
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t('treeHeader')}</Card.Title>
        </Card.Header>
        <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>{this.renderTree()}</Card.Content>
      </Card>
    );
  }
}

LocalTree.propTypes = {
  handbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  t: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired
};

const mapStateToProps = state => ({
  handbooks: selectHandbooks(state)
});

export default compose(
  injectT('editor.containers.LocalTree'),
  withRouter,
  connect(mapStateToProps)
)(LocalTree);
