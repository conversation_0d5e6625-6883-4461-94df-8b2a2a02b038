import React from 'react';
import PropTypes from 'prop-types';

import { Icon, Tree } from 'kf-bui';

import { SectionShape } from '../../../common/shapes';

const SectionItem = ({ section, onSectionClick }) => (
  <Tree.Item onClick={() => onSectionClick(section)} id={section.id}>
    <Icon icon="file-text-o" /> {section.title}
  </Tree.Item>
);

SectionItem.propTypes = {
  section: SectionShape.isRequired,
  onSectionClick: PropTypes.func.isRequired
};

export default SectionItem;
