/* eslint-env jest */
import {
  selectHandbooks,
  selectChapterById,
  selectSectionById,
  selectRootChapters,
  selectSubChaptersAndSections,
  selectPendingCount
} from '../selectors';

const state = {
  handbooks: {
    handbooks: {
      HMT: {
        id: 'HMT',
        title: 'KF HMT-Håndbok',
        pendingChange: true
      },
      HMS: {
        id: 'HMS',
        title: 'KF HMS-Håndbok'
      }
    },
    chapters: {
      hmtChap1: {
        id: 'hmtChap1',
        title: 'Avvik',
        handbookId: 'HMT',
        sortOrder: 1,
        pendingChange: true
      },
      hmtChap3: {
        id: 'hmtChap3',
        title: 'Risiko',
        handbookId: 'HMT',
        sortOrder: 2,
        parentId: 'hmtChap6',
        type: 'LOCAL_CHAPTER'
      },
      hmtChap5: {
        id: 'hmtChap5',
        title: 'Brannvern',
        handbookId: 'HMT',
        sortOrder: 3
      },
      hmtChap6: {
        id: 'hmtChap6',
        type: 'LOCAL_CHAPTER',
        title: 'Rutiner',
        handbookId: 'HMT',
        sortOrder: 4,
        parentId: 'hmtChap5'
      }
    },
    sections: {
      hmtDoc2: {
        id: 'hmtDoc2',
        title: 'Avvik skjema',
        handbookId: 'HMT',
        sortOrder: 5
      },
      hmtDoc4: {
        id: 'hmtDoc4',
        title: 'Skjema for Risikoanalyse',
        handbookId: 'HMT',
        sortOrder: 6,
        parentId: 'hmtChap3',
        pendingTextChange: true
      },
      hmtDoc7: {
        id: 'hmtDoc7',
        title: 'Rutiner for barnehager',
        handbookId: 'HMT',
        sortOrder: 7,
        parentId: 'hmtChap6'
      },
      hmtDoc8: {
        id: 'hmtDoc8',
        title: 'Rutiner for Skoler',
        handbookId: 'HMT',
        sortOrder: 8,
        parentId: 'hmtChap6'
      },
      hmtDoc9: {
        id: 'hmtDoc9',
        title: 'Rutiner for kontor',
        handbookId: 'HMT',
        sortOrder: 9,
        parentId: 'hmtChap6'
      }
    }
  }
};

describe('Handbooks selectors', () => {
  describe('selectHandbooks', () => {
    it('selects the handbooks, ordered by title', () => {
      const selection = selectHandbooks(state);
      expect(selection).toMatchSnapshot();
    });
  });

  describe('selectChapter', () => {
    it('selects a chapter', () => {
      const selection = selectChapterById('hmtChap5')(state);
      expect(selection).toMatchSnapshot();
    });
  });

  describe('selectSection', () => {
    it('selects a section', () => {
      const selection = selectSectionById('hmtDoc2')(state);
      expect(selection).toMatchSnapshot();
    });
  });

  describe('selectRootChapters', () => {
    it('selects the root chapters', () => {
      const selection = selectRootChapters('HMT')(state);
      expect(selection).toMatchSnapshot();
    });
  });

  describe('selectSubChaptersAndSections', () => {
    it('selects the sub chapters and sections and applies property to chapters so we can differentiate them from sections', () => {
      const parentId = 'hmtChap6';
      const selection = selectSubChaptersAndSections(parentId)(state);
      expect(selection).toMatchSnapshot();
    });
  });

  describe('selectPendingCount', () => {
    it('selects the total number of pending changes', () => {
      const result = selectPendingCount(state);
      expect(result).toEqual(3);
    });
  });
});
