import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';
import ClickableChapterNode from './ClickableChapterNode';
import { HandbookShape, ChapterShape, SectionShape } from '../../../../shapes';
import { selectRootChapters } from '../../../EditorPage/selectors';

const ClickableHandbookNode = ({
  handbook,
  chapters,
  selectElement,
  activeChapter,
  activeSection
}) => {
  const items = chapters.map(child => (
    <ClickableChapterNode
      key={child.id}
      chapter={child}
      selectElement={selectElement}
      activeChapter={activeChapter}
      activeSection={activeSection}
    />
  ));

  let disabled = false;

  if (activeSection) {
    // If we are moving a section, a handbook can never be the target
    disabled = true;
  } else if (activeChapter && !activeChapter.parentId && activeChapter.handbookId === handbook.id) {
    // If we are moving a chapter
    // disable this handbook if it is the parent of the chapter we are moving
    disabled = true;
  }

  return (
    <Tree.Item
      id={handbook.id}
      disabled={disabled}
      items={items}
      // active={!(activeSection || activeChapter) && activeHandbook === handbook}
      key={handbook.id}
      onClick={() => selectElement(handbook)}
    >
      <Icon icon="book" size="small" /> {handbook.title}
    </Tree.Item>
  );
};

ClickableHandbookNode.propTypes = {
  handbook: HandbookShape.isRequired,
  chapters: PropTypes.arrayOf(ChapterShape).isRequired,
  selectElement: PropTypes.func.isRequired,
  // Used to determining which node to display as active
  activeChapter: ChapterShape,
  activeSection: SectionShape
};

ClickableHandbookNode.defaultProps = {
  activeChapter: null,
  activeSection: null
};

const makeMapStateToProps = (_, ownProps) => {
  const selectRootChaptersSelector = selectRootChapters(ownProps.handbook.id);

  const mapStateToProps = state => ({
    chapters: selectRootChaptersSelector(state)
  });

  return mapStateToProps;
};

export default connect(makeMapStateToProps)(ClickableHandbookNode);
