import axios from 'axios';

const BASE_URL = '/session';

function init() {
  return axios.get(BASE_URL).then(res => res.data);
}

function setExternalOrganization(externalId) {
  return axios.post(`${BASE_URL}/${externalId}`).then(res => res.data);
}

function getLocalEditorsForOrganization(externalId) {
  return axios.get(`${BASE_URL}/editors/${externalId}`).then(res => new Set(res.data));
}

export { init, setExternalOrganization, getLocalEditorsForOrganization };
