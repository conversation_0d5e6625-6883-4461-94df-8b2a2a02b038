import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Link } from 'react-router-dom';
import { Button, Icon, Title, Control, Columns, Column, Group } from 'kf-bui';
import Wysiwyg from '../../../components/Wysiwyg';

import injectT from '../../../../common/i18n';
import Metadata from '../../../components/Metadata';
import DeleteButton from '../../../components/DeleteButton';
import { selectSectionEntities } from '../../EditorPage/selectors';
import { SectionShape } from '../../../shapes';
import { fetchSection, deleteLocalSection, saveLocalSection } from '../../EditorPage/actions';
import PendingChangeWarning from '../../../components/PendingChangeWarning';
import { selectSpellcheckUrl } from '../../App/selectors';
import OldVersionView from './OldVersionView';
import Attachments from '../../../components/Attachments';
import { Attachment } from '../../../components/Attachments/icons';
import { fetchHandbookFileCount } from '../../../components/Attachments/api';

class SectionSelection extends React.Component {
  state = {
    showAttachments: false,
    attachmentCount: 0
  };

  async componentDidMount() {
    const { match, fetchSectionFun } = this.props;
    fetchSectionFun(match.params.sectionId);
    await this.handleAttachmentCount();
  }

  componentWillReceiveProps(nextProps) {
    const { match, fetchSectionFun } = this.props;
    if (match.params.sectionId !== nextProps.match.params.sectionId) {
      fetchSectionFun(nextProps.match.params.sectionId);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.match.params.sectionId !== this.props.match.params.sectionId) {
      this.handleAttachmentCount();
    }
  }

  handleAttachmentCount = async () => {
    try {
      const { match } = this.props;
      const attachmentCount = await fetchHandbookFileCount('section', match.params.sectionId);
      this.setState({ attachmentCount: attachmentCount.count });
    } catch (e) {
      console.error(e);
    }
  };

  render() {
    const { sections, match, history, deleteLocalSectionFun, saveLocalSectionFun, t } = this.props;
    const section = sections[match.params.sectionId];

    if (!section) return null;

    return (
      <div>
        {this.state.showAttachments && (
          <Attachments
            sectionId={section.id}
            sectionType="SECTION"
            onClose={() => this.setState({ showAttachments: false })}
            handleAttachmentCount={this.handleAttachmentCount}
          />
        )}
        <Columns>
          <Column>
            <Title>
              <Icon icon="file-text-o" size="small" style={{ marginRight: '1rem' }} />
              <span>{section.title}</span>
              {section.pendingTitleChange && (
                <i className="fa fa-exclamation-triangle pendingChange" aria-hidden="true" />
              )}
            </Title>
          </Column>
        </Columns>
        <Columns>
          <Column>
            <Group>
              <Button
                control
                as={Link}
                to={`/editor/${section.handbookId}/section/${section.id}/edit/`}
                size="small"
              >
                <Icon icon="pencil" size="small" />
                <span>{t('editButton')}</span>
              </Button>
              <Button
                control
                onClick={() => this.setState({ showAttachments: true })}
                size="small"
                className="add-attachment-btn"
              >
                <Attachment />
                <span>{t('addAttachment')}</span>
                <span className="attachment-count">{this.state.attachmentCount}</span>
              </Button>
            </Group>
          </Column>
          <Column narrow>
            <Group>
              <Control>
                <PendingChangeWarning
                  element={section}
                  mergeLink={`/merge/section/${section.id}/`}
                  onDelete={keep => {
                    if (keep) {
                      saveLocalSectionFun({ section: { ...section, pendingDeletion: false } });
                      return;
                    }
                    deleteLocalSectionFun(section);
                    history.replace(`/editor/${section.handbookId}/chapter/${section.parentId}`);
                  }}
                  deleteText={{
                    buttonText: 'Avsnitt slettet sentralt',
                    title: 'Håndter sentral sletting',
                    text:
                      'Dette avsnittet er blitt slettet sentralt, ønsker du å slette det lokale også?'
                  }}
                />
              </Control>
              <Button
                control
                as={Link}
                to={`/editor/${section.handbookId}/section/${section.id}/move/`}
                size="small"
              >
                {t('moveButton')}
              </Button>

              {!section.pendingDeletion ? (
                <DeleteButton
                  toDelete={{ ...section }}
                  onDelete={() => {
                    deleteLocalSectionFun(section);
                  }}
                  t={t}
                />
              ) : null}
            </Group>
          </Column>
        </Columns>
        <Columns>
          <Column>
            <hr />
            <Metadata element={section} />
            <hr />
          </Column>
        </Columns>
        <label htmlFor="section-text" style={{ fontWeight: 'bold', fontSize: 'large' }}>
          Avsnittstekst
        </label>
        {section.pendingTextChange && (
          <i className="fa fa-exclamation-triangle pendingChange" aria-hidden="true" />
        )}
        <Wysiwyg
          id="section-text"
          spellCheckerRpcUrl={this.props.spellCheckUrl}
          value={section.text}
          disabled
        />
        <hr />
        <OldVersionView section={section} spellCheckUrl={this.props.spellCheckUrl} />
      </div>
    );
  }
}

SectionSelection.propTypes = {
  sections: PropTypes.objectOf(SectionShape).isRequired,
  fetchSectionFun: PropTypes.func.isRequired,
  deleteLocalSectionFun: PropTypes.func.isRequired,
  saveLocalSectionFun: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  spellCheckUrl: PropTypes.string,
  match: PropTypes.shape({
    url: PropTypes.string.isRequired,
    params: PropTypes.shape({
      sectionId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired,
  history: PropTypes.shape({
    replace: PropTypes.func
  }).isRequired
};

SectionSelection.defaultProps = {
  spellCheckUrl: ''
};

export default compose(
  injectT('editor.containers.SectionSelection'),
  connect(
    state => ({
      sections: selectSectionEntities(state),
      spellCheckUrl: selectSpellcheckUrl(state)
    }),
    {
      fetchSectionFun: fetchSection,
      deleteLocalSectionFun: deleteLocalSection,
      saveLocalSectionFun: saveLocalSection
    }
  )
)(SectionSelection);
