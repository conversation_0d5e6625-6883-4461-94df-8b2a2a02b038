# Soft Delete Implementation for Welcome Page Versions

## Overview

Implemented a soft delete mechanism for Welcome Page archived versions to preserve audit trails and enable data recovery while solving the unique constraint violation issue during publishing.

## Problem Solved

**Original Issue**: When publishing a second draft, the system failed with:
```
Cannot insert duplicate key in object 'dbo.welcome_page_version'. 
The duplicate key value is (handbook_id, ARCHIVED).
```

**Root Cause**: The database has a `UNIQUE (handbook_id, status)` constraint. When publishing:
1. System tries to archive current published version
2. Fails because an archived version already exists
3. Cannot proceed to promote draft to published

## Solution: Soft Delete Pattern

### Database Schema Changes

#### 1. Added Soft Delete Columns
```sql
-- PostgreSQL (migrate-db-26.sql & migrate-db-27.sql)
ALTER TABLE welcome_page_version ADD COLUMN deleted SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE welcome_page_version ADD COLUMN deleted_date BIGINT;

-- SQL Server (migrate-db-26-mssql.sql & migrate-db-27-mssql.sql)  
ALTER TABLE welcome_page_version ADD deleted SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE welcome_page_version ADD deleted_date BIGINT NULL;
```

#### 2. Updated Unique Constraint
```sql
-- PostgreSQL
ALTER TABLE welcome_page_version DROP CONSTRAINT welcome_page_version_handbook_id_status_key;
CREATE UNIQUE INDEX welcome_page_version_handbook_status_active 
ON welcome_page_version (handbook_id, status) 
WHERE deleted = 0;

-- SQL Server
ALTER TABLE welcome_page_version DROP CONSTRAINT uq_welcome_page_version_handbook_status;
CREATE UNIQUE INDEX ix_welcome_page_version_handbook_status_active 
ON welcome_page_version (handbook_id, status) 
WHERE deleted = 0;
```

### Repository Implementation Changes

#### 1. Publishing Logic (WelcomePageRepository.scala)
```scala
// BEFORE: Hard delete existing archived versions
val deleteArchivedSql = "DELETE FROM welcome_page_version WHERE handbook_id = ? AND status = ?"

// AFTER: Soft delete existing archived versions  
val timestamp = org.joda.time.DateTime.now().getMillis
val softDeleteArchivedSql = "UPDATE welcome_page_version SET deleted = 1, deleted_date = ? WHERE handbook_id = ? AND status = ? AND deleted = 0"
```

#### 2. Query Updates
All queries now exclude soft-deleted records:
```scala
// BEFORE
"SELECT * FROM welcome_page_version WHERE handbook_id = ? AND status = ?"

// AFTER  
"SELECT * FROM welcome_page_version WHERE handbook_id = ? AND status = ? AND deleted = 0"
```

#### 3. New Audit Methods
```scala
// Find all archived versions (including soft-deleted for audit)
def findAllArchivedVersions(handbookId: String): List[WelcomePageVersion]

// Find only active archived versions
def findActiveArchivedVersions(handbookId: String): List[WelcomePageVersion]
```

## Benefits

### 1. **Audit Trail Preservation**
- Soft-deleted archived versions remain in database
- Full history of all published versions maintained
- `deleted_date` tracks when versions were soft-deleted

### 2. **Data Recovery**
- Accidentally archived versions can be recovered
- Database administrators can restore soft-deleted data
- No permanent data loss

### 3. **Constraint Compliance**
- Unique constraint only applies to active records (`deleted = 0`)
- Multiple archived versions can exist (only one active)
- Publishing workflow works seamlessly

### 4. **Backward Compatibility**
- Application logic unchanged (WelcomePageVersion model unchanged)
- Soft delete handled transparently by repository layer
- All existing tests pass without modification

## Publishing Workflow

### Before (Failed)
1. Draft exists, Published exists, Archived exists
2. Try to archive Published → **FAILS** (constraint violation)
3. Cannot promote Draft to Published

### After (Success)
1. Draft exists, Published exists, Archived exists  
2. Soft delete existing Archived (`deleted = 1`)
3. Archive Published (new active Archived)
4. Promote Draft to Published ✅

## Data Management

### Active Records Query Pattern
```sql
-- All application queries exclude soft-deleted records
WHERE deleted = 0
```

### Audit/Recovery Query Pattern  
```sql
-- Admin queries can include soft-deleted records
WHERE deleted = 1  -- Only soft-deleted
-- OR no deleted filter for all records
```

### Cleanup Strategy (Future)
```sql
-- Permanent cleanup of old soft-deleted records (optional)
DELETE FROM welcome_page_version 
WHERE deleted = 1 AND deleted_date < ?  -- older than X months
```

## Migration Path

### For New Installations
- Run migrations 26, 27, and 28 in sequence
- Migration 26: Creates welcome page tables
- Migration 27: Adds collection audit fields
- Migration 28: Adds soft delete support

### For Existing Installations
- **If already on migration 26 or 27**: Run `migrate-db-28.sql` / `migrate-db-28-mssql.sql`
- **If starting fresh**: Run all migrations 26, 27, 28 in sequence

### Migration 28 Contents
- Adds `deleted` and `deleted_date` columns
- Updates unique constraint to filtered index
- Ensures data consistency

## Testing

- ✅ All 30 WelcomePageServiceTest tests pass
- ✅ Publishing workflow handles multiple archived versions
- ✅ Audit field mapping works correctly
- ✅ No compilation errors or warnings

## Conclusion

The soft delete implementation provides a robust solution that:
- **Solves the immediate constraint violation issue**
- **Preserves valuable audit data**
- **Enables future data recovery capabilities**
- **Maintains full backward compatibility**
- **Follows established patterns in the codebase**

This approach is superior to hard deletes as it balances operational requirements with data governance best practices.
