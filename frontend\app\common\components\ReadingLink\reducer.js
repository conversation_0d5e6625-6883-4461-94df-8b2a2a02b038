import { handleActions } from 'redux-actions';
import { omit } from 'lodash';
import {
  retrieveReadingLinkSuccess,
  persistReadingLinkSuccess,
  deleteLinkSuccess
} from './actions';

const initialState = {};

export default handleActions(
  {
    [retrieveReadingLinkSuccess]: (state, { payload: link }) => {
      if (link.centralSectionId !== undefined) {
        return {
          ...state,
          [link.centralSectionId]: link
        };
      }
      return {};
    },

    [persistReadingLinkSuccess]: (state, { payload: link }) => ({
      ...state,
      [link.centralSectionId]: link
    }),

    [deleteLinkSuccess]: (state, { payload: link }) => omit(state, [link.centralSectionId])
  },
  initialState
);
