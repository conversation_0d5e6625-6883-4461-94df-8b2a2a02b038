import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';
import SectionNode from './SectionNode';
import { ChapterShape, SectionShape } from '../../../shapes';
import { selectSubChaptersAndSections } from '../../EditorPage/selectors';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

const ChapterNode = ({ chapter, chaptersAndSections, location }) => {
  const items = chaptersAndSections.map(child =>
    child.type === 'LOCAL_CHAPTER' ? (
      <ConnectedChapterNode chapter={child} location={location} key={child.id} />
    ) : (
      <SectionNode location={location} key={child.id} section={child} />
    )
  );

  return (
    <Tree.ItemLink
      to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/`}
      id={chapter.id}
      items={items}
    >
      <Icon icon="bookmark-o" size="small" /> {chapter.title}
    </Tree.ItemLink>
  );
};

ChapterNode.propTypes = {
  chapter: ChapterShape.isRequired,
  chaptersAndSections: PropTypes.arrayOf(PropTypes.oneOfType([ChapterShape, SectionShape]))
    .isRequired,
  location: PropTypes.object.isRequired
};

const makeMapStateToProps = (_, ownProps) => {
  const { chapter } = ownProps;
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(chapter.id);

  const mapStateToProps = state => ({
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  });

  return mapStateToProps;
};

const ConnectedChapterNode = connect(makeMapStateToProps)(ChapterNode);
export default ConnectedChapterNode;
