import { createSelector } from 'reselect';
import { sortBySortOrderAndTitle } from '../../../common/util';

const selectCentralHandbooksState = state => state.centralHandbooks;

export const selectHandbooks = state => selectCentralHandbooksState(state).handbooks;

export const selectPublishedHandbooks = createSelector(selectHandbooks, handbooks =>
  handbooks.filter(handbook => handbook.isPublished)
);

export const selectHasCentralAccess = createSelector(
  selectHandbooks,
  handbooks => handbooks.length > 0
);

export const selectSelectedHandbook = state => selectCentralHandbooksState(state).selectedHandbook;

export const selectRootChapters = createSelector(
  selectCentralHandbooksState,
  selectSelectedHandbook,
  (state, handbook) => {
    if (handbook && Boolean(state.chapters[handbook.id])) {
      return state.chapters[handbook.id].filter(c => !c.parentId).sort(sortBySortOrderAndTitle);
    }
    return null;
  }
);

export const selectSubChaptersAndSections = (handbookId, parentId) =>
  createSelector(selectCentralHandbooksState, state => {
    const chapters = state.chapters[handbookId].filter(c => c.parentId === parentId);
    const sections = state.sections[handbookId].filter(s => s.parentId === parentId);
    return chapters.concat(sections).sort(sortBySortOrderAndTitle);
  });
