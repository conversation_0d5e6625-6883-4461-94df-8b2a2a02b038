import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Icon } from 'kf-bui';
import { fetchFile, getAttachments } from '../../HandbookPage/api';
import {
  DoxFile,
  ExelFile,
  JpgFile,
  PdfFile,
  PngFile,
  PptFile
} from '../../../../editor/components/Attachments/icons';

const fileTypeMapping = {
  png: PngFile,
  jpg: JpgFile,
  jpeg: JpgFile,
  docx: DoxFile,
  xlsx: ExelFile,
  xls: ExelFile,
  pptx: PptFile,
  pdf: PdfFile
};

const Attachments = ({ sectionId, onClose, type }) => {
  const [files, setFiles] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);

  const getFileType = fileName => {
    const extension = fileName
      .split('.')
      .pop()
      .toLowerCase();
    return fileTypeMapping[extension] || PngFile;
  };

  const handleGetFile = async (fileId, fileName) => {
    try {
      const file = fileId.split('/').pop();
      const data = await fetchFile(file);
      const blob = new Blob([data]);
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      onClose();
    } catch (error) {
      console.error('Error getting file:', error);
    }
  };

  useEffect(() => {
    const fetchFileData = async () => {
      try {
        setIsLoading(true);
        const data = await getAttachments(type, sectionId);
        setFiles(data);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    };
    fetchFileData();
  }, []);

  return (
    <div className="attachment-popup">
      <div className="arrow" />
      {isLoading ? (
        <div className="attachment-loading-message">
          <span className="loader" />
          <p>Laster inn vedlegg. Vennligst vent...</p>
        </div>
      ) : (
        <div className="attachment-public-content">
          {files
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map(file => {
              const FileTypeIcon = getFileType(file.title);
              return (
                <button
                  type="button"
                  key={file.id}
                  className="attachment-link"
                  onClick={() => handleGetFile(file.url, file.title)}
                >
                  <FileTypeIcon />
                  <span>{file.title}</span>
                </button>
              );
            })}
        </div>
      )}

      {!isLoading && files.length === 0 && (
        <div className="attachment-loading-message">
          <Icon icon="info" />
          <p>Fant ingen vedlegg</p>
        </div>
      )}
    </div>
  );
};

export default Attachments;

Attachments.propTypes = {
  sectionId: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  type: PropTypes.string.isRequired
};
