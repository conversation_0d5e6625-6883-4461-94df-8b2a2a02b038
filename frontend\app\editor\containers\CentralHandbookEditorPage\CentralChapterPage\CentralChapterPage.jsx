// @flow
import React from 'react';
import { Route, Switch } from 'react-router-dom';
import MoveChapter from '../MoveCentralChapterOrSection';
import CentralChapterScreen from './CentralChapterScreen';
import CentralChapterForm from './CentralChapterForm';

type Props = {
  match: {
    path: string,
    params: {
      handbookId: string
    }
  }
};

const CentralChapterPage = ({ match }: Props) => (
  <Switch>
    <Route exact path={`${match.path}/add-new`} component={CentralChapterForm} />
    <Route exact path={`${match.path}/edit/:chapterId`} component={CentralChapterForm} />
    <Route exact path={`${match.path}/move/:chapterId`} component={MoveChapter} />
    <Route path={`${match.path}/:chapterId`} component={CentralChapterScreen} />
  </Switch>
);

export default CentralChapterPage;
