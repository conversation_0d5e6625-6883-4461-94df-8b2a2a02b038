// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { Title } from 'kf-bui';
import { DragDropContext as dragDropContext } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import { compose } from 'redux';
import injectT from '../../../common/i18n';
import type { LinkCollectionType, LinkType } from '../../../types';
import {
  deleteLink,
  deleteLinkCollection,
  getLinkCollectionForHandbook,
  persistLink,
  persistLinkCollection
} from './api';
import LinkCollection from './components/LinkCollection';
import LinkCollectionForm from './components/LinkCollectionForm';

type Props = {
  match: {
    path: string,
    params: {
      handbookId: string
    }
  },
  t: string => string
};

type State = {
  linkCollections: LinkCollectionType[]
};

class LinkPage extends React.Component<Props, State> {
  state = {
    linkCollections: []
  };

  componentDidMount() {
    this.fetchLinkCollections();
  }

  onSaveLinkCollection = (linkCollection: LinkCollectionType) => {
    persistLinkCollection(linkCollection).then(() => this.fetchLinkCollections());
  };

  onDeleteLinkCollection = (linkCollectionId: string) => {
    deleteLinkCollection(linkCollectionId).then(() => this.fetchLinkCollections());
  };

  onSaveLink = (link: LinkType, linkCollectionId: string) => {
    persistLink(link, linkCollectionId).then(() => this.fetchLinkCollections());
  };

  onDeleteLink = (linkId: string) => {
    deleteLink(linkId).then(() => this.fetchLinkCollections());
  };

  fetchLinkCollections = () => {
    const { handbookId } = this.props.match.params;
    getLinkCollectionForHandbook(handbookId).then(res => this.setState({ linkCollections: res }));
  };

  render() {
    const { match, t } = this.props;
    const { handbookId } = match.params;
    const { linkCollections } = this.state;
    return (
      <div>
        <Title>Lenkesamling</Title>
        {linkCollections.length < 1 && (
          <LinkCollectionForm
            handbookId={handbookId}
            sortOrder={linkCollections.length}
            onSave={this.onSaveLinkCollection}
          />
        )}
        {linkCollections.map(linkCollection => (
          <LinkCollection
            key={linkCollection.id}
            {...linkCollection}
            refreshLinkCollections={this.fetchLinkCollections}
            onDeleteLink={this.onDeleteLink}
            onSaveLink={this.onSaveLink}
            onDeleteLinkCollection={this.onDeleteLinkCollection}
          />
        ))}
        {linkCollections.length === 0 && <span>{t('noLinkCollections')}</span>}
      </div>
    );
  }
}

export default compose(
  injectT('editor.containers.LinkPage'),
  dragDropContext(HTML5Backend)
)(LinkPage);
