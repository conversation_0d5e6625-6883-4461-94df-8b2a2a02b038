// @flow
import React from 'react';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';

import { makeSelectChapterChildren } from '../selectors';
import CentralSectionNode from './CentralSectionNode';

import type {
  CentralChapterType,
  CentralChildrenType,
  CentralTreeNodeType
} from '../../../../types';

type Props = {
  chapter: CentralChapterType,
  chapterChildren?: CentralChildrenType[],
  setSelectedItem: CentralTreeNodeType => void,
  params: { itemId: string },
  disabled?: boolean,
  moving: ?boolean
};

const CentralChapterNode = ({
  chapter,
  chapterChildren,
  setSelectedItem,
  params,
  disabled,
  moving
}: Props) => {
  const sortedChapterChildren =
    chapterChildren && chapterChildren.sort((c1, c2) => c1.sortOrder - c2.sortOrder);

  const items =
    sortedChapterChildren &&
    sortedChapterChildren.map(child =>
      child.type === 'SECTION' ? (
        <CentralSectionNode key={child.id} section={child} moving={moving} />
      ) : (
        <ConnectedCentralChapterNode
          key={child.id}
          chapter={child}
          setSelectedItem={setSelectedItem}
          params={params}
          disabled={disabled || chapter.id === params.itemId}
          moving={moving}
        />
      )
    );

  if (moving) {
    return (
      <Tree.Item
        items={items}
        key={chapter.id}
        id={chapter.id}
        disabled={chapter.id === params.itemId || disabled}
        onClick={() => setSelectedItem(chapter)}
      >
        <Icon icon="bookmark-o" size="small" /> {chapter.title}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      to={`/central-editor/${chapter.centralHandbookId}/chapter/${chapter.id}/`}
      items={items}
      key={chapter.id}
      id={chapter.id}
    >
      <Icon icon="bookmark-o" size="small" /> {chapter.title}
    </Tree.ItemLink>
  );
};

CentralChapterNode.defaultProps = {
  chapterChildren: [],
  disabled: false
};

const mapStateToProps = (state, props) => {
  const selectChildren = makeSelectChapterChildren(props.chapter.id);
  return {
    chapterChildren: selectChildren(state)
  };
};

const ConnectedCentralChapterNode = connect(mapStateToProps)(CentralChapterNode);

export default ConnectedCentralChapterNode;
