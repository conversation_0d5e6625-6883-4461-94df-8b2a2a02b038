/* eslint-env jest */
import { select } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import * as api from '../api';
import * as actions from '../actions';
import * as sagas from '../saga';
import { selectSession, selectLocale } from '../selectors';

import { fetchHandbooks as fetchCentralHandbooks } from '../../CentralTree/saga';
import {
  fetchHandbooks as fetchLocalHandbooks,
  fetchSubscriptions,
  fetchLocalEditorsForOrganization
} from '../../EditorPage/saga';

describe('setOrganization', () => {
  it('calls the API, updates the reducer', () =>
    // In this case we haven't selected a previous org
    expectSaga(sagas.setOrganization, actions.setOrganization('9900'))
      .provide([
        [select(selectSession), { organization: null }],
        [select(selectLocale), { user: {}, organization: null }],
        [
          matchers.call.fn(api.setExternalOrganization),
          { organization: { id: '9900', name: 'Kommuneforlgat' } }
        ],
        [matchers.fork.fn(fetchLocalHandbooks)],
        [matchers.fork.fn(fetchCentralHandbooks)],
        [matchers.fork.fn(fetchSubscriptions)],
        [matchers.fork.fn(fetchLocalEditorsForOrganization)]
      ])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));

  it('toasts when switching organizations', () =>
    // In this case we have selected a previous org!
    expectSaga(sagas.setOrganization, actions.setOrganization('9900'))
      .provide([
        [select(selectSession), { organization: { id: 'other', name: 'other' } }],
        [
          matchers.call.fn(sagas.translationWrapper),
          'nb',
          'editor.containers.App.changedOrganizationFail1'
        ],
        [
          matchers.call.fn(sagas.translationWrapper),
          'nb',
          'editor.containers.App.changedOrganizationFail2'
        ],
        [
          select(selectLocale),
          {
            user: {},
            organization: { id: 'other', name: 'other', language: 'nb' }
          }
        ],
        [
          matchers.call.fn(api.setExternalOrganization),
          { organization: { id: '9900', name: 'Kommuneforlgat' } }
        ],
        [matchers.fork.fn(fetchLocalHandbooks)],
        [matchers.fork.fn(fetchCentralHandbooks)],
        [matchers.fork.fn(fetchSubscriptions)],
        [matchers.fork.fn(fetchLocalEditorsForOrganization)]
      ])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));

  it('toasts when something goes awry', () =>
    expectSaga(sagas.setOrganization, actions.setOrganization('9900'))
      .provide([
        [
          matchers.call.fn(sagas.translationWrapper),
          'nb',
          'editor.containers.App.changedOrganizationFail1'
        ],
        [
          matchers.call.fn(sagas.translationWrapper),
          'nb',
          'editor.containers.App.changedOrganizationFail2'
        ],
        [
          select(selectLocale),
          {
            user: {},
            organization: { id: 'other', name: 'other', language: 'nb' }
          }
        ],
        [matchers.call.fn(api.setExternalOrganization), throwError(new Error('oops'))]
      ])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));
});
