# Welcome Page Version Constraint Verification

## Current Constraint Behavior

After Migration 28, the `welcome_page_version` table has a **filtered unique constraint**:

```sql
CREATE UNIQUE INDEX welcome_page_version_handbook_status_active 
ON welcome_page_version (handbook_id, status) 
WHERE deleted = 0;
```

## What This Means

### ✅ **ALLOWED**: Multiple records with same (handbook_id, status) if some are soft-deleted
```sql
-- Example: These records CAN coexist in the table
handbook_id='hb-1', status='ARCHIVED', deleted=1, deleted_date=1234567890  -- Soft-deleted
handbook_id='hb-1', status='ARCHIVED', deleted=1, deleted_date=1234567891  -- Soft-deleted  
handbook_id='hb-1', status='ARCHIVED', deleted=0, deleted_date=NULL        -- Active
```

### ❌ **PREVENTED**: Multiple active records with same (handbook_id, status)
```sql
-- Example: These records CANNOT coexist (constraint violation)
handbook_id='hb-1', status='ARCHIVED', deleted=0, deleted_date=NULL        -- Active
handbook_id='hb-1', status='ARCHIVED', deleted=0, deleted_date=NULL        -- Active ❌ FAILS
```

## Constraint Rules Summary

| Scenario | handbook_id | status | deleted | Result |
|----------|-------------|--------|---------|---------|
| First active record | hb-1 | ARCHIVED | 0 | ✅ **ALLOWED** |
| Second active record (same handbook+status) | hb-1 | ARCHIVED | 0 | ❌ **BLOCKED** |
| Soft-deleted record (same handbook+status) | hb-1 | ARCHIVED | 1 | ✅ **ALLOWED** |
| Different handbook | hb-2 | ARCHIVED | 0 | ✅ **ALLOWED** |
| Different status | hb-1 | PUBLISHED | 0 | ✅ **ALLOWED** |

## Real-World Publishing Scenario

### Before Publishing (Initial State)
```sql
INSERT INTO welcome_page_version VALUES 
('v1', 'hb-1', 'PUBLISHED', 1000, 1000, 'user1', 0, NULL),  -- Active published
('v2', 'hb-1', 'DRAFT', 2000, 2000, 'user2', 0, NULL),      -- Active draft
('v3', 'hb-1', 'ARCHIVED', 500, 500, 'user0', 0, NULL);     -- Active archived
```

### During Publishing Process
```sql
-- Step 1: Soft delete existing archived version
UPDATE welcome_page_version 
SET deleted = 1, deleted_date = 3000 
WHERE handbook_id = 'hb-1' AND status = 'ARCHIVED' AND deleted = 0;

-- Step 2: Archive current published version (now allowed - no active archived exists)
UPDATE welcome_page_version 
SET status = 'ARCHIVED' 
WHERE id = 'v1';

-- Step 3: Promote draft to published
UPDATE welcome_page_version 
SET status = 'PUBLISHED' 
WHERE id = 'v2';
```

### After Publishing (Final State)
```sql
-- Final table contents:
('v1', 'hb-1', 'ARCHIVED', 1000, 1000, 'user1', 0, NULL),   -- New active archived
('v2', 'hb-1', 'PUBLISHED', 2000, 2000, 'user2', 0, NULL),  -- New active published  
('v3', 'hb-1', 'ARCHIVED', 500, 500, 'user0', 1, 3000);     -- Soft-deleted archived
```

## Verification Queries

### Check Active Records Only (What Application Sees)
```sql
SELECT handbook_id, status, COUNT(*) as count
FROM welcome_page_version 
WHERE deleted = 0
GROUP BY handbook_id, status
HAVING COUNT(*) > 1;
-- Should return 0 rows (no duplicates allowed)
```

### Check All Records Including Soft-Deleted (Audit View)
```sql
SELECT handbook_id, status, deleted, deleted_date, COUNT(*) as count
FROM welcome_page_version 
GROUP BY handbook_id, status, deleted, deleted_date
ORDER BY handbook_id, status, deleted;
-- Can show multiple ARCHIVED records for same handbook
```

### Test Constraint Violation
```sql
-- This should FAIL with constraint violation:
INSERT INTO welcome_page_version VALUES 
('v4', 'hb-1', 'PUBLISHED', 4000, 4000, 'user3', 0, NULL);
-- Error: duplicate key value violates unique constraint
```

## Key Points

1. **✅ Constraint Still Enforces Uniqueness**: Each handbook can have only ONE active version per status
2. **✅ Soft-Deleted Records Don't Count**: Multiple soft-deleted versions can exist for audit
3. **✅ Publishing Works**: Old archived versions are soft-deleted before creating new ones
4. **✅ Data Integrity Maintained**: No duplicate active versions possible
5. **✅ Audit Trail Preserved**: Historical versions remain in database

## Application Behavior

- **Normal Queries**: Always include `WHERE deleted = 0` → See only active records
- **Audit Queries**: Can query without deleted filter → See full history
- **Publishing**: Soft-deletes old archived before creating new → No constraint violation
- **Data Recovery**: Can "undelete" soft-deleted records if needed

The constraint **still prevents duplicate active records** while allowing **multiple historical records** for audit purposes.
