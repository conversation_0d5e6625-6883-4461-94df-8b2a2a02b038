import React from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';

// See https://reacttraining.com/react-router/web/guides/scroll-restoration
class ScrollToTop extends React.Component {
  /* TODO: Figure out how to work this with hash scrolling :<
  componentDidUpdate(prevProps) {
    if (this.props.location !== prevProps.location) {
      window.scrollTo(0, 0);
    }
  } */

  render() {
    const { children } = this.props;
    return children;
  }
}

ScrollToTop.propTypes = {
  children: PropTypes.node
};

ScrollToTop.defaultProps = {
  children: null
};

export default withRouter(ScrollToTop);
