// @flow
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router';
import { toast } from 'kf-toaster';
import { Card, Tree } from 'kf-bui';
import type { CentralHandbookType } from '../../../../types';
import injectT from '../../../../common/i18n';
import { selectCentralHandbooks } from '../selectors';
import CentralHandbookNode from './CentralHandbookNode';
import { pendingPublications } from '../api';

type Props = {
  match: { params: {} },
  location: { pathname: string },
  centralHandbooks: Array<CentralHandbookType>,
  moving?: boolean,
  t: string => string,
  successToast: Function
};

const CentralTree = ({ centralHandbooks, moving, t, match, location, successToast }: Props) => {
  const [pendingHandbooks, setPendingHandbooks] = useState([]);

  const extractHandbookIdFromPathname = () => {
    const matchPath = location.pathname.match(/\/central-editor\/([^/]+)/);
    return matchPath ? match[1] : null;
  };
  const handbookIdInRoute = extractHandbookIdFromPathname();

  const pollPendingPublications = async () => {
    const updatedPendingHandbooks = [];

    await Promise.all(
      pendingHandbooks.map(async pendingBook => {
        if (handbookIdInRoute === pendingBook.id) return;

        try {
          const stillPending = await pendingPublications(pendingBook.id);
          if (stillPending) {
            updatedPendingHandbooks.push(pendingBook);
          } else {
            successToast(`Endringer publisert: ${pendingBook.handbookTitle}`);
          }
        } catch (error) {
          console.error(`Error polling pending status for handbook ${pendingBook.id}:`, error);
        }
      })
    );

    setPendingHandbooks(updatedPendingHandbooks);
  };

  const checkInitialPendingPublications = async () => {
    const initialPendingHandbooks = [];

    await Promise.all(
      centralHandbooks.map(async book => {
        if (handbookIdInRoute === book.id) return;

        try {
          const isPending = await pendingPublications(book.id);
          if (isPending) {
            initialPendingHandbooks.push({ id: book.id, handbookTitle: book.title });
          }
        } catch (error) {
          console.error(`Error checking initial pending status for handbook ${book.id}:`, error);
        }
      })
    );

    setPendingHandbooks(initialPendingHandbooks);
  };

  useEffect(() => {
    if (centralHandbooks.length > 0) {
      checkInitialPendingPublications();
    }
  }, [centralHandbooks, handbookIdInRoute]);

  useEffect(() => {
    let pollingInterval;
    if (pendingHandbooks.length > 0) {
      pollingInterval = setInterval(pollPendingPublications, 10000);
    }

    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pendingHandbooks, handbookIdInRoute]);

  const items = centralHandbooks
    ? centralHandbooks.map(book => (
        <CentralHandbookNode
          key={book.id}
          centralHandbook={book}
          moving={moving}
          params={match.params}
        />
      ))
    : null;

  return (
    <Card>
      <Card.Header>
        <Card.Title>{t('treeHeader')}</Card.Title>
      </Card.Header>
      <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>
        <Tree>{items}</Tree>
      </Card.Content>
    </Card>
  );
};

CentralTree.defaultProps = {
  moving: false
};

const mapStateToProps = state => ({
  centralHandbooks: selectCentralHandbooks(state)
});

export default compose(
  injectT('editor.containers.CentralTree'),
  withRouter,
  connect(mapStateToProps, {
    successToast: toast.success
  })
)(CentralTree);
