import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';
import SectionNode from './ClickableSectionNode';
import { ChapterShape, SectionShape } from '../../../../shapes';
import { selectSubChaptersAndSections } from '../../../EditorPage/selectors';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

const ChapterNode = ({
  chapter,
  chaptersAndSections,
  selectElement,
  activeChapter,
  activeSection,
  disableChapterChildren
}) => {
  const items = chaptersAndSections.map(child =>
    child.type === 'LOCAL_CHAPTER' ? (
      <ConnectedChapterNode
        activeChapter={activeChapter}
        activeSection={activeSection}
        chapter={child}
        disableChapterChildren={
          disableChapterChildren || (activeChapter && activeChapter.id === chapter.id)
        }
        key={child.id}
        selectElement={selectElement}
      />
    ) : (
      <SectionNode key={child.id} section={child} />
    )
  );

  let disabled = disableChapterChildren || false;
  // If we are moving a section and this chapter is the parent,
  // disable it (no need to move to the same parent)
  if (activeSection && activeSection.parentId === chapter.id) {
    disabled = true;
    // If we the chapter we are moving is this one, disable it.
    // Also disable if is the parent of the one we are moving (no need to move to the same parent)
  } else if (
    activeChapter &&
    (activeChapter.id === chapter.id || activeChapter.parentId === chapter.id)
  ) {
    disabled = true;
  }

  return (
    <Tree.Item
      id={chapter.id}
      // active={!activeSection && activeChapter === chapter}
      disabled={disabled}
      items={items}
      onClick={() => selectElement(chapter)}
      // onClick={onClick}
    >
      <Icon icon="bookmark-o" size="small" /> {chapter.title}
    </Tree.Item>
  );
};

ChapterNode.propTypes = {
  chapter: ChapterShape.isRequired,
  chaptersAndSections: PropTypes.arrayOf(PropTypes.oneOfType([ChapterShape, SectionShape]))
    .isRequired,
  disableChapterChildren: PropTypes.bool.isRequired,
  selectElement: PropTypes.func.isRequired,
  // Used to determining which node to display as active
  activeChapter: ChapterShape,
  activeSection: SectionShape
};

ChapterNode.defaultProps = {
  activeChapter: null,
  activeSection: null
};

const makeMapStateToProps = (_, ownProps) => {
  const { chapter } = ownProps;
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(chapter.id);

  const mapStateToProps = state => ({
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  });

  return mapStateToProps;
};

const ConnectedChapterNode = connect(makeMapStateToProps)(ChapterNode);
export default ConnectedChapterNode;
