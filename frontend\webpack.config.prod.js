/**
 * PRODUCTION WEBPACK CONFIGURATION
 */

// Important modules this config uses
const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = require('./webpack.config.base')({
  // In production, we skip all hot-reloading stuff
  entry: {
    editor: path.join(process.cwd(), 'app/editor/index.jsx'),
    public: path.join(process.cwd(), 'app/public/index.jsx')
  },

  // Utilize long-term caching by adding content hashes (not compilation hashes) to compiled assets
  output: {
    filename: 'js/[name].[chunkhash].js',
    chunkFilename: 'js/[name].[chunkhash].chunk.js',
    publicPath: '/handboker/'
  },

  plugins: [
    // Minify and optimize the index.html
    new HtmlWebpackPlugin({
      xhtml: true,
      inject: true,
      template: './app/editor/index.html',
      favicon: './favicon.ico',
      filename: 'editor/index.html',
      excludeChunks: ['public'],
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true
      },
    }),
    new HtmlWebpackPlugin({
      xhtml: true,
      inject: true,
      template: './app/public/index.html',
      favicon: './favicon.ico',
      filename: 'public/index.html',
      excludeChunks: ['editor'],
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true
      }
    }),

    new MiniCssExtractPlugin({
      filename: 'css/[name].css',
      chunkFilename: 'css/[name].css'
    })
  ],
  optimization: {
    minimizer: [
      new UglifyJsPlugin({
        // react-intl skaper en feil i UglifyJs, da UglifyJs ikke forstår ES6
        exclude: [/js\/npm.react-intl/]
      })
    ]
  }
});
