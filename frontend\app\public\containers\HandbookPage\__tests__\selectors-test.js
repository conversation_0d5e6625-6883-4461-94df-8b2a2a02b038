/* eslint-env jest */
import { selectHandbook, selectSubChaptersAndSections } from '../selectors';

const state = {
  handbook: {
    handbook: {
      id: 'HMT',
      title: 'KF HMT-Håndbok'
    },
    chapters: [
      {
        id: 'hmtChap1',
        title: 'Avvik',
        handbookId: 'HMT',
        sortOrder: 1,
        pendingChange: true
      },
      {
        id: 'hmtChap3',
        title: 'Risiko',
        handbookId: 'HMT',
        sortOrder: 2,
        parentId: 'hmtChap6'
      },
      {
        id: 'hmtChap5',
        title: 'Brannvern',
        handbookId: 'HMT',
        sortOrder: 3
      },
      {
        id: 'hmtChap6',
        title: 'Rutiner',
        handbookId: 'HMT',
        sortOrder: 4,
        parentId: 'hmtChap5'
      }
    ],
    sections: [
      {
        id: 'hmtDoc2',
        title: 'Avvik skjema',
        handbookId: 'HMT',
        sortOrder: 5
      },
      {
        id: 'hmtDoc4',
        title: 'Skjema for Risikoanalyse',
        handbookId: 'HMT',
        sortOrder: 6,
        parentId: 'hmtChap3',
        pendingChange: true
      },
      {
        id: 'hmtDoc7',
        title: 'Rutiner for barnehager',
        handbookId: 'HMT',
        sortOrder: 7,
        parentId: 'hmtChap6'
      },
      {
        id: 'hmtDoc8',
        title: 'Rutiner for Skoler',
        handbookId: 'HMT',
        sortOrder: 8,
        parentId: 'hmtChap6'
      },
      {
        id: 'hmtDoc9',
        title: 'Rutiner for kontor',
        handbookId: 'HMT',
        sortOrder: 9,
        parentId: 'hmtChap6'
      }
    ]
  }
};

describe('Handbooks selectors', () => {
  describe('selectSubChaptersAndSections', () => {
    it('selects the sub chapters and sections and applies property to chapters so we can differentiate them from sections', () => {
      const parentId = 'hmtChap6';
      const selector = selectSubChaptersAndSections(parentId)(state);
      expect(selector).toMatchSnapshot();
    });
  });

  describe('selectHandbook', () => {
    it('selects the handbook', () => {
      const result = selectHandbook(state);
      expect(result).toBe(state.handbook.handbook);
    });
  });
});
