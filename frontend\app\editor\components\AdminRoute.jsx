import React from 'react';
import PropTypes from 'prop-types';
import { Route, Redirect } from 'react-router-dom';
import { SessionShape } from '../shapes';

/**
 * If the route matches and the user isn't a KF Admin, redirect to the 403
 * page instead of rendering the component
 */
const AdminRoute = ({ session, component: Component, ...rest }) => (
  <Route
    // eslint-disable-next-line react/jsx-props-no-spreading
    {...rest}
    render={() => (session.isKfAdmin ? <Component /> : <Redirect to="/forbidden" />)}
  />
);

AdminRoute.propTypes = {
  session: SessionShape.isRequired,
  component: PropTypes.func.isRequired
};

export default AdminRoute;
