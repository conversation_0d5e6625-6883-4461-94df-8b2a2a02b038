import axios from 'axios';
import { normalize, schema } from 'normalizr';

const sectionSchema = new schema.Entity('sections');
const chapterSchema = new schema.Entity('chapters');
export const handbookSchema = new schema.Object({
  sections: [sectionSchema],
  chapters: [chapterSchema]
});

/**
 * @param {String} externalOrgId
 * @param {String} handbookId
 */
export function fetchHandbook(externalOrgId, handbookId) {
  return axios
    .get(`${externalOrgId}/${handbookId}`)
    .then(res => normalize(res.data, handbookSchema));
}

/**
 * @param {String} sectionId
 */
export function fetchSection(sectionId) {
  return axios.get(`section/${sectionId}`).then(res => res.data);
}

export async function getAttachments(type, id) {
  const response = await axios.get(`/${type}/${id}/attachments`);
  return response.data;
}

export async function fetchFile(fileId) {
  const response = await axios.get(`/attactment/${fileId}`, {
    responseType: 'blob'
  });
  return response.data;
}

export async function getAttachmentCount(type, id) {
  const response = await axios.get(`/${type}/${id}/attachments/count`);
  return response.data;
}
