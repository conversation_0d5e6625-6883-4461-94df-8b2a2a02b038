import React from 'react';
import PropTypes from 'prop-types';

const CircularProgress = ({ percentage }) => {
  // Calculate the value for stroke-dashoffset
  const value = (1 - percentage / 100) * (2 * Math.PI * 40);

  return (
    <div className="circular-progress">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 100 100"
        aria-labelledby="title"
        role="img"
      >
        <title id="title">SVG circular progress bar</title>
        <circle cx="50" cy="50" r="40" />
        <circle cx="50" cy="50" r="40" style={{ strokeDashoffset: value }} />
      </svg>
      <p className="pct">{`${percentage}%`}</p>
    </div>
  );
};

CircularProgress.propTypes = {
  percentage: PropTypes.number.isRequired
};

export default CircularProgress;
