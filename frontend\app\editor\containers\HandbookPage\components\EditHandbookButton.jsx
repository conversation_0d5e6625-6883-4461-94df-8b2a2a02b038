import React from 'react';
import PropTypes from 'prop-types';
import { Button, Icon, Control } from 'kf-bui';
import CreateOrUpdateHandbook from '../../CreateOrUpdateHandbook';
import { HandbookShape } from '../../../shapes';
import BooleanToggler from '../../../components/BooleanToggler';

const EditHandbook = ({ handbook, t }) => (
  <BooleanToggler>
    {(toggle, value) => (
      <Control>
        {[
          <Button key="button" onClick={toggle} title="Rediger denne Håndboken" size="small">
            <Icon icon="pencil" size="small" />
            <span>{t('editButton')}</span>
          </Button>,
          value ? <CreateOrUpdateHandbook key="modal" onHide={toggle} handbook={handbook} /> : null
        ]}
      </Control>
    )}
  </BooleanToggler>
);

EditHandbook.propTypes = {
  handbook: HandbookShape.isRequired,
  t: PropTypes.func.isRequired
};

export default EditHandbook;
