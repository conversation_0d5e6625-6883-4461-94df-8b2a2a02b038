{"name": "handboker", "description": "Frontend code for Handboker", "version": "0.0.1", "scripts": {"prebuild": "npm install && npm run lint && npm run format && npm run flow check && npm run build:clean", "build": "webpack --env.NODE_ENV=local --env.production --color -p", "build:clean": "rimraf ./build/*", "start": "cross-env --env.NODE_ENV=local node dev-server hi=HELLO --eh", "start:inline": "cross-env --env.NODE_ENV=local node dev-server inline", "start:analyze": "cross-env --env.NODE_ENV=local node dev-server analyzer", "test": "cross-env NODE_ENV=test jest --runInBand --passWithNoTests", "test:clean": "rimraf ./coverage", "test:watch": "npm test -- --watch", "test:cov": "npm test -- --coverage", "lint": "npm run lint:js", "lint:js": "eslint --fix --ext .js --ext .jsx ./app", "format": "prettier-eslint --write \"app/**/*.{js,jsx}\"", "flow": "flow"}, "private": true, "repository": {"type": "git", "url": "ssh://******************:7999/kf/handboker.git"}, "jest": {"transform": {"^.+\\.jsx$": "babel-jest", "^.+\\.js$": "babel-jest"}, "testEnvironment": "node", "rootDir": "app", "moduleNameMapper": {"^.+\\.css$": "identity-obj-proxy"}}, "dependencies": {"@babel/polyfill": "7.2.5", "axios": "0.16.2", "classnames": "^2.5.1", "connected-react-router": "4.4.1", "core-js": "2.6.4", "formik": "1.3.2", "history": "4.6.3", "js-cookie": "^3.0.5", "kf-bui": "4.2.8-10", "kf-norwegian-sort": "1.0.0", "kf-toaster": "2.2.6", "kf-wysiwyg": "1.3.5", "lodash": "4.17.4", "moment": "^2.30.1", "normalizr": "3.2.3", "object-path-immutable": "0.5.1", "prop-types": "15.5.10", "query-string": "4.3.4", "react": "16.8.6", "react-dnd": "2.4.0", "react-dnd-html5-backend": "2.4.1", "react-dom": "16.8.6", "react-dropzone": "^11.4.2", "react-hook-form": "6.0.5", "react-idle-timer": "^4.5.0", "react-intl": "2.6.0", "react-redux": "5.0.5", "react-router": "4.1.1", "react-router-dom": "4.1.1", "react-sortable-hoc": "0.6.7", "react-sticky-el": "1.0.20", "react-title-component": "1.0.1", "react-waypoint": "^8.1.0", "redux": "3.7.1", "redux-actions": "2.2.1", "redux-entities": "2.0.0", "redux-form": "6.8.0", "redux-saga": "0.15.4", "redux-thunk": "2.2.0", "reselect": "3.0.1", "styled-components": "2.1.1", "tinymce": "^5.7.0", "yup": "0.26.6"}, "devDependencies": {"@babel/core": "7.2.2", "@babel/plugin-proposal-class-properties": "7.2.3", "@babel/plugin-proposal-object-rest-spread": "7.2.0", "@babel/preset-env": "7.2.3", "@babel/preset-flow": "7.0.0", "@babel/preset-react": "7.0.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "babel-loader": "8.0.5", "babel-plugin-lodash": "3.3.4", "babel-plugin-react-intl": "3.0.1", "cross-env": "5.0.2", "css-loader": "0.28.4", "eslint": "6.6.0", "eslint-config-kf": "4.4.0", "eslint-plugin-flowtype": "4.7.0", "exports-loader": "0.6.4", "express": "4.15.3", "file-loader": "6.0.0", "flow-bin": "0.93.0", "html-loader": "0.5.5", "html-webpack-plugin": "3.2.0", "identity-obj-proxy": "3.0.0", "imports-loader": "0.7.1", "jest": "^23.6.0", "mini-css-extract-plugin": "0.5.0", "react-test-renderer": "15.6.1", "redux-saga-test-plan": "3.7.0", "regenerator-runtime": "^0.12.1", "rimraf": "2.6.1", "style-loader": "0.18.2", "uglifyjs-webpack-plugin": "^1.0.0", "url-loader": "0.5.9", "webpack": "4.28.1", "webpack-cli": "3.2.1", "webpack-dev-middleware": "3.7.2", "webpack-dev-server": "3.1.14", "webpack-hot-middleware": "^2.25.0"}}