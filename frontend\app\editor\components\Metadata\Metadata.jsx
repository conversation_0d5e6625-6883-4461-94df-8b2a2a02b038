import React from 'react';
import PropTypes from 'prop-types';

import { Level, Heading, FormattedDate } from 'kf-bui';

import injectT from '../../../common/i18n';
import { HandbookShape, ChapterShape, SectionShape } from '../../shapes';

const Metadata = ({ t, element, prepend, append }) => (
  <Level style={{ fontSize: '0.8em' }}>
    {prepend && <Level.Item textCentered>{prepend}</Level.Item>}
    <Level.Item textCentered>
      <div>
        <Heading>{t('created')}</Heading> <p>{element.createdBy || 'Ukjent'}</p>
        <FormattedDate value={element.createdDate} />
      </div>
    </Level.Item>
    <Level.Item textCentered>
      <div>
        <Heading>{t('updated')}</Heading> <p>{element.updatedBy || 'Ukjent'}</p>
        <FormattedDate value={element.updatedDate} />
      </div>
    </Level.Item>
    {element.textUpdatedBy && (
      <Level.Item textCentered>
        <div>
          <Heading>Tekst oppdatert av</Heading> <p>{element.textUpdatedBy || 'Ukjent'}</p>
          <FormattedDate value={element.textUpdatedDate} />
        </div>
      </Level.Item>
    )}
    {append && <Level.Item textCentered>{append}</Level.Item>}
  </Level>
);

Metadata.propTypes = {
  t: PropTypes.func.isRequired,
  prepend: PropTypes.node,
  append: PropTypes.node,
  element: PropTypes.oneOfType([HandbookShape, ChapterShape, SectionShape]).isRequired
};

Metadata.defaultProps = {
  prepend: null,
  append: null
};

export default injectT('editor.components.Metadata')(Metadata);
