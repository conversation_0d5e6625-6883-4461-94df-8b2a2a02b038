/* eslint-env jest */
import { initialState } from '../reducer';
import { selectHandbooks } from '../selectors';

describe('Central Handbook selectors', () => {
  let state;
  beforeEach(() => {
    state = {
      centralAccess: initialState
    };
  });

  describe('selectHandbooks', () => {
    it('selects the selected node ', () => {
      const handbooks = [
        { id: 'book1', title: 'handbook 1' },
        { id: 'book2', title: 'handbook 2' }
      ];
      state.centralAccess.handbooks = handbooks;

      expect(selectHandbooks(state)).toEqual(handbooks);
    });
  });
});
