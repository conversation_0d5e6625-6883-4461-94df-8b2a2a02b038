import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Modal } from 'kf-bui';
import { withRouter } from 'react-router-dom';
import IdleTimer from 'react-idle-timer';

const timeout = 2 * 60 * 60 * 1000 - 10 * 60 * 1000; // Timeout is Session time 2 hours - 10 minutes
const idleRemainingTime = 600; // 10 minutes
const totalIdleTime = timeout + idleRemainingTime * 1000;

export const IdleAlert = ({ history, t }) => {
  const idleTimerRef = useRef(null);
  const [remainingTime, setRemainingTime] = useState(idleRemainingTime);
  const [title, setTitle] = useState(t('sessionInactiveTitle'));
  const [message, setMessage] = useState(t('sessionInactiveMessage'));
  const [showModal, setShowModal] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);
  const [countdownStarted, setCountdownStarted] = useState(false);
  const [idleStartTime, setIdleStartTime] = useState(null);
  const [lastUpdatedTime, setLastUpdatedTime] = useState(null);

  const handleAllowAfterExpiration = () => {
    setRemainingTime(0);
    setShowModal(false);
    setCountdownStarted(false);
    setSessionExpired(true);
    setIdleStartTime(null);
    setLastUpdatedTime(null);

    if (idleTimerRef.current) {
      idleTimerRef.current = null;
    }
  };

  const handleStillHere = () => {
    setRemainingTime(idleRemainingTime);
    setShowModal(false);
    setCountdownStarted(false);
    setSessionExpired(false);
    setIdleStartTime(null);
    setLastUpdatedTime(Date.now());

    if (idleTimerRef.current) {
      idleTimerRef.current.reset();
    }
  };

  const handleLogout = () => {
    handleAllowAfterExpiration();
    history.push('/');
  };

  const onIdle = () => {
    if (!sessionExpired && !countdownStarted && idleTimerRef.current) {
      setIdleStartTime(Date.now());
      setRemainingTime(idleRemainingTime);
      setShowModal(true);
      setCountdownStarted(true);
    }
  };

  const handleOnActive = () => {
    if (!sessionExpired && idleTimerRef.current) {
      const lastIdleTime = idleTimerRef.current.getLastIdleTime();
      const lastActiveTime = idleTimerRef.current.getLastActiveTime();
      const currentTime = Date.now();
      const idleDuration = currentTime - lastIdleTime;
      const activeDuration = currentTime - lastActiveTime;

      const timeSinceIdleStart = currentTime - idleStartTime;
      const timeSinceLastUpdated = currentTime - lastUpdatedTime;

      if (
        idleDuration > totalIdleTime ||
        activeDuration > totalIdleTime ||
        timeSinceLastUpdated > totalIdleTime
      ) {
        setRemainingTime(0);
        setTitle(t('sessionExpiredTitle'));
        setMessage(t('sessionExpiredMessage'));
        setSessionExpired(true);
        setCountdownStarted(false);
        setIdleStartTime(null);
      } else if (timeSinceIdleStart < idleRemainingTime * 1000) {
        const remainingTimeToUpdate = Math.floor(
          (idleRemainingTime * 1000 - timeSinceIdleStart) / 1000
        );
        setRemainingTime(remainingTimeToUpdate);
      }
    }
  };

  useEffect(() => {
    let interval;
    if (showModal && !sessionExpired) {
      interval = setInterval(() => {
        setRemainingTime(prevTime => {
          const newTime = Math.max(prevTime - 1, 0);
          if (newTime === 0) {
            setTitle(t('sessionExpiredTitle'));
            setMessage(t('sessionExpiredMessage'));
            setSessionExpired(true);
          }
          return newTime;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [showModal, sessionExpired, remainingTime]);

  useEffect(() => {
    if (remainingTime === 0 && !sessionExpired) {
      setTitle(t('sessionExpiredTitle'));
      setMessage(t('sessionExpiredMessage'));
      setSessionExpired(true);
    }
  }, [remainingTime, sessionExpired]);

  useEffect(() => {
    setLastUpdatedTime(Date.now());
  }, [t]);

  const minutes = Math.floor(remainingTime / 60);
  const seconds = remainingTime % 60;

  return (
    <>
      <IdleTimer
        ref={idleTimerRef}
        timeout={timeout}
        onIdle={onIdle}
        debounce={250}
        startOnMount
        onActive={handleOnActive}
      />

      {showModal && (
        <Modal>
          <Modal.Header>
            <Modal.Title>{title}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>{message}</p>
            {remainingTime > 0 && (
              <p>
                {`${t('sessionTimeoutRemaining')}: ${minutes} ${t('minutes')} ${seconds} ${t(
                  'seconds'
                )}`}
              </p>
            )}
          </Modal.Body>
          <Modal.Footer>
            {remainingTime === 0 ? (
              <>
                <Button color="primary" onClick={handleAllowAfterExpiration}>
                  {t('close')}
                </Button>
                <Button color="danger" onClick={handleLogout}>
                  {t('logOut')}
                </Button>
              </>
            ) : (
              <Button disabled={remainingTime === 0} color="primary" onClick={handleStillHere}>
                {t('keepSessionActive')}
              </Button>
            )}
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

IdleAlert.propTypes = {
  history: PropTypes.object.isRequired,
  t: PropTypes.func.isRequired
};

export default withRouter(IdleAlert);
