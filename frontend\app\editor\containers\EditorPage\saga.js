import { all, call, put, select, takeLatest, takeEvery } from 'redux-saga/effects';
import { toast } from 'kf-toaster';
import { push, replace } from 'connected-react-router';

import * as actions from './actions';
import * as api from './api';
import { getLocalEditorsForOrganization as fetchLocalEditorsForOrg } from '../App/api';
import { selectChapterById, selectSectionById } from './selectors';
import { selectExternalOrganizationId } from '../App/selectors';

export function* fetchHandbooks() {
  try {
    const handbooks = yield call(api.fetchLocalHandbooks);
    yield put(actions.fetchLocalHandbooksSuccess(handbooks));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente håndbøker.'));
  }
}

export function* fetchSection({ payload: id }) {
  try {
    const res = yield call(api.fetchSection, id);
    yield put(actions.fetchSectionSuccess(res));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente avsnitt.'));
  }
}
export function* fetchChapter({ payload: id }) {
  try {
    const res = yield call(api.fetchChapter, id);
    yield put(actions.fetchChapterSuccess(res));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente kapittel.'));
  }
}
export function* fetchHandbook({ payload: id }) {
  try {
    const res = yield call(api.fetchHandbook, id);
    yield put(actions.fetchHandbookSuccess(res));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente håndbok.'));
  }
}

/**
 * @param {Array} ids
 */
export function* sortItems({ payload: ids }) {
  try {
    yield call(api.saveSortOrder, ids);
    // Refetch content after sort
    yield call(fetchHandbooks);
    yield put(toast.success('Lagret sortering.'));
  } catch (error) {
    yield put(toast.error('Klarte ikke lagre sortering.'));
  }
}

/**
 * Create or update a handbook
 * @param {Object} handbook
 */
export function* saveLocalHandbook({ payload: handbook }) {
  const updating = Boolean(handbook.id);

  try {
    const result = yield call(api.saveLocalHandbook, handbook);

    // If we are basing a handbook on a central handbook, we need to get all the children
    if (!updating && handbook.importedHandbookId) {
      yield call(fetchHandbooks);
    }

    yield put(actions.saveLocalHandbookSuccess(result));

    // New handbooks are automatically subscribed in the backend,
    // so we need to update the state here
    if (!updating) {
      yield put(actions.subscribeSuccess(result.id, true));
      yield call(api.subscribeToHandbook, result.id, true);
    }

    // Navigate to any new handbook
    yield put(push(`/editor/${result.id}/`));
    yield put(toast.success(updating ? 'Lagret endringer.' : 'Opprettet lokal håndbok.'));
  } catch (error) {
    yield put(toast.error('Klarte ikke lagre håndboken'));
  }
}

export function* deleteLocalHandbook({ payload: handbook }) {
  try {
    yield call(api.deleteLocalHandbook, handbook.id);
    yield put(actions.deleteLocalHandbookSuccess(handbook.id));
    yield put(replace('/editor/'));
    yield put(toast.success('Slettet Håndbok.', `Slettet: ${handbook.title}`));
  } catch (error) {
    yield put(toast.error('Det oppstod en feil under sletting av håndboken.'));
  }
}

/**
 * Using the same saga for both creating, updating and moving to a new parent!
 * @param {Object} chapter
 */
export function* saveLocalChapter({ payload: { chapter, centralChange } }) {
  try {
    let result;
    let oldChapter;

    // If we are updating, select the old version from the store so we can see if we have moved it.
    if (chapter.id) {
      [result, oldChapter] = yield all([
        call(api.saveLocalChapter, chapter, centralChange || 'local'),
        select(selectChapterById(chapter.id))
      ]);
    } else {
      result = yield call(api.saveLocalChapter, chapter, centralChange || 'local');
      // If we are basing a chapter on a central chapter, we need to get all the children
      if (chapter.importedHandbookChapterId) {
        yield call(fetchHandbooks);
      }
    }

    // Notify the store
    yield put(actions.saveLocalChapterSuccess(result));

    // If this is a new chapter, navigate to it and return early
    if (!oldChapter) {
      yield put(push(`/editor/${chapter.handbookId}/chapter/${result.id}/`));
      yield put(toast.success('Opprettet kapittel.'));
      return;
    }

    // If we moved to a new parent, replace the url
    if (oldChapter.handbookId !== result.handbookId || oldChapter.parentId !== result.parentId) {
      // Refetch things because moving a chapter also moves it's descendants
      yield call(fetchHandbooks);
      yield put(replace(`/editor/${chapter.handbookId}/chapter/${result.id}/`));
    } else {
      // We are probably updating, navigate back after saving
      yield put(push(`/editor/${chapter.handbookId}/chapter/${result.id}/`));
    }

    yield put(toast.success('Oppdaterte kapitlet.'));
  } catch (error) {
    yield put(toast.error('Klarte ikke lagre kapitlet'));
  }
}

export function* deleteLocalChapter({ payload: chapter }) {
  try {
    yield call(api.deleteLocalChapter, chapter.id);
    // Refetch all the things cause we don't want to delete recursively
    yield call(fetchHandbooks);

    // If it is a root chapter, we navigate to the handbook,
    // otherwise we navigate to the parent of the deleted chapter
    const to = chapter.parentId
      ? `/editor/${chapter.handbookId}/chapter/${chapter.parentId}/`
      : `/editor/${chapter.handbookId}/`;
    yield put(actions.deleteLocalChapterSuccess(chapter.id));
    yield put(replace(to));

    yield put(toast.success('Slettet kapittel.', `Slettet: ${chapter.title}`));
  } catch (error) {
    yield put(toast.error('Det oppstod en feil under sletting av kapitlet.'));
  }
}

/**
 * Using the same saga for both creating, updating and moving to a new parent!
 * @param {Object} section
 */
export function* saveLocalSection({ payload: { section, centralChange, centralTextChange } }) {
  try {
    let result;
    let oldSection;

    // If we are updating, select the old version from the store so we can see if we have moved it.
    if (section.id) {
      [result, oldSection] = yield all([
        call(api.saveLocalSection, section, centralChange || 'local', centralTextChange || 'local'),
        select(selectSectionById(section.id))
      ]);
    } else {
      result = yield call(api.saveLocalSection, section, 'local', 'local');
    }

    // Notify the store
    yield put(actions.saveLocalSectionSuccess(result));

    // If this is a new section, navigate to it and return early
    if (!oldSection) {
      yield put(push(`/editor/${result.handbookId}/section/${result.id}/`));
      yield put(toast.success('Opprettet avsnitt.'));
      return;
    }

    // If we moved to a new parent, replace the url
    if (oldSection.parentId !== result.parentId) {
      yield put(replace(`/editor/${section.handbookId}/section/${result.id}/`));
    } else {
      // We are probably updating, navigate back after saving
      yield put(push(`/editor/${section.handbookId}/section/${result.id}/`));
    }
    yield put(toast.success('Oppdaterte avsnittet.'));
  } catch (error) {
    yield put(toast.error('Klarte ikke lagre avsnittet.'));
  }
}

export function* deleteLocalSection({ payload: section }) {
  try {
    yield call(api.deleteLocalSection, section.id);
    yield put(actions.deleteLocalSectionSuccess(section.id));
    yield put(replace(`/editor/${section.handbookId}/chapter/${section.parentId}/`));
    yield put(toast.success('Slettet avsnitt.', `Slettet: ${section.title}`));
  } catch (error) {
    yield put(toast.error('Det oppstod en feil under sletting av avsnittet.'));
  }
}

export function* subscribeToHandbook({ payload: { handbookId, bool } }) {
  try {
    yield call(api.subscribeToHandbook, handbookId, bool);
    yield put(actions.subscribeSuccess(handbookId, bool));
  } catch (error) {
    yield put(toast.error('Klarte ikke endre abonnering.'));
  }
}

export function* fetchSubscriptions() {
  try {
    const subscriptions = yield call(api.fetchSubscriptions);
    yield put(actions.fetchSubscriptionsSuccess(subscriptions));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente abonneringer'));
  }
}

export function* fetchLocalEditors({ payload: handbookId }) {
  try {
    const localEditors = yield call(api.fetchLocalEditors, handbookId);
    yield put(actions.fetchLocalEditorsSuccess(localEditors));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente redaktører for håndboken'));
  }
}

export function* deleteLocalEditor({ payload: localEditor }) {
  try {
    yield call(api.deleteLocalEditor, localEditor.id);
    yield put(actions.deleteLocalEditorSuccess(localEditor));
    yield put(
      toast.success('Lokal redaktørtilgang fjernet.', `Bruker: ${localEditor.rightsHolder}`)
    );
  } catch (error) {
    yield put(toast.error('Det oppstod en feil under sletting av lokal redaktør.'));
  }
}

export function* saveLocalEditor({ payload: localEditor }) {
  try {
    const insertedEditor = yield call(api.insertLocalEditor, localEditor);
    yield put(actions.saveLocalEditorSuccess(insertedEditor));
    yield put(
      toast.success('Lokal redaktørtilgang opprettet.', `Bruker: ${localEditor.rightsHolder}`)
    );
  } catch (error) {
    yield put(
      toast.error('Klarte ikke legge til redaktørtilgang.', `Bruker: ${localEditor.rightsHolder}`)
    );
  }
}

export function* fetchLocalEditorsForOrganization() {
  try {
    const id = yield select(selectExternalOrganizationId);
    const localEditors = yield call(fetchLocalEditorsForOrg, id);
    yield put(actions.fetchLocalEditorsForOrganizationSuccess(localEditors));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente redaktører for organisasjonen'));
  }
}

export default function* rootSaga() {
  yield all([
    takeLatest(actions.fetchLocalHandbooks, fetchHandbooks),
    takeLatest(actions.fetchSubscriptions, fetchSubscriptions),
    takeLatest(actions.fetchSection, fetchSection),
    takeLatest(actions.fetchChapter, fetchChapter),
    takeLatest(actions.fetchHandbook, fetchHandbook),

    takeLatest(actions.subscribe, subscribeToHandbook),
    takeLatest(actions.saveLocalHandbook, saveLocalHandbook),
    takeLatest(actions.deleteLocalHandbook, deleteLocalHandbook),

    takeLatest(actions.saveLocalChapter, saveLocalChapter),
    takeLatest(actions.deleteLocalChapter, deleteLocalChapter),

    takeLatest(actions.saveLocalSection, saveLocalSection),
    takeLatest(actions.deleteLocalSection, deleteLocalSection),

    takeLatest(actions.sortItems, sortItems),

    takeLatest(actions.fetchLocalEditors, fetchLocalEditors),
    takeEvery(actions.saveLocalEditor, saveLocalEditor),
    takeEvery(actions.deleteLocalEditor, deleteLocalEditor),

    takeLatest(actions.fetchLocalEditorsForOrganization, fetchLocalEditorsForOrganization)
  ]);
}
