import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { FormattedMessage } from 'react-intl';
import { toast } from 'kf-toaster';
import { Container, Section, Title, Subtitle, Select } from 'kf-bui';

import injectT from '../../../common/i18n';
import SelectCentralHandbooks from './components/SelectCentralHandbooks';
import { fetchPublishedCentralHandbooks } from '../CentralHandbookEditorPage/actions';
import { fetchCentralHandbooks as fetchCentralHandbooksWithAccess } from './actions';
import { fetchAccess, saveAccess } from './api';
import { selectSession } from '../App/selectors';
import { SessionShape } from '../../shapes';
import { selectCentralHandbooks as selectAllCentralHandbooks } from '../CentralHandbookEditorPage/selectors';

class CentralHandbooksPage extends React.Component {
  state = {
    externalOrganization: this.props.session.organization.id,
    access: [],
    isSaving: false
  };

  /**
   * Fetch the external organizations (with access to Håndbøker) and all central handbooks
   */
  componentDidMount() {
    const { fetchCentralHandbooksFun, fetchCentralHandbooksForOrganization } = this.props;
    const { externalOrganization } = this.state;

    fetchCentralHandbooksFun();
    // Force fetch for preselected org on mount
    this.handleExternalOrgChange(externalOrganization);
    fetchCentralHandbooksForOrganization();
  }

  /**
   * When we select an organization, we retrieve it's accesses
   */
  handleExternalOrgChange = externalOrgId => {
    fetchAccess(externalOrgId).then(access => this.setState({ access }));
    this.setState({ externalOrganization: externalOrgId });
  };

  /**
   * Saving the accesses for an external org
   */
  handleSave = ids => {
    this.setState({ isSaving: true });

    saveAccess(this.state.externalOrganization, ids).then(access => {
      const { session, fetchCentralHandbooksForOrganization, toastFun } = this.props;

      this.setState({ access, isSaving: false });
      // If we are altering the access for the organization
      // we are logged into, refetch that org's central handbooks
      if (session.organization.id === this.state.externalOrganization) {
        fetchCentralHandbooksForOrganization();
      }
      toastFun('Lagret tilgang for organisasjon.');
    });
  };

  render() {
    const { handbooks, t, session } = this.props;
    const { access, isSaving, externalOrganization } = this.state;

    document.title = `${t('title')}  - KF Håndbøker`;

    return (
      <Section>
        <Container>
          <Title>
            <FormattedMessage id="editor.containers.CentralHandbooksPage.title" />
          </Title>
          <Subtitle>
            <FormattedMessage id="editor.containers.CentralHandbooksPage.header" />
          </Subtitle>
          <Select
            aria-label={t('chooseOrganization')}
            onChange={this.handleExternalOrgChange}
            options={session.userOrgsWithAccess}
            value={externalOrganization}
            placeholder={t('chooseOrganization')}
            labelKey="name"
            valueKey="id"
            simpleValue
          />
          <hr />
          {externalOrganization && (
            <SelectCentralHandbooks
              handbooks={handbooks}
              access={access}
              isSaving={isSaving}
              onSave={this.handleSave}
            />
          )}
        </Container>
      </Section>
    );
  }
}

CentralHandbooksPage.propTypes = {
  handbooks: PropTypes.array.isRequired,
  fetchCentralHandbooksFun: PropTypes.func.isRequired,
  session: SessionShape.isRequired,
  fetchCentralHandbooksForOrganization: PropTypes.func.isRequired,
  toastFun: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

const mapStateToProps = state => ({
  handbooks: selectAllCentralHandbooks(state),
  session: selectSession(state)
});

const mapDispatchToProps = {
  fetchCentralHandbooksFun: fetchPublishedCentralHandbooks,
  fetchCentralHandbooksForOrganization: fetchCentralHandbooksWithAccess,
  toastFun: msg => toast.success(msg)
};

export default compose(
  injectT('editor.containers.CentralHandbooksPage'),
  connect(mapStateToProps, mapDispatchToProps)
)(CentralHandbooksPage);
