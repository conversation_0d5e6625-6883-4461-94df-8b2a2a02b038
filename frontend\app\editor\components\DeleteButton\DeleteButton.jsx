// @flow
import React from 'react';
import { Button } from 'kf-bui';
import { FormattedMessage } from 'react-intl';
import DeleteModal from './DeleteModal';
import BooleanToggler from '../BooleanToggler';

type Props = {
  t: (string, ?{}) => string,
  toDelete: { +id: string, +title: string, +type: string },
  onDelete: (id: string) => void,
  readlinkExists?: boolean
};

const DeleteButton = ({ onDelete, t, toDelete, readlinkExists = false }: Props) => (
  <BooleanToggler>
    {(toggle, value) => (
      <span>
        <Button
          key="button"
          onClick={toggle}
          icon="trash"
          control
          outlined
          color="danger"
          size="small"
        >
          {t('deleteButton')}
        </Button>
        {value ? (
          <DeleteModal
            key="modal"
            onHide={toggle}
            onDelete={onDelete}
            title={t('deleteTitle')}
            text={
              <div>
                <p>
                  {t('deleteQuestion', {
                    title: toDelete.title
                  })}
                </p>
                {toDelete.type !== 'LOCAL_SECTION' && toDelete.type !== 'SECTION' && (
                  <p>{t('deleteWarning')}</p>
                )}
                {toDelete.type === 'HANDBOOK' && (
                  <p>
                    <b>MERK! </b>
                    <FormattedMessage id="editor.containers.DeleteButton.deleteLocalWarning" />
                  </p>
                )}
                {readlinkExists ? (
                  <p>
                    <b>OBS! </b>
                    <FormattedMessage id="editor.containers.DeleteButton.readLinkDeleteWarning" />
                  </p>
                ) : null}
              </div>
            }
          />
        ) : null}
      </span>
    )}
  </BooleanToggler>
);

DeleteButton.defaultProps = {
  readlinkExists: false
};

export default DeleteButton;
