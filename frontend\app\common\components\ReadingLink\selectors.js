// @flow

import { createSelector } from 'reselect';
import type { ReadingLinkType, StateType } from '../../../types';

const selectReadingLinkState = (state: StateType): { [string]: ReadingLinkType } =>
  state.readingLinks;

export const selectReadingLinksMap = createSelector(
  selectReadingLinkState,
  readingLinkState => readingLinkState
);

export const makeSelectReadingLink = (centralSectionId: string) =>
  createSelector(selectReadingLinkState, readingLinks => readingLinks[centralSectionId]);
