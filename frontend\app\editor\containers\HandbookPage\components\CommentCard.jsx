// @flow
import React, { useState, Fragment } from 'react';
import { Card, Icon, FormattedDate } from 'kf-bui';
import { FormattedMessage } from 'react-intl';

export type Comment = {
  id: string,
  text: string,
  editedBy: string,
  editedDate: Date,
  handbookId: string
};
type Props = {
  comment: Comment,
  deleteFunction: (id: string) => void,
  saveFunction: (text: string) => Promise<void>
};

const CommentCard = ({ comment, deleteFunction, saveFunction }: Props) => {
  const [editComment, setEditComment] = useState<boolean>(false);
  const [newComment, setNewComment] = useState<string>(comment.text);

  return (
    <Card className="comment">
      <Card.Header>
        <div>{comment.editedBy}</div>
        <FormattedDate
          id="editedDate"
          name="editedDate"
          value={comment.editedDate}
          format="DD.MM.YYYY - HH:mm"
          style={{ float: 'right', marginRight: '5px' }}
        />
      </Card.Header>
      <Card.Content style={{ padding: '0' }}>
        {editComment ? (
          <textarea
            id={`kommentar-${comment.id}`}
            aria-label="Endre kommentaren"
            value={newComment}
            onChange={e => setNewComment(e.target.value)}
            style={{ display: 'block', width: '100%', height: '5em', padding: '5px' }}
          />
        ) : (
          <div style={{ padding: '5px', minHeight: '4em', whiteSpace: 'pre-wrap' }}>
            {comment.text}
          </div>
        )}
      </Card.Content>

      <Card.Footer>
        {editComment ? (
          <Fragment>
            <Card.Footer.Item as="a" role="button" onClick={() => setEditComment(v => !v)}>
              <Icon icon="edit" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.cancelButton" />
              </span>
            </Card.Footer.Item>
            <Card.Footer.Item
              as="a"
              role="button"
              size="small"
              onClick={() => saveFunction(newComment).then(() => setEditComment(false))}
            >
              <Icon icon="save" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.saveButton" />
              </span>
            </Card.Footer.Item>
          </Fragment>
        ) : (
          <Fragment>
            <Card.Footer.Item
              as="a"
              role="button"
              size="small"
              onClick={() => setEditComment(v => !v)}
            >
              <Icon icon="edit" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentCard.editButton" />
              </span>
            </Card.Footer.Item>
            <Card.Footer.Item as="a" role="button" onClick={() => deleteFunction(comment.id)}>
              <Icon icon="times" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentCard.deleteButton" />
              </span>
            </Card.Footer.Item>
          </Fragment>
        )}
      </Card.Footer>
    </Card>
  );
};

export default CommentCard;
