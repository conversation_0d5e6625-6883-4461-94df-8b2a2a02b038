import { takeLatest, all, select, put, call } from 'redux-saga/effects';
import { toast } from 'kf-toaster';
import { selectQuery } from './selectors';
import { selectHandbook } from '../HandbookPage/selectors';
import getSearchResults from './api';
import * as actions from './actions';

export function* search({ payload: page = 1 }) {
  try {
    const [query, handbook] = yield all([select(selectQuery), select(selectHandbook)]);
    if (query) {
      const res = yield call(
        getSearchResults,
        handbook.externalOrgId,
        handbook.id,
        query.toLowerCase(),
        page,
        handbook.title
      );
      yield put(actions.searchSuccess(res));
    }
  } catch (error) {
    yield put(toast.error('En feil oppstod under søk'));
  }
}

export default function* rootSaga() {
  yield takeLatest(actions.search, search);
}
