import { createSelector } from 'reselect';
import { clone, groupBy } from 'lodash';
import { sortBy, sortBySortOrderAndTitle } from '../../../common/util';

const selectState = state => state.handbooks;

/**
 * Returns object of handbooks keyed by id
 */
export const selectHandbookEntities = state => selectState(state).handbooks;

/**
 * Returns object of chapters keyed by id
 */
export const selectChapterEntities = state => selectState(state).chapters;

/**
 * Returns object of sections keyed by id
 */
export const selectSectionEntities = state => selectState(state).sections;

export const selectHandbooks = createSelector(selectHandbookEntities, handbooks =>
  Object.values(handbooks).sort(sortBy('title'))
);

export const selectChapters = createSelector(selectChapterEntities, chapters =>
  Object.values(chapters)
);

export const selectSections = createSelector(selectSectionEntities, sections =>
  Object.values(sections)
);

/**
 * @param {String} id
 */
export const selectChapterById = id => state => selectChapterEntities(state)[id];

/**
 * @param {String} id
 */
export const selectSectionById = id => state => selectSectionEntities(state)[id];

/**
 * Selects the current subscriptions (handbookIds)
 */
export const selectSubscriptions = state => selectState(state).subscriptions;

/**
 * Selects the local editors for the organization
 */
export const selectLocalEditorsForOrg = state => selectState(state).localEditorsForOrg;

/**
 * Selects the local editors for the handbook
 */
export const selectLocalEditors = state => selectState(state).localEditors;

/**
 * Select the (sorted) root chapters for a handbook
 * @param {String} handbookId
 */
export const selectRootChapters = handbookId =>
  createSelector(selectChapters, chapters =>
    chapters.filter(c => c.handbookId === handbookId && !c.parentId).sort(sortBySortOrderAndTitle)
  );

/**
 * Select the (sorted) sub chapters and sections of a parent
 * @param {String} handbookId
 * @param {String} parentId
 */
export const selectSubChaptersAndSections = parentId =>
  createSelector(selectChapters, selectSections, (chapters, sections) =>
    [...chapters, ...sections].filter(c => c.parentId === parentId).sort(sortBySortOrderAndTitle)
  );

/**
 * Selects the count of pending changes
 */
export const selectPendingCount = createSelector(
  selectHandbooks,
  selectChapters,
  selectSections,
  (handbooks, chapters, sections) =>
    handbooks.filter(h => h.pendingChange).length +
    chapters.filter(c => c.pendingChange || c.pendingDeletion).length +
    sections.filter(s => s.pendingTitleChange || s.pendingTextChange || s.pendingDeletion).length
);

/**
 * Selects the items with pending changes, grouped by handbook
 */
export const selectPending = createSelector(
  selectHandbooks,
  selectChapters,
  selectSections,
  (handbooks, chapters, sections) => {
    // Group sections and chapter by handbook
    const groupedSections = groupBy(
      sections.filter(s => s.pendingTitleChange || s.pendingTextChange || s.pendingDeletion),
      'handbookId'
    );
    const groupedChapters = groupBy(
      chapters.filter(c => c.pendingChange || c.pendingDeletion),
      'handbookId'
    );

    const grouped = handbooks
      .map(h => {
        const handbook = clone(h);
        handbook.sections = groupedSections[handbook.id] || [];
        handbook.chapters = groupedChapters[handbook.id] || [];
        return handbook;
      })
      .sort(sortBy('title'));

    return grouped.filter(h => h.pendingChange || h.sections.length || h.chapters.length);
  }
);
