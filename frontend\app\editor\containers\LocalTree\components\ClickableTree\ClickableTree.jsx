import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';

import { Card, Tree } from 'kf-bui';

import injectT from '../../../../../common/i18n';
import HandbookNode from './ClickableHandbookNode';
import { ChapterShape, HandbookShape, SectionShape } from '../../../../shapes';
import {
  selectHandbooks,
  selectChapterEntities,
  selectSectionEntities
} from '../../../EditorPage/selectors';
import { selectElement } from '../../actions';

/**
 * Instead of creating a tree that supports both urls and clicking, we break the DRY principle
 */

class ClickableTree extends React.PureComponent {
  renderTree() {
    const { handbooks, activeChapter, activeSection, selectElementFun } = this.props;

    const items = handbooks.map(handbook => (
      <HandbookNode
        handbook={handbook}
        key={handbook.id}
        activeChapter={activeChapter}
        activeSection={activeSection}
        selectElement={selectElementFun}
      />
    ));

    return <Tree>{items}</Tree>;
  }

  render() {
    const { t } = this.props;
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t('treeHeader')}</Card.Title>
        </Card.Header>
        <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>{this.renderTree()}</Card.Content>
      </Card>
    );
  }
}

ClickableTree.propTypes = {
  handbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  selectElementFun: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,

  activeChapter: ChapterShape,
  activeSection: SectionShape
};

ClickableTree.defaultProps = {
  activeChapter: null,
  activeSection: null
};

const mapStateToProps = (state, ownProps) => ({
  handbooks: selectHandbooks(state),
  activeChapter: selectChapterEntities(state)[ownProps.match.params.id],
  activeSection: selectSectionEntities(state)[ownProps.match.params.id]
});

export default compose(
  injectT('editor.containers.LocalTree'),
  connect(mapStateToProps, { selectElementFun: selectElement })
)(ClickableTree);
