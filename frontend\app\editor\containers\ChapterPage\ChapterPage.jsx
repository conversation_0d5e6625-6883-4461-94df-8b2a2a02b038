import React from 'react';
import PropTypes from 'prop-types';
import { Route, Switch } from 'react-router-dom';
import ChapterScreen from './components/ChapterScreen';
import EditChapterScreen from './components/EditChapterScreen';
import MoveChapterOrSection from '../MoveChapterOrSection';

const ChapterPage = ({ match }) => (
  <Switch>
    <Route path={`${match.url}/add-new`} component={EditChapterScreen} />
    <Route path={`${match.url}/:chapterId/edit`} component={EditChapterScreen} />
    <Route path={`${match.url}/:chapterId/move`} component={MoveChapterOrSection} />
    <Route path={`${match.url}/:chapterId`} component={ChapterScreen} />
  </Switch>
);

ChapterPage.propTypes = {
  match: PropTypes.shape({
    url: PropTypes.string.isRequired,
    path: PropTypes.string.isRequired,
    params: PropTypes.shape({
      handbookId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired
};

export default ChapterPage;
