import React from 'react';
import PropTypes from 'prop-types';
import { Section, Container } from 'kf-bui';
import { Route, Switch, Redirect } from 'react-router-dom';

import injectT from '../../../common/i18n';
import MergeSection from './components/MergeSection';
import MergeHandbookOrChapter from './components/MergeHandbookOrChapter';

const MergePage = ({ match, t }) => {
  document.title = `${t('title')}  - KF <PERSON>`;
  return (
    <Section>
      <Container>
        <Switch>
          <Route path={`${match.path}/handbook/:handbookId`} component={MergeHandbookOrChapter} />
          <Route path={`${match.path}/chapter/:chapterId`} component={MergeHandbookOrChapter} />
          <Route path={`${match.path}/section/:sectionId`} component={MergeSection} />
          <Redirect to="404" />
        </Switch>
      </Container>
    </Section>
  );
};

MergePage.propTypes = {
  t: PropTypes.func.isRequired,
  match: PropTypes.shape({
    path: PropTypes.string.isRequired
  }).isRequired
};

export default injectT('editor.containers.MergePage')(MergePage);
