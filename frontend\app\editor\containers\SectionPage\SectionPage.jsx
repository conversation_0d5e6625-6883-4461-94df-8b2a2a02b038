import React from 'react';
import PropTypes from 'prop-types';
import { Route, Switch } from 'react-router-dom';
import SectionScreen from './components/SectionScreen';
import EditSectionScreen from './components/EditSectionScreen';
import MoveChapterOrSection from '../MoveChapterOrSection';

const SectionPage = ({ match }) => (
  <Switch>
    <Route path={`${match.url}/add-new`} component={EditSectionScreen} />
    <Route path={`${match.url}/:sectionId/edit`} component={EditSectionScreen} />
    <Route path={`${match.url}/:sectionId/move`} component={MoveChapterOrSection} />
    <Route path={`${match.url}/:sectionId`} component={SectionScreen} />
  </Switch>
);

SectionPage.propTypes = {
  match: PropTypes.shape({
    url: PropTypes.string.isRequired,
    path: PropTypes.string.isRequired,
    params: PropTypes.shape({
      handbookId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired
};

export default SectionPage;
