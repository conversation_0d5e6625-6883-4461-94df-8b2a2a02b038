import React, { useCallback, useEffect, useState, useRef } from 'react';
import { Button, Icon, Modal, Title } from 'kf-bui';
import { useDropzone } from 'react-dropzone';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import moment from 'moment';
import { toast } from 'kf-toaster';
import { connect } from 'react-redux';
import classNames from 'classnames';
import './styles.css';
import axios from 'axios';
import { bytesToKB, formatFileSize, formatKilobytes } from '../../util/form';
import { WarningIcon } from './icons';
import { deleteFile, fetchFile, fetchHandbookFileLinksByType, saveHandbookFileLinks } from './api';
import injectT from '../../../common/i18n';
import CircularProgress from './CircularProgress';

const MAX_FILE_COUNT = 10;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_FILE_TYPES = '.docx, .xlsx, .pdf, .pptx, .png, .jpg';
const DATE_FORMAT = 'DD.MM.YYYY';

const Attachments = ({
  onClose,
  t,
  sectionId,
  sectionType,
  errorToast,
  successToast,
  handleAttachmentCount
}) => {
  const fileListRef = useRef(null);

  const [files, setFiles] = useState([]);
  const [uploadedFilesData, setUploadedFilesData] = useState([]);
  const [oversizedFiles, setOversizedFiles] = useState([]);
  const [removedFileIds, setRemovedFileIds] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [isPublishDisabled, setIsPublishDisabled] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);

  const onDrop = useCallback(
    async acceptedFiles => {
      const currentFileCount = files.length;
      const validFiles = [];
      const newOversizedFiles = [];
      setErrorMessage('');

      acceptedFiles.forEach(file => {
        if (file.size > MAX_FILE_SIZE) {
          newOversizedFiles.push({
            ...file,
            title: file.name,
            status: 'oversized'
          });
        } else {
          validFiles.push(file);
        }
      });

      if (currentFileCount + validFiles.length > MAX_FILE_COUNT) {
        setErrorMessage(t('uploadLimitReached'));
        return;
      }

      validFiles.forEach((file, index) => {
        const formData = new FormData();
        formData.append('file', file);

        axios
          .post('/files', formData, {
            headers: { 'Content-Type': 'multipart/form-data' },
            onUploadProgress: progressEvent => {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );

              setFiles(prevFiles => {
                const updatedFiles = [...prevFiles];
                updatedFiles[currentFileCount + index] = {
                  ...file,
                  progress: percentCompleted,
                  status: 'uploading',
                  title: file.name
                };
                return updatedFiles;
              });
            }
          })
          .then(response => {
            const { data } = response;
            const fileSize = formatFileSize(file.size);

            const currentValidFileCount = files.filter(
              validFile => validFile.status !== 'oversized'
            ).length;

            const getSortOrder = () => {
              if (currentFileCount === 0) {
                return 1;
              }
              if (currentFileCount !== 0 && index === 0) {
                return currentValidFileCount + 1;
              }
              return currentValidFileCount + index;
            };

            const fileDetails = {
              id: data.location,
              title: file.name,
              url: data.location,
              fileSize,
              size: file.size,
              sortOrder: getSortOrder()
            };

            setFiles(prevFiles => {
              const updatedFiles = [...prevFiles];
              updatedFiles[currentFileCount + index] = {
                ...file,
                ...fileDetails,
                status: 'uploaded'
              };
              return updatedFiles;
            });

            setUploadedFilesData(prevData => [...prevData, fileDetails]);
          })
          .catch(error => {
            console.error('Error uploading file:', file.name, error);
            errorToast('Feil ved opplasting av fil:', file.name);

            setFiles(prevFiles => {
              const updatedFiles = [...prevFiles];
              updatedFiles[currentFileCount + index] = {
                ...file,
                status: 'failed'
              };
              return updatedFiles;
            });
          });
      });

      setFiles(prevFiles => [...prevFiles, ...validFiles, ...newOversizedFiles]);
      setOversizedFiles(prevOversizedFiles => [...prevOversizedFiles, ...newOversizedFiles]);

      setTimeout(() => {
        if (fileListRef.current) {
          fileListRef.current.scrollTo({
            top: fileListRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 200);
    },
    [files]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxFiles: MAX_FILE_COUNT
  });

  const handleRemoveFile = async fileToRemove => {
    setFiles(files.filter(file => file !== fileToRemove));

    setUploadedFilesData(prevData => prevData.filter(data => data.title !== fileToRemove.title));

    setOversizedFiles(prevOversizedFiles =>
      prevOversizedFiles.filter(file => file !== fileToRemove)
    );

    if (!fileToRemove.belongsTo) {
      try {
        const fileName = fileToRemove.id.split('/').pop();
        await deleteFile(fileName);
      } catch (error) {
        console.error('Error deleting file:', fileToRemove.name, error);
      }
    } else {
      setRemovedFileIds(prevIds => [...prevIds, fileToRemove.id]);
    }
  };

  const handlePublishUploadedFiles = async () => {
    try {
      setIsSaving(true);

      const prevUploadedFilesDataLength = uploadedFilesData.length;
      const prevRemovedFileIdsLength = removedFileIds.length;

      const payload = {
        belongsTo: sectionType,
        ownerId: sectionId,
        newlyAdded: uploadedFilesData.map(file => ({
          title: file.title,
          url: file.url,
          sortOrder: file.sortOrder,
          size: bytesToKB(file.size)
        })),
        removed: removedFileIds
      };

      const data = await saveHandbookFileLinks(payload);
      data.map(file => {
        const newFile = file;
        newFile.status = 'uploaded';
        newFile.fileSize = formatKilobytes(file.size);
        return newFile;
      });
      setFiles(data);
      setUploadedFilesData([]);
      setRemovedFileIds([]);
      setOversizedFiles([]);
      setIsSaving(false);

      if (prevUploadedFilesDataLength === 0 && prevRemovedFileIdsLength > 0) {
        successToast(t('attachmentsRemovedSuccessfully'));
      } else if (prevUploadedFilesDataLength > 0 && prevRemovedFileIdsLength === 0) {
        successToast(t('attachmentsAddedSuccessfully'));
      } else if (prevUploadedFilesDataLength > 0 && prevRemovedFileIdsLength > 0) {
        successToast(t('attachmentsUpdatedSuccessfully'));
      }

      handleAttachmentCount();
      setTimeout(() => {
        onClose();
      }, 500);
    } catch (error) {
      setIsSaving(false);
      console.error('Error saving file links:', error);
    }
  };

  const handleGetFile = async (fileId, fileName) => {
    try {
      const data = await fetchFile(fileId);
      const blob = new Blob([data]);
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error getting file:', error);
    }
  };

  const handleOnClose = async () => {
    // Clean up unpublished files
    if (uploadedFilesData.length > 0) {
      setIsCleaning(true);
      try {
        const deletePromises = uploadedFilesData.map(async fileData => {
          const fileName = fileData.id.split('/').pop();
          await deleteFile(fileName);
        });

        await Promise.all(deletePromises);
      } catch (error) {
        console.error('Error cleaning up files:', error);
      } finally {
        setIsCleaning(false);
      }
    }

    onClose();
  };

  useEffect(() => {
    const fetchFileData = async () => {
      try {
        setIsLoading(true);
        const data = await fetchHandbookFileLinksByType(sectionType.toLowerCase(), sectionId);
        if (data.length) {
          data.map(file => {
            const newFile = file;
            newFile.status = 'uploaded';
            newFile.fileSize = formatKilobytes(file.size);
            return newFile;
          });
        }
        setFiles(data);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
        errorToast('Error fetching files');
      }
    };
    fetchFileData();
  }, []);

  useEffect(() => {
    setIsPublishDisabled(uploadedFilesData.length === 0 && removedFileIds.length === 0);
  }, [uploadedFilesData, removedFileIds]);

  return (
    <Modal className="attachment-modal">
      <Modal.Body>
        <Title>{t('attachmentModalTitle')}</Title>
        {files.length < MAX_FILE_COUNT && (
          <div className="file-upload-wrapper">
            <div {...getRootProps()} className={`drag-drop-area ${isDragActive ? 'active' : ''}`}>
              <input {...getInputProps()} />
              <p>{t('dragAndDrop')}</p>
              <p className="or-text">{t('or')}</p>
              <button type="button" className="upload-link">
                {t('clickToAdd')}
              </button>
              <p>
                {t('accestedFileTypes')}: {ACCEPTED_FILE_TYPES}
              </p>
              <p>{`${t('max')} 5MB og maks ${MAX_FILE_COUNT} ${t('attachmentForSection')}`}</p>
            </div>
          </div>
        )}

        {files.length >= MAX_FILE_COUNT && (
          <div className="error-message-wrapper">
            <WarningIcon />
            <p className="error-message">{t('uploadLimitReached')}</p>
          </div>
        )}
        {errorMessage && (
          <div className="error-message-wrapper">
            <WarningIcon />
            <p className="error-message">{errorMessage}</p>
          </div>
        )}

        <div className="file-list-title">
          <h3>{t('addAttachment')}</h3>
        </div>

        <div className="file-list" ref={fileListRef}>
          {isLoading ? (
            <div className="attachment-loading-message">
              <span className="loader" />
              <p>Laster vedlegg. Vennligst vent...</p>
            </div>
          ) : (
            files
              .sort((a, b) => a.sortOrder - b.sortOrder)
              .map(file => (
                <div
                  key={file.id}
                  className={classNames(
                    'file-item',
                    oversizedFiles.includes(file) ? 'oversized-file' : '',
                    !file.belongsTo && !oversizedFiles.includes(file) ? 'new-item' : ''
                  )}
                >
                  <div
                    className="attachment-file-details"
                    onClick={() =>
                      !oversizedFiles.includes(file) &&
                      handleGetFile(file.url.split('/').pop(), file.title)
                    }
                    onKeyDown={e => {
                      if ((e.key === 'Enter' || e.key === ' ') && !oversizedFiles.includes(file)) {
                        handleGetFile(file.url.split('/').pop(), file.title);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                  >
                    <h4 className="attachment-file-name">
                      {file.title} {file.fileSize && `(${file.fileSize})`}
                    </h4>

                    {oversizedFiles.includes(file) && (
                      <div className="file-oversized-error-wrapper">
                        <WarningIcon />
                        <h4 className="file-oversized-error">{t('fileOversized')}</h4>
                      </div>
                    )}

                    {file.status === 'failed' && (
                      <div className="file-oversized-error-wrapper">
                        <WarningIcon />
                        <h4 className="file-oversized-error">
                          Opplasting mislyktes. Prøv å laste opp igjen.
                        </h4>
                      </div>
                    )}

                    {!oversizedFiles.includes(file) && file.status === 'uploaded' && (
                      <h4 className="file-more-info">
                        {file.updatedBy
                          ? `${t('uploadedBy')}: ${file.updatedBy ? file.updatedBy : ''},
                          ${moment(file.updatedDate).format(DATE_FORMAT)}`
                          : t('readyToPublish')}
                      </h4>
                    )}

                    {!oversizedFiles.includes(file) && file.status === 'uploading' && (
                      <p className="attachment-uploading-progress">Vedlegg lastes opp</p>
                    )}
                  </div>

                  {oversizedFiles.includes(file) ||
                  file.status === 'uploaded' ||
                  file.status === 'failed' ? (
                    <button
                      type="button"
                      className="remove-file-button"
                      onClick={() => handleRemoveFile(file)}
                    >
                      <Icon icon="close" size="small" />
                    </button>
                  ) : (
                    <div className="attachment-upload-percentage">
                      <CircularProgress percentage={file.progress || 0} />
                    </div>
                  )}
                </div>
              ))
          )}
        </div>

        <div className="attachments-action-wrapper">
          <Button color="primary" onClick={handleOnClose} loading={isCleaning}>
            {t('cancel')}
          </Button>
          <Button
            className="attachment-publish-btn"
            onClick={handlePublishUploadedFiles}
            disabled={isPublishDisabled}
            loading={isSaving}
          >
            {t('publish')}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

Attachments.propTypes = {
  onClose: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  sectionId: PropTypes.string.isRequired,
  sectionType: PropTypes.string.isRequired,
  errorToast: PropTypes.func.isRequired,
  successToast: PropTypes.func.isRequired,
  handleAttachmentCount: PropTypes.func.isRequired
};

export default compose(
  injectT('editor.containers'),
  connect(() => ({}), {
    errorToast: toast.error,
    successToast: toast.success
  })
)(Attachments);
