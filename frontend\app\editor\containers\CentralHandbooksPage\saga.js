import { call, put, takeLatest } from 'redux-saga/effects';
import { toast } from 'kf-toaster';

import * as api from './api';
import * as actions from './actions';

export function* fetchCentralHandbooks() {
  try {
    const res = yield call(api.fetchAllCentralHandbooks);
    yield put(actions.fetchCentralHandbooksSuccess(res));
  } catch (error) {
    yield put(toast.error('En ukjent feil har oppstått'));
  }
}

export default function* rootSaga() {
  yield takeLatest(actions.fetchCentralHandbooks, fetchCentralHandbooks);
}
