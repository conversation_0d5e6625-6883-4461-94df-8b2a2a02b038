/* eslint-env jest */
import reducer, { initialState } from '../reducer';
import {
  fetchHandbookContentSuccess,
  fetchHandbooksSuccess,
  fetchHandbookContent
} from '../actions';
import { setOrganizationSuccess } from '../../App/actions';

describe('centralHandbookReducer', () => {
  let state;

  beforeEach(() => {
    state = initialState;
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should return the initial state when changing the external org', () => {
    expect(reducer(undefined, setOrganizationSuccess('9900'))).toEqual(initialState);
  });

  it('should handle select handbook success', () => {
    const handbook = { id: '1', title: 'HMS' };

    const action = fetchHandbookContent(handbook);
    const expectedResult = {
      ...state,
      selectedHandbook: handbook
    };

    expect(reducer(state, action)).toEqual(expectedResult);
  });

  it('should handle fetch handbooks success', () => {
    const handbooks = [
      { id: '1', title: 'HMS' },
      { id: '2', title: 'Avvik' }
    ];

    const next = reducer(state, fetchHandbooksSuccess(handbooks));
    expect(next).toMatchSnapshot();
  });

  it('should handle fetch handbook content success', () => {
    const handbookId1 = '1';
    const handbookId2 = '2';
    const chapters = [
      { id: '1.1', title: 'Arbeidsgiverpolitikk' },
      { id: '1.2', title: 'Permisjon' }
    ];
    const sections = [
      { id: '1.1.1', title: 'Varsling' },
      { id: '1.1.2', title: 'Etiske retningslinjer' }
    ];

    let next = reducer(
      state,
      fetchHandbookContentSuccess({
        handbookId: handbookId1,
        chapters,
        sections
      })
    );

    expect(next).toMatchSnapshot();

    // We go again, to make sure we add chapters and sections,
    // keyed by handbookId, instead of overwriting
    next = reducer(
      next,
      fetchHandbookContentSuccess({
        handbookId: handbookId2,
        chapters,
        sections
      })
    );
    expect(next).toMatchSnapshot();
  });
});
