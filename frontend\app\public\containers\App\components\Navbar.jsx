import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Route, Switch, Link, withRouter } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import { compose } from 'redux';
import {
  Nav,
  Container,
  Input,
  Hero,
  Title,
  Subtitle,
  Columns,
  Column,
  ImageContainer,
  Tabs,
  ImageHero
} from 'kf-bui';
import styled from 'styled-components';
import { HandbookShape } from '../../../shapes';
import { selectQuery } from '../../SearchPage/selectors';
import { search, searchQueryChange } from '../../SearchPage/actions';
import DropDownLinkItems from './DropDownLinkItems';

const GrowingHero = styled(ImageHero)`
  .container {
    height: unset !important;
  }
  .hero-body {
    height: unset !important;
  }
  .hero-foot {
    height: unset !important;
  }
  height: unset !important;
`;

class Navbar extends React.Component {
  onSearch = event => {
    event.preventDefault();
    const { handbook, history, query } = this.props;
    // Only search if we have a query string
    if (query) {
      history.push(`/${handbook.externalOrgId}/${handbook.id}/search`);
      this.props.search();
    }
  };

  onQueryChange = event => {
    this.props.searchQueryChange(event.target.value);
  };

  render() {
    const {
      handbook,
      organization,
      query,
      linkCollections,
      location,
      bannerUrl,
      logoUrl
    } = this.props;

    return (
      <GrowingHero color="info" imageUrl={bannerUrl}>
        <Container>
          {handbook ? (
            <>
              <Switch>
                <Route
                  path="/:externalOrgId/:handbookId"
                  render={() => (
                    <Hero.Body>
                      <Columns vCentered>
                        {logoUrl && (
                          <Column narrow paddingLess>
                            <ImageContainer dimension="48x48">
                              <img src={logoUrl} alt="Organisasjonslogo" />
                            </ImageContainer>
                          </Column>
                        )}
                        <Column>
                          {handbook && (
                            <Title style={{ marginBottom: '4px' }}>{handbook.title}</Title>
                          )}
                          {organization && (
                            <Subtitle style={{ marginTop: '0px' }}>{organization.name}</Subtitle>
                          )}
                        </Column>
                        <Column as="form" onSubmit={this.onSearch}>
                          <Input
                            aria-label="Søk"
                            placeholder="Søk"
                            size="medium"
                            iconLeft="search"
                            iconSize="small"
                            value={query}
                            onChange={this.onQueryChange}
                            type="search"
                          />
                        </Column>
                      </Columns>
                    </Hero.Body>
                  )}
                />
                <Route
                  render={() => (
                    <Hero.Header>
                      <Nav shadow>
                        <Container>
                          <Nav.Brand>
                            <Nav.Item brand>
                              <FormattedMessage id="public.components.Header.brand" />
                            </Nav.Item>
                          </Nav.Brand>
                        </Container>
                      </Nav>
                    </Hero.Header>
                  )}
                />
              </Switch>

              <Route
                path="/:externalOrgId/:handbookId"
                render={({ match }) => {
                  const searchIsActive = `${match.url}/search` === location.pathname;
                  return (
                    <Hero.Footer>
                      <Tabs
                        boxed
                        centered
                        style={{
                          overflow: 'visible',
                          overflowX: 'visible',
                          display: 'inline-block'
                        }}
                      >
                        <Tabs.Tab active={!searchIsActive} style={{ overflow: 'hidden' }}>
                          <Link to={match.url}>Håndbok</Link>
                        </Tabs.Tab>
                        <Tabs.Tab active={searchIsActive} style={{ overflow: 'hidden' }}>
                          <Link to={`${match.url}/search`}>Søkeresultat</Link>
                        </Tabs.Tab>
                        <Tabs.Tab>
                          <DropDownLinkItems linkCollections={linkCollections} />
                        </Tabs.Tab>
                      </Tabs>
                    </Hero.Footer>
                  );
                }}
              />
            </>
          ) : (
            <Hero.Body>
              <Columns vCentered>
                {logoUrl && (
                  <Column narrow paddingLess>
                    <ImageContainer dimension="48x48">
                      <img src={logoUrl} alt="Organisasjonslogo" />
                    </ImageContainer>
                  </Column>
                )}
              </Columns>
            </Hero.Body>
          )}
        </Container>
      </GrowingHero>
    );
  }
}

Navbar.propTypes = {
  logoUrl: PropTypes.string,
  organization: PropTypes.shape({
    name: PropTypes.string
  }),
  handbook: HandbookShape,
  searchQueryChange: PropTypes.func.isRequired,
  search: PropTypes.func.isRequired,
  query: PropTypes.string.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func.isRequired
  }).isRequired,
  linkCollections: PropTypes.array,
  location: PropTypes.shape({
    pathname: PropTypes.string.isRequired
  }).isRequired,
  bannerUrl: PropTypes.string
};

Navbar.defaultProps = {
  organization: null,
  handbook: null,
  logoUrl: null,
  linkCollections: [],
  bannerUrl: undefined
};

export default compose(
  withRouter,
  connect(
    state => ({
      query: selectQuery(state)
    }),
    { search, searchQueryChange }
  )
)(Navbar);
