// @flow

import React from 'react';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withFormik } from 'formik';

import { Button, Modal, Field, Input, Label, Form } from 'kf-bui';

import { saveCentralHandbook } from './actions';

import injectT from '../../../common/i18n';
import type { CentralHandbookType } from '../../../types';

type Props = {
  centralHandbook: ?CentralHandbookType,
  // eslint-disable-next-line react/no-unused-prop-types
  saveCentralHandbook: string => void, // defined and used in connect
  onHide: () => void,
  t: string => string
};

type FormProps = {
  handleSubmit: (any, props: Props) => void,
  handleChange: (event: Event) => void,
  errors: { [string]: string },
  values: { [string]: string }
};

type State = {
  isSaving: boolean
};

class CentralHandbookModal extends React.Component<Props & FormProps, State> {
  state = {
    isSaving: false
  };

  render() {
    const { centralHandbook, t, handleChange, handleSubmit, onHide, values } = this.props;
    const { isSaving } = this.state;

    // While we are submitting, the onHide is a noop
    // so we can't close the modal until the server has responded
    const toggleHide = isSaving ? () => {} : onHide;

    return (
      <Modal onClose={toggleHide} autoFocus={false}>
        <Form onSubmit={handleSubmit}>
          <Modal.Header onClose={toggleHide}>
            <Modal.Title>{centralHandbook ? t('editTitle') : t('createTitle')}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Field>
              <Label htmlFor="title">Tittel</Label>
              <Input
                id="title"
                name="title"
                placeholder="Tittel"
                autoFocus
                onChange={handleChange}
                value={values.title}
                required
              />
            </Field>
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={toggleHide}>Avbryt</Button>
            <Button loading={isSaving} type="submit" color="success">
              {t('saveButton')}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    );
  }
}

export default compose(
  injectT('editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm'),
  connect(null, { saveCentralHandbook }),
  withFormik({
    enableReinitialize: true,
    mapPropsToValues: props => ({
      title: props.centralHandbook ? props.centralHandbook.title : ''
    }),
    validate: values => {
      const errors = {};
      if (!values.title) errors.title = 'Håndboka må ha en tittel';
      return errors;
    },
    handleSubmit: (values, { props }) => {
      const { title } = values;
      const handbook =
        props.centralHandbook !== undefined
          ? Object.assign(props.centralHandbook, { title })
          : { title };
      props.saveCentralHandbook(handbook);
      props.onHide();
    }
  })
)(CentralHandbookModal);
