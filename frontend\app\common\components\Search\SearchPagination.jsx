import React from 'react';
import PropTypes from 'prop-types';
import { Pagination } from 'kf-bui';
import { SearchResultShape } from '../../shapes';

/* eslint-disable no-mixed-operators */
// TODO: Remove after fixing prettier and eslint conflict

const MAX_BUTTONS = 5;

const SearchPagination = ({ onPageClick, result }) => {
  const { pageSize, totalHits, page: activePage } = result;
  if (!(pageSize < totalHits)) {
    return null;
  }

  const items = Math.ceil(totalHits / pageSize);
  let startPage;
  let endPage;
  let hasHiddenPagesAfter;
  const pageButtons = [];

  if (MAX_BUTTONS) {
    const hiddenPagesBefore = activePage - parseInt(MAX_BUTTONS / 2, 10);
    startPage = hiddenPagesBefore > 1 ? hiddenPagesBefore : 1;
    hasHiddenPagesAfter = startPage + MAX_BUTTONS <= items;

    if (!hasHiddenPagesAfter) {
      endPage = items;
      startPage = items - MAX_BUTTONS + 1;
      if (startPage < 1) {
        startPage = 1;
      }
    } else {
      endPage = startPage + MAX_BUTTONS - 1;
    }
  } else {
    startPage = 1;
    endPage = items;
  }

  for (let pageNumber = startPage; pageNumber <= endPage; pageNumber += 1) {
    pageButtons.push(
      <Pagination.Item
        key={pageNumber}
        active={pageNumber === activePage}
        aria-label={`Side ${pageNumber}`}
        onClick={() => onPageClick(pageNumber)}
      >
        {pageNumber}
      </Pagination.Item>
    );
  }

  return <Pagination centered>{pageButtons}</Pagination>;
};

SearchPagination.propTypes = {
  result: SearchResultShape.isRequired,
  onPageClick: PropTypes.func.isRequired
};

export default SearchPagination;
