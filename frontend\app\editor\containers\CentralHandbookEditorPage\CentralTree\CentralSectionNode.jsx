// @flow
import React from 'react';
import { Icon, Tree } from 'kf-bui';
import type { CentralSectionType } from '../../../../types';

type Props = {
  section: CentralSectionType,
  moving: ?boolean
};

const CentralSectionNode = ({ section, moving }: Props) => {
  if (moving) {
    return (
      <Tree.Item key={section.id} id={section.id} disabled>
        <Icon icon="file-text-o" size="small" /> {section.title}
      </Tree.Item>
    );
  }
  return (
    <Tree.ItemLink
      to={`/central-editor/${section.centralHandbookId}/section/${section.id}/`}
      key={section.id}
      id={section.id}
    >
      <Icon icon="file-text-o" size="small" /> {section.title}
    </Tree.ItemLink>
  );
};
export default CentralSectionNode;
