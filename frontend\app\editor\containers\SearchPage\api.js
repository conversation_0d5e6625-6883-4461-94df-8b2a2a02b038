import axios from 'axios';

const BASE_URL = '/search';

export function getSearchResult(query, page, handbookId = null) {
  return axios
    .get(BASE_URL, {
      params: {
        query,
        page,
        handbookId
      }
    })
    .then(res => res.data);
}

export function resetIndexes() {
  return axios.post(`${BASE_URL}/reset`);
}

export function resetManyIndexes(externalOrgIds) {
  return axios.post(`${BASE_URL}/index/`, externalOrgIds).then(res => res.data);
}
