/* eslint-env jest */
import { sortBySortOrderAndTitle } from '../sort';

describe('sortBySortOrderAndTitle', () => {
  it('should sort by sortOrder', () => {
    const toSort = [
      { sortOrder: 1, title: 'DEF' },
      { sortOrder: 0, title: 'ABC' }
    ];
    const expected = [toSort[1], toSort[0]];
    expect(toSort.sort(sortBySortOrderAndTitle)).toEqual(expected);
  });

  it('that 2 should be sorted before 10 even though javascript', () => {
    const toSort = [
      { sortOrder: 10, title: 'DEF' },
      { sortOrder: 2, title: 'ABC' }
    ];
    const expected = [toSort[1], toSort[0]];
    expect(toSort.sort(sortBySortOrderAndTitle)).toEqual(expected);
  });

  it("should sort by title if no sortOrder doesn't exist", () => {
    const toSort = [{ title: 'DEF' }, { title: 'ABC' }];
    const expected = [toSort[1], toSort[0]];
    expect(toSort.sort(sortBySortOrderAndTitle)).toEqual(expected);
  });

  // TODO: Do not skip this test when the logic is fixed
  it.skip('should sort items where some have a sortorder', () => {
    const toSort = [{ title: 'DEF' }, { title: 'ABC' }, { sortOrder: 0, title: 'XYZ' }];

    const expected = [toSort[2], toSort[1], toSort[0]];
    expect(toSort.sort(sortBySortOrderAndTitle)).toEqual(expected);
  });
});
