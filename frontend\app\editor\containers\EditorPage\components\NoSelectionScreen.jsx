import React from 'react';
import PropTypes from 'prop-types';

import { Title } from 'kf-bui';

import injectT from '../../../../common/i18n';

const NoSelection = ({ t }) => (
  <div>
    <Title textCentered>
      {t('text')}
      ...
    </Title>
    <hr />
  </div>
);

NoSelection.propTypes = {
  t: PropTypes.func.isRequired
};

export default injectT('editor.components.NoSelection')(NoSelection);
