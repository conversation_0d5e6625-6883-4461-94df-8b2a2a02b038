import React from 'react';
import PropTypes from 'prop-types';
import { withRouter, Switch, Route, Redirect } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';

import Toaster from 'kf-toaster';

import './custom.css';
import injectT from '../../../common/i18n';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import { SessionShape } from '../../shapes';
import { selectSession } from './selectors';
import { selectPendingCount } from '../EditorPage/selectors';
import { selectHasCentralAccess } from '../CentralTree/selectors';
import { fetchHandbooks as fetchCentralHandbooks } from '../CentralTree/actions';

import CentralHandbooksPage from '../CentralHandbooksPage';
import CentralHandbookEditorPage from '../CentralHandbookEditorPage/CentralHandbookEditorPage';
import SelectOrganizationPage from '../SelectOrganizationPage';
import NoAccessPage from '../../../common/components/NoAccessPage';
import NotFoundPage from '../../../common/components/NotFoundPage';
import EditorPage from '../EditorPage';
import PendingPage from '../PendingPage';
import SearchPage from '../SearchPage';
import MergePage from '../MergePage';
import AdminRoute from '../../components/AdminRoute';

import {
  fetchLocalEditorsForOrganization,
  fetchLocalHandbooks,
  fetchSubscriptions
} from '../EditorPage/actions';
import OptOutModal from '../../../common/components/OptOutModal/OptOutModal';
// import { IdleAlert } from '../../../common/util/IdleAlert';

// The inline styling here is for a sticky footer
// The most outer div here isn't supposed to be needed,
// but it works around a bug in IE 10/11 so we get our sticky footer
// See https://github.com/philipwalton/flexbugs#workaround-2

class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showOptOutModal: false
    };
  }

  componentDidMount() {
    const {
      session,
      fetchLocalHandbooksFun,
      fetchCentralHandbooksFun,
      fetchSubscriptionsFun,
      fetchLocalEditorsForOrganizationFun
    } = this.props;

    if (session.organization) {
      fetchLocalHandbooksFun();
      fetchCentralHandbooksFun();
      fetchSubscriptionsFun();
      fetchLocalEditorsForOrganizationFun();
    }

    if (localStorage.hasVisited) {
      this.setState({ showOptOutModal: false });
    } else {
      localStorage.hasVisited = true;
      this.setState({ showOptOutModal: true });
    }
  }

  render() {
    const { session, logoUrl, pendingCount, t, hasCentralAccess, location } = this.props;

    document.title = t('title');

    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {/* <IdleAlert t={t} history={this.props.history} /> */}

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100vh'
          }}
        >
          <div style={{ flex: '1 0 auto' }}>
            <Navbar
              session={session}
              logoUrl={logoUrl}
              pendingCount={pendingCount}
              hasCentralAccess={hasCentralAccess}
            />
            {this.state.showOptOutModal && (
              <OptOutModal toggleHide={() => this.setState({ showOptOutModal: false })} />
            )}
            <Switch>
              {session.organization
                ? [
                    <Redirect from="/" to="/editor" exact key="home" />,
                    <Route path="/select" component={SelectOrganizationPage} key="selectOrg" />,
                    <Route path="/editor" component={EditorPage} key="editor" />,
                    <Route path="/pending" component={PendingPage} key="pending" />,
                    <Route path="/search" component={SearchPage} key="search" />,
                    <Route path="/merge" component={MergePage} key="merge" />,
                    <AdminRoute
                      path="/central"
                      component={CentralHandbooksPage}
                      key="central"
                      session={session}
                    />,
                    <AdminRoute
                      path="/central-editor"
                      component={CentralHandbookEditorPage}
                      key="central-editor"
                      session={session}
                    />,
                    <Route path="/forbidden" component={NoAccessPage} key="forbidden" />,
                    <Route path="/403" component={NoAccessPage} key="403" />,
                    <Route component={NotFoundPage} key="404" />
                  ]
                : [
                    <Route path="/select" component={SelectOrganizationPage} key="selectOrg" />,
                    <Redirect
                      to={{ pathname: '/select', state: { from: location } }}
                      key="redirect"
                    />
                  ]}
            </Switch>
          </div>
          <Toaster />
          <Footer
            session={session}
            showOptOutModal={() => this.setState({ showOptOutModal: true })}
          />
        </div>
      </div>
    );
  }
}

App.propTypes = {
  t: PropTypes.func.isRequired,
  session: SessionShape.isRequired,
  logoUrl: PropTypes.string,
  pendingCount: PropTypes.number.isRequired,
  hasCentralAccess: PropTypes.bool.isRequired,
  fetchCentralHandbooksFun: PropTypes.func.isRequired,
  fetchLocalHandbooksFun: PropTypes.func.isRequired,
  fetchSubscriptionsFun: PropTypes.func.isRequired,
  fetchLocalEditorsForOrganizationFun: PropTypes.func.isRequired,
  location: PropTypes.shape({
    pathname: PropTypes.string.isRequired
  }).isRequired
  // history: PropTypes.object.isRequired
};

App.defaultProps = {
  logoUrl: null
};

const mapStateToProps = state => ({
  session: selectSession(state),
  pendingCount: selectPendingCount(state),
  hasCentralAccess: selectHasCentralAccess(state)
});

// withRouter to fix the issue with blocking updates https://reacttraining.com/react-router/web/guides/dealing-with-update-blocking
export default compose(
  injectT('editor.containers.App'),
  withRouter,
  connect(mapStateToProps, {
    fetchLocalHandbooksFun: fetchLocalHandbooks,
    fetchCentralHandbooksFun: fetchCentralHandbooks,
    fetchSubscriptionsFun: fetchSubscriptions,
    fetchLocalEditorsForOrganizationFun: fetchLocalEditorsForOrganization
  })
)(App);
