import React, { Component } from 'react';
import PropTypes from 'prop-types';
import tinymce from 'tinymce/tinymce';
import '../../tinymce/langs/nb_NO';

import 'tinymce/themes/silver';
import 'tinymce/icons/default';

import 'tinymce/plugins/anchor/plugin';
import 'tinymce/plugins/autolink/plugin';
import 'tinymce/plugins/autoresize/plugin';
import 'tinymce/plugins/contextmenu/plugin';
import 'tinymce/plugins/image/plugin';
import 'tinymce/plugins/lists/plugin';
import 'tinymce/plugins/link/plugin';
import 'tinymce/plugins/paste/plugin';
import 'tinymce/plugins/table/plugin';
import 'tinymce/plugins/media/plugin';
import 'tinymce/plugins/searchreplace/plugin';
import 'tinymce/plugins/advlist/plugin';
import 'tinymce/plugins/spellchecker/plugin';

class Wysiwyg extends Component {
  componentDidMount() {
    const {
      id,
      toolbar,
      // eslint-disable-next-line camelcase
      toolbar_mode,
      height,
      disabled,
      menubar,
      statusbar,
      imagesUploadUrl,
      spellCheckerRpcUrl,
      onChange
    } = this.props;
    const plugins = [...this.props.plugins, 'paste', 'autolink'];

    tinymce
      .init({
        selector: `textarea#${id}`,
        // KFHB-145 - Stop editors making wide tables that get a horizontal scroll bar in the public view.
        content_style: 'table td {word-break: break-word}',
        language: 'nb_NO',
        // This is an old comment ->>> Really struggling to get friggin TinyMCE's css, fonts and images bundled by
        // webpack without the styling bleeding over in the rest of the application scope
        // This quick fix spares us for hours of work
        // NB! If tinyMCE is upgraded in package.json, remember to update this url!
        skin_url: 'https://cdnjs.cloudflare.com/ajax/libs/tinymce/5.7.0/skins/ui/oxide',
        skin: 'oxide',
        plugins,
        toolbar,
        toolbar_mode,
        height,
        elementpath: false,
        readonly: disabled,
        paste_data_images: true,
        ie7_compat: false,
        relative_urls: false,
        automatic_uploads: false,
        images_upload_url: imagesUploadUrl,
        default_link_target: '_blank',
        link_context_toolbar: true,
        menubar,
        statusbar,
        branding: false,
        image_title: true,
        advlist_number_styles: 'default,lower-alpha,upper-alpha',
        advlist_bullet_styles: 'default',
        spellchecker_languages: 'Bokmål=nb_NO,Nynorsk=nn_NO',
        spellchecker_language: 'nb_NO',
        spellchecker_rpc_url: spellCheckerRpcUrl,
        paste_word_valid_elements:
          'b,strong,i,em,h1,h2,u,p,ol,ul,li,a[href],span,color,font-size,font-color,font-family,mark,table,tr,td',
        paste_retain_style_properties: 'all',
        setup: editor => {
          if (onChange) {
            editor.on('change', e => {
              onChange(e.level.content);
              if (this.props.onEditorContentChange) this.props.onEditorContentChange(true);
            });
          }
          editor.on('keydown', () => {
            if (this.props.onEditorContentChange) this.props.onEditorContentChange(true);
          });
        }
      })
      .then(() => {
        const { value } = this.props;
        // Wait for initialization to be done before setting the content
        tinymce.get(this.props.id).setContent(value || '');
      });
  }

  componentWillReceiveProps(nextProps) {
    const { id, disabled } = this.props;

    if (nextProps.disabled !== disabled) {
      const mode = nextProps.disabled ? 'readonly' : 'design';
      tinymce.get(id).setMode(mode);
    }
  }

  // FIXME: An error is thrown if we attempt setContent before tinymce has initialized,
  // since we only set when init is true, we could potentially "lose" changes to value
  componentDidUpdate(prevProps) {
    const { id, value } = this.props;

    if (value !== prevProps.value) {
      if (tinymce.get(id).initialized) {
        tinymce.get(id).setContent(value || '');
      }
    }
  }

  componentWillUnmount() {
    const { id } = this.props;
    tinymce.get(id).remove();
  }

  setValue(value) {
    const { id } = this.props;
    tinymce.get(id).setContent(value || '');
  }

  isDirty() {
    const { id } = this.props;
    tinymce.get(id).isDirty();
  }

  uploadImagesAndGetContent() {
    const { id } = this.props;
    return (
      tinymce
        .get(id)
        .uploadImages()
        // eslint-disable-next-line react/destructuring-assignment
        .then(() => tinymce.get(this.props.id).getContent())
    );
  }

  render() {
    // eslint-disable-next-line no-unused-vars
    const {
      value,
      disabled,
      toolbar,
      // eslint-disable-next-line camelcase
      toolbar_mode,
      plugins,
      height,
      menubar,
      statusbar,
      imagesUploadUrl,
      spellCheckerRpcUrl,
      ...rest
    } = this.props;

    return (
      <div>
        <textarea className="tiny-mce-editor" {...rest} />
      </div>
    );
  }
}

Wysiwyg.propTypes = {
  id: PropTypes.string,
  disabled: PropTypes.bool,
  value: PropTypes.string,

  // Wysywig specific
  plugins: PropTypes.arrayOf(PropTypes.string),
  toolbar: PropTypes.string,
  toolbar_mode: PropTypes.string,
  height: PropTypes.number,
  menubar: PropTypes.string,
  statusbar: PropTypes.bool,
  imagesUploadUrl: PropTypes.string,
  spellCheckerRpcUrl: PropTypes.string,
  onChange: PropTypes.func,
  onEditorContentChange: PropTypes.func
};

Wysiwyg.defaultProps = {
  id: 'wysiwyg-editor',
  menubar: 'edit insert format table tools',
  statusbar: true,
  plugins: [
    'lists link image anchor media searchreplace advlist spellchecker',
    'table contextmenu paste'
  ],
  toolbar:
    'undo redo | styleselect | bold italic underline | link image media | bullist numlist outdent indent | searchreplace pastetext spellchecker | alignleft aligncenter alignright alignjustify ',
  toolbar_mode: 'wrap',
  height: 500,
  disabled: false,
  imagesUploadUrl: '/handboker/image-upload/',
  value: null,
  spellCheckerRpcUrl: null,
  onChange: null,
  onEditorContentChange: null
};

export default Wysiwyg;
