import { handleActions } from 'redux-actions';
import { fetchHandbookSuccess, fetchSectionSuccess } from './actions';

export const initialState = {
  handbook: null,
  chapters: {},
  sections: {},
  organization: null
};

export default handleActions(
  {
    [fetchHandbookSuccess]: (state, { payload }) => ({
      ...state,
      handbook: payload.result.handbook,
      chapters: payload.entities.chapters || {},
      sections: payload.entities.sections || {},
      linkCollections: payload.result.linkCollections,
      organization: payload.result.organization
    }),

    [fetchSectionSuccess]: (state, { payload }) => ({
      ...state,
      sections: {
        ...state.sections,
        [payload.id]: payload
      }
    })
  },
  initialState
);
