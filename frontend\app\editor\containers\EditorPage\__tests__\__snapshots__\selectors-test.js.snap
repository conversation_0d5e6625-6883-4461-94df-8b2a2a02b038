// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Handbooks selectors selectChapter selects a chapter 1`] = `
Object {
  "handbookId": "HMT",
  "id": "hmtChap5",
  "sortOrder": 3,
  "title": "<PERSON>rannvern",
}
`;

exports[`Handbooks selectors selectHandbooks selects the handbooks, ordered by title 1`] = `
Array [
  Object {
    "id": "HMS",
    "title": "KF HMS-Håndbok",
  },
  Object {
    "id": "HMT",
    "pendingChange": true,
    "title": "KF HMT-Håndbok",
  },
]
`;

exports[`Handbooks selectors selectRootChapters selects the root chapters 1`] = `
Array [
  Object {
    "handbookId": "HMT",
    "id": "hmtChap1",
    "pendingChange": true,
    "sortOrder": 1,
    "title": "Avvik",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtChap5",
    "sortOrder": 3,
    "title": "Brannvern",
  },
]
`;

exports[`Handbooks selectors selectSection selects a section 1`] = `
Object {
  "handbookId": "HMT",
  "id": "hmtDoc2",
  "sortOrder": 5,
  "title": "Avvik skjema",
}
`;

exports[`Handbooks selectors selectSubChaptersAndSections selects the sub chapters and sections and applies property to chapters so we can differentiate them from sections 1`] = `
Array [
  Object {
    "handbookId": "HMT",
    "id": "hmtChap3",
    "parentId": "hmtChap6",
    "sortOrder": 2,
    "title": "Risiko",
    "type": "LOCAL_CHAPTER",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc7",
    "parentId": "hmtChap6",
    "sortOrder": 7,
    "title": "Rutiner for barnehager",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc8",
    "parentId": "hmtChap6",
    "sortOrder": 8,
    "title": "Rutiner for Skoler",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc9",
    "parentId": "hmtChap6",
    "sortOrder": 9,
    "title": "Rutiner for kontor",
  },
]
`;
