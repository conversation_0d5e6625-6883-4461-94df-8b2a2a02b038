// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`fetchHandbooks calls the API and notifies the reducer on success 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchLocalHandbooks",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": Object {
          "payload": Array [
            "HMS",
            "HMT",
          ],
          "type": "EditorContainer/FETCH_LOCAL_HANDBOOKS_SUCCESS",
        },
        "channel": null,
      },
    },
  ],
}
`;

exports[`fetchHandbooks can fail gracefully 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchLocalHandbooks",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": "@@redux-saga-test-plan/json/function/<anonymous>",
        "channel": null,
      },
    },
  ],
}
`;

exports[`fetchSection can fail gracefully 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          "123",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchSection",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": "@@redux-saga-test-plan/json/function/<anonymous>",
        "channel": null,
      },
    },
  ],
}
`;

exports[`fetchSection fetches text when we must 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          "123",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchSection",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": Object {
          "payload": Object {
            "id": "123",
            "text": "text",
            "title": "title",
          },
          "type": "EditorContainer/FETCH_LOCAL_SECTION_SUCCESS",
        },
        "channel": null,
      },
    },
  ],
}
`;
