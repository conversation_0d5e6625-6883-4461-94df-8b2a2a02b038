import React from 'react';
import PropTypes from 'prop-types';
import { Container, Title, Subtitle, Section } from 'kf-bui';

import injectT from '../../i18n';

const NoAccessPage = ({ t }) => (
  <Section>
    <Container>
      <Title>403</Title>
      <Subtitle>{t('header')}</Subtitle>
      <p>{t('message')}</p>
    </Container>
  </Section>
);

NoAccessPage.propTypes = {
  t: PropTypes.func.isRequired
};

export default injectT('common.components.NoAccessPage')(NoAccessPage);
