/* eslint-env jest */

import '@babel/polyfill';

import { normalize } from 'normalizr';
import reducer, { initialState } from '../reducer';
import { fetchHandbookSuccess } from '../actions';
import { handbookSchema } from '../api';

const handbook = { id: '1', title: 'handbook' };

const chapters = [
  { id: 'chap1', title: 'Avvik', handbookId: '1' },
  { id: 'chap3', title: 'Chapter without children', handbookId: '1' }
];

const sections = [{ id: 'section1', title: 'Avvik skjema', handbookId: '1' }];

describe('handbookReducer', () => {
  let state;

  // This gives us some state to work with. Most of the tests needs data in the store.
  beforeEach(() => {
    state = initialState;
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should handle fetch handbook success', () => {
    const action = fetchHandbookSuccess(
      normalize(
        {
          handbook,
          chapters,
          sections
        },
        handbookSchema
      )
    );

    const next = expect(reducer(state, action));
    expect(next).toMatchSnapshot();
  });
});
