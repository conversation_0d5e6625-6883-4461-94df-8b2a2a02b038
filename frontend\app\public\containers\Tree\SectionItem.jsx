// @flow

import React from 'react';

import { Icon, Tree } from 'kf-bui';

import type { SectionType } from '../../../types';

type Props = {
  externalOrgId: string,
  section: SectionType,
  rootChapterId: string,
  // eslint-disable-next-line react/require-default-props
  onClick?: () => void,
  activeSections: string[]
};

class SectionItem extends React.Component<Props> {
  shouldComponentUpdate = (nextProps: Props) => {
    const sectionId = this.props.section.id;

    const wasActive = this.props.activeSections[0] === sectionId;
    const willBecomeActive = !wasActive && nextProps.activeSections[0] === sectionId;
    const willBecomeInactive = wasActive && nextProps.activeSections[0] !== sectionId;

    return willBecomeActive || willBecomeInactive;
  };

  // The current active section might be outside the sidebar view.
  // We want to scroll the active section to the top of the page.
  componentDidUpdate = ({ activeSections: prevActiveSections }: Props) => {
    const { activeSections, section } = this.props;
    const isActive = activeSections[0] === section.id;
    const wasActive = prevActiveSections[0] === section.id;

    if (!wasActive && isActive) {
      const currentElement = document.getElementById(section.id);
      const tree = document.getElementById('scrollable-tree');
      const offset = window.innerHeight / 4;

      if (currentElement && tree) {
        tree.scrollTop += currentElement.getBoundingClientRect().top - offset;
      }
    }
  };

  render() {
    const { externalOrgId, rootChapterId, section, onClick, activeSections } = this.props;

    const to = `/${externalOrgId}/${section.handbookId}/chapter/${rootChapterId}?id=${section.id}`;

    const isActive = activeSections.length > 0 && activeSections[0] === section.id;

    return (
      <Tree.ItemLink
        onClick={onClick}
        to={to}
        id={section.id}
        active={isActive}
        elementColor="info"
      >
        <Icon icon="file-text-o" size="small" /> {section.title}
      </Tree.ItemLink>
    );
  }
}

export default SectionItem;
