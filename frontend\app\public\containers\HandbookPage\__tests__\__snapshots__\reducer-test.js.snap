// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`handbookReducer should handle fetch handbook success 1`] = `
Object {
  "lastCalledWith": [Function],
  "lastReturnedWith": [Function],
  "not": Object {
    "lastCalledWith": [Function],
    "lastReturnedWith": [Function],
    "nthCalledWith": [Function],
    "nthReturnedWith": [Function],
    "toBe": [Function],
    "toBeCalled": [Function],
    "toBeCalledTimes": [Function],
    "toBeCalledWith": [Function],
    "toBeCloseTo": [Function],
    "toBeDefined": [Function],
    "toBeFalsy": [Function],
    "toBeGreaterThan": [Function],
    "toBeGreaterThanOrEqual": [Function],
    "toBeInstanceOf": [Function],
    "toBeLessThan": [Function],
    "toBeLessThanOrEqual": [Function],
    "toBeNaN": [Function],
    "toBeNull": [Function],
    "toBeTruthy": [Function],
    "toBeUndefined": [Function],
    "toContain": [Function],
    "toContainEqual": [Function],
    "toEqual": [Function],
    "toHaveBeenCalled": [Function],
    "toHaveBeenCalledTimes": [Function],
    "toHaveBeenCalledWith": [Function],
    "toHaveBeenLastCalledWith": [Function],
    "toHaveBeenNthCalledWith": [Function],
    "toHaveLastReturnedWith": [Function],
    "toHaveLength": [Function],
    "toHaveNthReturnedWith": [Function],
    "toHaveProperty": [Function],
    "toHaveReturned": [Function],
    "toHaveReturnedTimes": [Function],
    "toHaveReturnedWith": [Function],
    "toMatch": [Function],
    "toMatchInlineSnapshot": [Function],
    "toMatchObject": [Function],
    "toMatchSnapshot": [Function],
    "toReturn": [Function],
    "toReturnTimes": [Function],
    "toReturnWith": [Function],
    "toStrictEqual": [Function],
    "toThrow": [Function],
    "toThrowError": [Function],
    "toThrowErrorMatchingInlineSnapshot": [Function],
    "toThrowErrorMatchingSnapshot": [Function],
  },
  "nthCalledWith": [Function],
  "nthReturnedWith": [Function],
  "rejects": Object {
    "lastCalledWith": [Function],
    "lastReturnedWith": [Function],
    "not": Object {
      "lastCalledWith": [Function],
      "lastReturnedWith": [Function],
      "nthCalledWith": [Function],
      "nthReturnedWith": [Function],
      "toBe": [Function],
      "toBeCalled": [Function],
      "toBeCalledTimes": [Function],
      "toBeCalledWith": [Function],
      "toBeCloseTo": [Function],
      "toBeDefined": [Function],
      "toBeFalsy": [Function],
      "toBeGreaterThan": [Function],
      "toBeGreaterThanOrEqual": [Function],
      "toBeInstanceOf": [Function],
      "toBeLessThan": [Function],
      "toBeLessThanOrEqual": [Function],
      "toBeNaN": [Function],
      "toBeNull": [Function],
      "toBeTruthy": [Function],
      "toBeUndefined": [Function],
      "toContain": [Function],
      "toContainEqual": [Function],
      "toEqual": [Function],
      "toHaveBeenCalled": [Function],
      "toHaveBeenCalledTimes": [Function],
      "toHaveBeenCalledWith": [Function],
      "toHaveBeenLastCalledWith": [Function],
      "toHaveBeenNthCalledWith": [Function],
      "toHaveLastReturnedWith": [Function],
      "toHaveLength": [Function],
      "toHaveNthReturnedWith": [Function],
      "toHaveProperty": [Function],
      "toHaveReturned": [Function],
      "toHaveReturnedTimes": [Function],
      "toHaveReturnedWith": [Function],
      "toMatch": [Function],
      "toMatchInlineSnapshot": [Function],
      "toMatchObject": [Function],
      "toMatchSnapshot": [Function],
      "toReturn": [Function],
      "toReturnTimes": [Function],
      "toReturnWith": [Function],
      "toStrictEqual": [Function],
      "toThrow": [Function],
      "toThrowError": [Function],
      "toThrowErrorMatchingInlineSnapshot": [Function],
      "toThrowErrorMatchingSnapshot": [Function],
    },
    "nthCalledWith": [Function],
    "nthReturnedWith": [Function],
    "toBe": [Function],
    "toBeCalled": [Function],
    "toBeCalledTimes": [Function],
    "toBeCalledWith": [Function],
    "toBeCloseTo": [Function],
    "toBeDefined": [Function],
    "toBeFalsy": [Function],
    "toBeGreaterThan": [Function],
    "toBeGreaterThanOrEqual": [Function],
    "toBeInstanceOf": [Function],
    "toBeLessThan": [Function],
    "toBeLessThanOrEqual": [Function],
    "toBeNaN": [Function],
    "toBeNull": [Function],
    "toBeTruthy": [Function],
    "toBeUndefined": [Function],
    "toContain": [Function],
    "toContainEqual": [Function],
    "toEqual": [Function],
    "toHaveBeenCalled": [Function],
    "toHaveBeenCalledTimes": [Function],
    "toHaveBeenCalledWith": [Function],
    "toHaveBeenLastCalledWith": [Function],
    "toHaveBeenNthCalledWith": [Function],
    "toHaveLastReturnedWith": [Function],
    "toHaveLength": [Function],
    "toHaveNthReturnedWith": [Function],
    "toHaveProperty": [Function],
    "toHaveReturned": [Function],
    "toHaveReturnedTimes": [Function],
    "toHaveReturnedWith": [Function],
    "toMatch": [Function],
    "toMatchInlineSnapshot": [Function],
    "toMatchObject": [Function],
    "toMatchSnapshot": [Function],
    "toReturn": [Function],
    "toReturnTimes": [Function],
    "toReturnWith": [Function],
    "toStrictEqual": [Function],
    "toThrow": [Function],
    "toThrowError": [Function],
    "toThrowErrorMatchingInlineSnapshot": [Function],
    "toThrowErrorMatchingSnapshot": [Function],
  },
  "resolves": Object {
    "lastCalledWith": [Function],
    "lastReturnedWith": [Function],
    "not": Object {
      "lastCalledWith": [Function],
      "lastReturnedWith": [Function],
      "nthCalledWith": [Function],
      "nthReturnedWith": [Function],
      "toBe": [Function],
      "toBeCalled": [Function],
      "toBeCalledTimes": [Function],
      "toBeCalledWith": [Function],
      "toBeCloseTo": [Function],
      "toBeDefined": [Function],
      "toBeFalsy": [Function],
      "toBeGreaterThan": [Function],
      "toBeGreaterThanOrEqual": [Function],
      "toBeInstanceOf": [Function],
      "toBeLessThan": [Function],
      "toBeLessThanOrEqual": [Function],
      "toBeNaN": [Function],
      "toBeNull": [Function],
      "toBeTruthy": [Function],
      "toBeUndefined": [Function],
      "toContain": [Function],
      "toContainEqual": [Function],
      "toEqual": [Function],
      "toHaveBeenCalled": [Function],
      "toHaveBeenCalledTimes": [Function],
      "toHaveBeenCalledWith": [Function],
      "toHaveBeenLastCalledWith": [Function],
      "toHaveBeenNthCalledWith": [Function],
      "toHaveLastReturnedWith": [Function],
      "toHaveLength": [Function],
      "toHaveNthReturnedWith": [Function],
      "toHaveProperty": [Function],
      "toHaveReturned": [Function],
      "toHaveReturnedTimes": [Function],
      "toHaveReturnedWith": [Function],
      "toMatch": [Function],
      "toMatchInlineSnapshot": [Function],
      "toMatchObject": [Function],
      "toMatchSnapshot": [Function],
      "toReturn": [Function],
      "toReturnTimes": [Function],
      "toReturnWith": [Function],
      "toStrictEqual": [Function],
      "toThrow": [Function],
      "toThrowError": [Function],
      "toThrowErrorMatchingInlineSnapshot": [Function],
      "toThrowErrorMatchingSnapshot": [Function],
    },
    "nthCalledWith": [Function],
    "nthReturnedWith": [Function],
    "toBe": [Function],
    "toBeCalled": [Function],
    "toBeCalledTimes": [Function],
    "toBeCalledWith": [Function],
    "toBeCloseTo": [Function],
    "toBeDefined": [Function],
    "toBeFalsy": [Function],
    "toBeGreaterThan": [Function],
    "toBeGreaterThanOrEqual": [Function],
    "toBeInstanceOf": [Function],
    "toBeLessThan": [Function],
    "toBeLessThanOrEqual": [Function],
    "toBeNaN": [Function],
    "toBeNull": [Function],
    "toBeTruthy": [Function],
    "toBeUndefined": [Function],
    "toContain": [Function],
    "toContainEqual": [Function],
    "toEqual": [Function],
    "toHaveBeenCalled": [Function],
    "toHaveBeenCalledTimes": [Function],
    "toHaveBeenCalledWith": [Function],
    "toHaveBeenLastCalledWith": [Function],
    "toHaveBeenNthCalledWith": [Function],
    "toHaveLastReturnedWith": [Function],
    "toHaveLength": [Function],
    "toHaveNthReturnedWith": [Function],
    "toHaveProperty": [Function],
    "toHaveReturned": [Function],
    "toHaveReturnedTimes": [Function],
    "toHaveReturnedWith": [Function],
    "toMatch": [Function],
    "toMatchInlineSnapshot": [Function],
    "toMatchObject": [Function],
    "toMatchSnapshot": [Function],
    "toReturn": [Function],
    "toReturnTimes": [Function],
    "toReturnWith": [Function],
    "toStrictEqual": [Function],
    "toThrow": [Function],
    "toThrowError": [Function],
    "toThrowErrorMatchingInlineSnapshot": [Function],
    "toThrowErrorMatchingSnapshot": [Function],
  },
  "toBe": [Function],
  "toBeCalled": [Function],
  "toBeCalledTimes": [Function],
  "toBeCalledWith": [Function],
  "toBeCloseTo": [Function],
  "toBeDefined": [Function],
  "toBeFalsy": [Function],
  "toBeGreaterThan": [Function],
  "toBeGreaterThanOrEqual": [Function],
  "toBeInstanceOf": [Function],
  "toBeLessThan": [Function],
  "toBeLessThanOrEqual": [Function],
  "toBeNaN": [Function],
  "toBeNull": [Function],
  "toBeTruthy": [Function],
  "toBeUndefined": [Function],
  "toContain": [Function],
  "toContainEqual": [Function],
  "toEqual": [Function],
  "toHaveBeenCalled": [Function],
  "toHaveBeenCalledTimes": [Function],
  "toHaveBeenCalledWith": [Function],
  "toHaveBeenLastCalledWith": [Function],
  "toHaveBeenNthCalledWith": [Function],
  "toHaveLastReturnedWith": [Function],
  "toHaveLength": [Function],
  "toHaveNthReturnedWith": [Function],
  "toHaveProperty": [Function],
  "toHaveReturned": [Function],
  "toHaveReturnedTimes": [Function],
  "toHaveReturnedWith": [Function],
  "toMatch": [Function],
  "toMatchInlineSnapshot": [Function],
  "toMatchObject": [Function],
  "toMatchSnapshot": [Function],
  "toReturn": [Function],
  "toReturnTimes": [Function],
  "toReturnWith": [Function],
  "toStrictEqual": [Function],
  "toThrow": [Function],
  "toThrowError": [Function],
  "toThrowErrorMatchingInlineSnapshot": [Function],
  "toThrowErrorMatchingSnapshot": [Function],
}
`;
