/* eslint-env jest */
import reducer, { initialState } from '../reducer';
import { fetchCentralHandbooksSuccess } from '../actions';

const handbooks = [
  { id: '1', title: 'central handbook 1' },
  { id: '2', title: 'central handbook 2' }
];

describe('centralHandbookReducer', () => {
  let state;

  // This gives us some state to work with. Most of the tests needs data in the store.
  beforeEach(() => {
    state = initialState;
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should handle fetch central handbook success', () => {
    const next = reducer(state, fetchCentralHandbooksSuccess(handbooks));
    expect(next).toMatchSnapshot();
  });
});
