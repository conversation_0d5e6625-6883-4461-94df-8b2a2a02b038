/* eslint-env jest */
import '@babel/polyfill';

import { expectSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import * as api from '../api';
import * as actions from '../actions';
import * as saga from '../saga';

describe('fetchHandbooks', () => {
  it('calls the API and notifies the reducer on success', () =>
    expectSaga(saga.fetchHandbooks)
      .provide([[matchers.call.fn(api.fetchLocalHandbooks), ['HMS', 'HMT']]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));

  it('can fail gracefully', () =>
    expectSaga(saga.fetchHandbooks)
      .provide([[matchers.call.fn(api.fetchLocalHandbooks), throwError(new Error('oops'))]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));
});

describe('fetchSection', () => {
  it('fetches text when we must', () =>
    expectSaga(saga.fetchSection, actions.fetchSection('123'))
      .provide([[matchers.call.fn(api.fetchSection), { id: '123', title: 'title', text: 'text' }]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));

  it('can fail gracefully', () =>
    expectSaga(saga.fetchSection, actions.fetchSection('123'))
      .provide([[matchers.call.fn(api.fetchSection), throwError(new Error('oops'))]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));
});

/* describe('createLocalHandbook', () => {
  it('calls the API, notifies the reducer,
  navigates to the handbook and notifies the user on a success', () => {
    const handbook = { title: 'Test handbook' };
    const generator = saveLocalHandbookSaga(handbook);

    expect(generator.next().value).toEqual(call(api.saveLocalHandbook, handbook));

    expect(generator.next(handbook).value).toEqual(
      put(createLocalHandbookAction.success(handbook)),
    );

    expect(generator.next().value).toEqual(
      put(subscribeAction.success({ handbookId: handbook.id, subscribe: true })),
    );

    expect(generator.next().value).toEqual(put(push(`/editor/${handbook.id}/`)));

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('refetches handbooks when creating a new one based on a central handbook', () => {
    const handbook = {
      title: 'Test handbook',
      importedHandbookId: 'importedId',
    };
    const generator = saveLocalHandbookSaga(handbook);

    expect(generator.next().value).toEqual(call(api.saveLocalHandbook, handbook));

    expect(generator.next(handbook).value).toEqual(call(fetchHandbooksSaga));

    expect(generator.next(handbook).value).toEqual(
      put(createLocalHandbookAction.success(handbook)),
    );
  });

  it('can fail gracefully', () => {
    const generator = saveLocalHandbookSaga({});
    generator.next();

    expect(generator.throw().value).toEqual(put(createLocalHandbookAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('updateLocalHandbook', () => {
  it('calls the API, notifies the reducer,
  navigates to the handbook and notifies the user on a success', () => {
    const handbook = { id: 'has id', title: 'Test handbook' };
    const generator = saveLocalHandbookSaga(handbook);

    expect(generator.next().value).toEqual(call(api.saveLocalHandbook, handbook));

    expect(generator.next(handbook).value).toEqual(
      put(updateLocalHandbookAction.success(handbook)),
    );

    expect(generator.next().value).toEqual(put(push(`/editor/${handbook.id}/`)));

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = saveLocalHandbookSaga({ id: 'has id' });
    generator.next();

    expect(generator.throw().value).toEqual(put(updateLocalHandbookAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('deleteLocalHandbook', () => {
  it('calls the API, notifies the reducer,
  replaces the location and notifies the user on a success', () => {
    const handbook = { id: 'to_delete', title: 'Test handbook' };
    const generator = deleteLocalHandbookSaga(handbook);

    expect(generator.next().value).toEqual(call(api.deleteLocalHandbook, handbook.id));

    expect(generator.next(handbook).value).toEqual(
      put(deleteLocalHandbookAction.success(handbook.id)),
    );

    expect(generator.next().value).toEqual(put(replace('/editor/')));

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = deleteLocalHandbookSaga({ id: 'id' });
    generator.next();

    expect(generator.throw().value).toEqual(put(deleteLocalHandbookAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('createLocalChapter', () => {
  it('calls the API, notifies the reducer,
  navigates to the chapter and notifies the user on a success', () => {
    const chapter = { title: 'Test chapter', handbookId: 'handbookId' };
    const generator = saveLocalChapterSaga(chapter);

    expect(generator.next().value).toEqual(call(api.saveLocalChapter, chapter));

    expect(generator.next(chapter).value).toEqual(put(createLocalChapterAction.success(chapter)));

    expect(generator.next().value).toEqual(
      put(push(`/editor/${chapter.handbookId}/chapter/${chapter.id}/`)),
    );

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('refetches handbooks when creating a new one based on a central chapter', () => {
    const chapter = {
      title: 'Test chapter',
      handbookId: 'handbookId',
      importedHandbookChapterId: 'importedId',
    };
    const generator = saveLocalChapterSaga(chapter);

    expect(generator.next().value).toEqual(call(api.saveLocalChapter, chapter));

    expect(generator.next(chapter).value).toEqual(call(fetchHandbooksSaga));

    expect(generator.next().value).toEqual(put(createLocalChapterAction.success(chapter)));

    expect(generator.next().value).toEqual(
      put(push(`/editor/${chapter.handbookId}/chapter/${chapter.id}/`)),
    );

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = saveLocalChapterSaga({});
    generator.next();

    expect(generator.throw().value).toEqual(put(createLocalChapterAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('deleteLocalChapter', () => {
  it('calls the API, refetches the content,
  replaces the location with the handbook and notifies the user on a success', () => {
    const chapter = {
      id: 'to_delete',
      title: 'Test handbook',
      handbookId: 'hbId',
    };
    const generator = deleteLocalChapterSaga(chapter);

    expect(generator.next().value).toEqual(call(api.deleteLocalChapter, chapter.id));

    expect(generator.next().value).toEqual(call(fetchHandbooksSaga));

    expect(generator.next(chapter).value).toEqual(
      put(deleteLocalChapterAction.success(chapter.id)),
    );

    expect(generator.next().value).toEqual(put(replace(`/editor/${chapter.handbookId}/`)));

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('calls the API, notifies the reducer,
  replaces the location with the parent chapter and notifies the user on a success', () => {
    const chapter = {
      id: 'to_delete',
      title: 'Test handbook',
      handbookId: 'hbId',
      parentId: 'parentId',
    };
    const generator = deleteLocalChapterSaga(chapter);

    expect(generator.next().value).toEqual(call(api.deleteLocalChapter, chapter.id));

    expect(generator.next().value).toEqual(call(fetchHandbooksSaga));

    expect(generator.next(chapter).value).toEqual(
      put(deleteLocalChapterAction.success(chapter.id)),
    );

    expect(generator.next().value).toEqual(
      put(replace(`/editor/${chapter.handbookId}/chapter/${chapter.parentId}/`)),
    );

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = deleteLocalChapterSaga({ id: 'id' });
    generator.next();

    expect(generator.throw().value).toEqual(put(deleteLocalChapterAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('createLocalSection', () => {
  it('calls the API, notifies the reducer,
  navigates to the section and notifies the user on a success', () => {
    const section = { title: 'Test section', handbookId: 'handbookId' };
    const generator = saveLocalSectionSaga(section);

    expect(generator.next().value).toEqual(call(api.saveLocalSection, section));

    expect(generator.next(section).value).toEqual(put(createLocalSectionAction.success(section)));

    expect(generator.next().value).toEqual(
      put(push(`/editor/${section.handbookId}/section/${section.id}/`)),
    );

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = saveLocalSectionSaga({});
    generator.next();

    expect(generator.throw().value).toEqual(put(createLocalSectionAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('sortItems', () => {
  it('calls the API, notifies the reducer,
  navigates to the section and notifies the user on a success', () => {
    const ids = ['id3', 'id2', 'id1'];
    const generator = sortItemsSaga(ids);

    expect(generator.next().value).toEqual(call(api.saveSortOrder, ids));

    expect(generator.next().value).toEqual(call(fetchHandbooksSaga));

    expect(generator.next().value).toEqual(put(sortItemsAction.success()));

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = sortItemsSaga([]);
    generator.next();

    expect(generator.throw().value).toEqual(put(sortItemsAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('deleteLocalSection', () => {
  it('calls the API, notifies the reducer,
  replaces the location with the parent chapter and notifies the user on a success', () => {
    const section = {
      id: 'to_delete',
      title: 'Test handbook',
      handbookId: 'hbId',
      parentId: 'parentId',
    };
    const generator = deleteLocalSectionSaga(section);

    expect(generator.next().value).toEqual(call(api.deleteLocalSection, section.id));

    expect(generator.next(section).value).toEqual(
      put(deleteLocalSectionAction.success(section.id)),
    );

    expect(generator.next().value).toEqual(
      put(replace(`/editor/${section.handbookId}/chapter/${section.parentId}/`)),
    );

    expect(generator.next().value.PUT.type).toEqual(put(toast.success()).PUT.type);

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = deleteLocalSectionSaga({ id: 'id' });
    generator.next();

    expect(generator.throw().value).toEqual(put(deleteLocalSectionAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });
});

describe('subscribeToHandbook', () => {
  it('calls the API and notifies the reducer', () => {
    const handbookId = 'h1';
    const generator = subscribeToHandbookSaga({ handbookId, subscribe: false });

    expect(generator.next().value).toEqual(call(api.subscribeToHandbook, handbookId, false));

    expect(generator.next().value).toEqual(
      put(subscribeAction.success({ handbookId, subscribe: false })),
    );

    expect(generator.next().done).toBe(true);
  });

  it('can fail gracefully', () => {
    const generator = subscribeToHandbookSaga({ handbookId: 'id' });
    generator.next();

    expect(generator.throw().value).toEqual(put(subscribeAction.failure()));

    // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
    // is of the error type, but this works for now because of time constraints
    expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
    expect(generator.next().done).toBe(true);
  });

  describe('fetchSubscriptions', () => {
    it('calls the API and notifies the reducer', () => {
      const generator = fetchSubscriptionsSaga();

      expect(generator.next().value).toEqual(call(api.fetchSubscriptions));

      const mockRes = new Set(['h1', 'h2']);
      expect(generator.next(mockRes).value).toEqual(put(fetchSubscriptionsAction.success(mockRes)));

      expect(generator.next().done).toBe(true);
    });

    it('can fail gracefully', () => {
      const generator = fetchSubscriptionsSaga();
      generator.next();

      expect(generator.throw().value).toEqual(put(fetchSubscriptionsAction.failure()));

      // FIXME: This doesn't look nice and we aren't really testing to see if the toast message
      // is of the error type, but this works for now because of time constraints
      expect(generator.next().value.PUT.type).toEqual(put(toast.error()).PUT.type);
      expect(generator.next().done).toBe(true);
    });
  });
}); */
