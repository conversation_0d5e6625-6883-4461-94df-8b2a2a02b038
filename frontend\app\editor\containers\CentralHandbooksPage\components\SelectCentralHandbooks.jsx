import React from 'react';
import PropTypes from 'prop-types';
import { isEqual, partition } from 'lodash';

import { Button, Column, Menu, Columns, Group } from 'kf-bui';

import { HandbookShape } from '../../../shapes';
import { sortBy } from '../../../../common/util';
import injectT from '../../../../common/i18n';

/**
 * Partitions (and sorts) the central handbooks whether the organization has access to them or not
 */
function partitionHandbooks(handbooks, access) {
  const partioned = partition(handbooks, h => access.includes(h.id));
  partioned[0].sort(sortBy('title'));
  partioned[1].sort(sortBy('title'));
  return partioned;
}

class SelectCentralHandbooks extends React.Component {
  constructor(props) {
    super(props);
    const partitioned = partitionHandbooks(props.handbooks, props.access);
    this.state = {
      initialAvailable: partitioned[1],
      available: partitioned[1],
      initialSelected: partitioned[0],
      selected: partitioned[0]
    };
    this.onReset = this.onReset.bind(this);
  }

  /**
   * If the accesses are changed, reset the state (similar to the constructor)
   */
  componentWillReceiveProps(nextProps) {
    const { access, handbooks } = this.props;

    if (access !== nextProps.access || handbooks !== nextProps.handbooks) {
      const partitioned = partitionHandbooks(nextProps.handbooks, nextProps.access);
      this.setState({
        initialAvailable: partitioned[1],
        available: partitioned[1],
        initialSelected: partitioned[0],
        selected: partitioned[0]
      });
    }
  }

  onSelect = (event, item) => {
    event.preventDefault();
    this.setState(state => ({
      selected: state.selected.concat(item).sort(sortBy('title')),
      available: state.available.filter(i => i !== item)
    }));
  };

  onDeselect = (event, item) => {
    event.preventDefault();
    this.setState(state => ({
      selected: state.selected.filter(i => i !== item),
      available: state.available.concat(item).sort(sortBy('title'))
    }));
  };

  onReset = () => {
    this.setState(state => ({
      available: state.initialAvailable,
      selected: state.initialSelected
    }));
  };

  render() {
    const { available, initialAvailable, selected } = this.state;
    const { t, onSave } = this.props;

    return (
      <div>
        <Columns>
          <Column>
            <SelectableList
              items={available}
              onSelect={this.onSelect}
              label={t('availableBooks')}
            />
          </Column>
          <Column>
            <SelectableList items={selected} onSelect={this.onDeselect} label={t('orgBooks')} />
          </Column>
        </Columns>
        <Columns>
          <Column>
            <Group>
              <Button
                control
                disabled={isEqual(available, initialAvailable)}
                onClick={this.onReset}
              >
                {t('resetButton')}
              </Button>
              <Button
                control
                disabled={isEqual(available, initialAvailable)}
                onClick={() => onSave(selected.map(h => h.id))}
                color="primary"
              >
                {t('saveButton')}
              </Button>
            </Group>
          </Column>
        </Columns>
      </div>
    );
  }
}

SelectCentralHandbooks.propTypes = {
  handbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  access: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSave: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

export default injectT('editor.components.SelectCentralHandbooks')(SelectCentralHandbooks);

const SelectableList = ({ items, onSelect, label }) => (
  <Menu style={{ maxHeight: '35em', overflowY: 'scroll' }}>
    <Menu.Label>{label}</Menu.Label>
    <Menu.List>
      {items.length ? (
        items.map(value => <SelectableItem key={value.id} value={value} onSelect={onSelect} />)
      ) : (
        <em>Ingen</em>
      )}
    </Menu.List>
  </Menu>
);

SelectableList.propTypes = {
  label: PropTypes.string.isRequired,
  items: PropTypes.array.isRequired,
  onSelect: PropTypes.func.isRequired
};

const SelectableItem = ({ value, onSelect }) => (
  <Menu.Item onClick={event => onSelect(event, value)} role="button" href="">
    {value.title}
  </Menu.Item>
);

SelectableItem.propTypes = {
  value: HandbookShape.isRequired,
  onSelect: PropTypes.func.isRequired
};
