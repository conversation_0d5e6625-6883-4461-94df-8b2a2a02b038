import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';

import { Button, Control, Column, Icon, Columns, Title, Group, Menu } from 'kf-bui';

import injectT from '../../../../common/i18n';
import DeleteButton from '../../../components/DeleteButton/DeleteButton';
import Metadata from '../../../components/Metadata';
import SortChildrenScreen from '../../../components/SortChildrenScreen';
import { selectSubChaptersAndSections, selectChapterEntities } from '../../EditorPage/selectors';
import {
  deleteLocalChapter,
  saveLocalChapter,
  sortItems,
  fetchChapter
} from '../../EditorPage/actions';
import { ChapterShape, SectionShape } from '../../../shapes';
import PendingChangeWarning from '../../../components/PendingChangeWarning';
import { Attachment } from '../../../components/Attachments/icons';
import Attachments from '../../../components/Attachments';
import { fetchHandbookFileCount } from '../../../components/Attachments/api';

class ChapterSelection extends React.Component {
  state = {
    isSorting: false,
    showAttachments: false,
    attachmentCount: 0
  };

  async componentDidMount() {
    const { match, fetchChapterFun } = this.props;
    fetchChapterFun(match.params.chapterId);
    await this.handleAttachmentCount();
  }

  componentWillReceiveProps(nextProps) {
    const { match, fetchChapterFun } = this.props;
    if (match.params.chapterId !== nextProps.match.params.chapterId) {
      fetchChapterFun(nextProps.match.params.chapterId);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.match.params.chapterId !== this.props.match.params.chapterId) {
      this.handleAttachmentCount();
    }
  }

  handleAttachmentCount = async () => {
    try {
      const { match } = this.props;
      const attachmentCount = await fetchHandbookFileCount('chapter', match.params.chapterId);
      this.setState({ attachmentCount: attachmentCount.count });
    } catch (e) {
      console.error(e);
    }
  };

  toggleSort = () => this.setState(state => ({ isSorting: !state.isSorting }));

  render() {
    const {
      chapters,
      chaptersAndSections,
      deleteLocalChapterFun,
      saveLocalChapterFun,
      sortItemsFun,
      match,
      t
    } = this.props;
    const chapter = chapters[match.params.chapterId];
    const { isSorting } = this.state;

    if (!chapter) return null;

    return (
      <div>
        <Columns>
          <Column>
            <Title>
              <Icon icon="bookmark-o" size="small" style={{ marginRight: '1rem' }} />
              <span>{chapter.title}</span>
            </Title>
          </Column>
        </Columns>
        <Columns responsive="desktop">
          <Column>
            <Group>
              <Button
                control
                as={Link}
                to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/edit/`}
                size="small"
              >
                <Icon icon="pencil" size="small" />
                <span>{t('editButton')}</span>
              </Button>
              <Button
                control
                as={Link}
                to={{
                  pathname: `/editor/${chapter.handbookId}/chapter/add-new`,
                  search: `?parent=${chapter.id}`
                }}
                size="small"
              >
                <Icon icon="plus" size="small" />
                <span>{t('newChapter')}</span>
              </Button>
              <Button
                control
                as={Link}
                to={{
                  pathname: `/editor/${chapter.handbookId}/section/add-new/`,
                  search: `?parent=${chapter.id}`
                }}
                size="small"
              >
                <Icon icon="plus" size="small" />
                <span>{t('newSection')}</span>
              </Button>
              <Button
                control
                onClick={() => this.setState({ showAttachments: true })}
                size="small"
                className="add-attachment-btn"
              >
                <Attachment />
                <span>{t('addAttachment')}</span>
                <span className="attachment-count">{this.state.attachmentCount}</span>
              </Button>
            </Group>
          </Column>
          <Column narrow>
            <Group>
              <Control>
                <PendingChangeWarning
                  element={chapter}
                  mergeLink={`/merge/chapter/${chapter.id}/`}
                  onDelete={keep => {
                    if (keep) {
                      saveLocalChapterFun({ chapter: { ...chapter, pendingDeletion: false } });
                      return;
                    }
                    deleteLocalChapterFun(chapter);
                  }}
                  deleteText={{
                    buttonText: 'Kapittel slettet sentralt',
                    title: 'Håndter sentral sletting',
                    text:
                      'Dette kapittelet er blitt slettet sentralt, ønsker du å slette det lokale også?'
                  }}
                />
              </Control>
              <Button
                control
                as={Link}
                to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/move/`}
                size="small"
              >
                {t('moveButton')}
              </Button>
              <Button
                control
                active={isSorting}
                disabled={chaptersAndSections.length <= 1}
                onClick={this.toggleSort}
                size="small"
              >
                {t('sortButton')}
              </Button>
              {!chapter.pendingDeletion ? (
                <DeleteButton
                  t={t}
                  toDelete={chapter}
                  onDelete={() => deleteLocalChapterFun(chapter)}
                />
              ) : null}
            </Group>
          </Column>
        </Columns>
        <Columns>
          <Column>
            <hr />
            <Metadata element={chapter} />
            <hr />
            {isSorting ? (
              <SortChildrenScreen
                items={chaptersAndSections}
                onCancel={this.toggleSort}
                sortFunction={sortItemsFun}
              />
            ) : (
              <Menu>
                <Menu.List>
                  {chaptersAndSections.map(child => (
                    <Menu.Item
                      key={child.id}
                      as={Link}
                      to={`/editor/${child.handbookId}/${
                        child.type === 'LOCAL_CHAPTER' ? 'chapter' : 'section'
                      }/${child.id}/`}
                    >
                      <Icon icon={child.type === 'LOCAL_CHAPTER' ? 'bookmark-o' : 'file-text-o'} />{' '}
                      {child.title}
                    </Menu.Item>
                  ))}
                </Menu.List>
              </Menu>
            )}
          </Column>
        </Columns>

        {this.state.showAttachments && (
          <Attachments
            sectionId={chapter.id}
            sectionType="CHAPTER"
            onClose={() => this.setState({ showAttachments: false })}
            handleAttachmentCount={this.handleAttachmentCount}
          />
        )}
      </div>
    );
  }
}

ChapterSelection.propTypes = {
  match: PropTypes.shape({
    params: PropTypes.shape({
      chapterId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired,
  chapters: PropTypes.objectOf(ChapterShape).isRequired,
  chaptersAndSections: PropTypes.arrayOf(PropTypes.oneOfType([ChapterShape, SectionShape]))
    .isRequired,
  deleteLocalChapterFun: PropTypes.func.isRequired,
  saveLocalChapterFun: PropTypes.func.isRequired,
  sortItemsFun: PropTypes.func.isRequired,
  fetchChapterFun: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

const mapStateToProps = (state, ownProps) => {
  const { chapterId } = ownProps.match.params;
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(chapterId);
  return {
    chapters: selectChapterEntities(state),
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  };
};

export default compose(
  injectT('editor.containers.ChapterSelection'),
  connect(mapStateToProps, {
    deleteLocalChapterFun: deleteLocalChapter,
    saveLocalChapterFun: saveLocalChapter,
    sortItemsFun: sortItems,
    fetchChapterFun: fetchChapter
  })
)(ChapterSelection);
