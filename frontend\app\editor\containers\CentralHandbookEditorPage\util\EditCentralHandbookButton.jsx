// @flow
import React from 'react';
import { compose } from 'redux';
import { Button, Icon, Control } from 'kf-bui';
import CentralHandbookForm from '../CentralHandbookModal';
import BooleanToggler from '../../../components/BooleanToggler';
import type { CentralHandbookType } from '../../../../types';
import injectT from '../../../../common/i18n';

type Props = {
  centralHandbook: CentralHandbookType,
  t: string => string
};

const EditCentralHandbookButton = ({ centralHandbook, t }: Props) => (
  <BooleanToggler>
    {(toggle, value) => (
      <Control>
        {[
          <Button key="button" onClick={toggle} title="Rediger denne Håndboken" size="small">
            <Icon icon="pencil" size="small" />
            <span>{t('editButton')}</span>
          </Button>,
          value ? (
            <CentralHandbookForm key="modal" onHide={toggle} centralHandbook={centralHandbook} />
          ) : null
        ]}
      </Control>
    )}
  </Bo<PERSON>anToggler>
);

export default compose(injectT('editor.containers.HandbookSelection'))(EditCentralHandbookButton);
