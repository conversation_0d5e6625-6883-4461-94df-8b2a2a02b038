import axios from 'axios';

const BASE_URL = '/handbooks/central';

export function fetchAllCentralHandbooks() {
  return axios.get(`${BASE_URL}/handbooks/`).then(res => res.data);
}

/**
 * @param {String} externalOrgId
 */
export function fetchAccess(externalOrgId) {
  return axios.get(`${BASE_URL}/access/${externalOrgId}/`).then(res => res.data);
}

/**
 * @param {String} externalOrgId
 * @param {Array} List of central handbook ids
 */
export function saveAccess(externalOrgId, ids) {
  return axios.post(`${BASE_URL}/access/${externalOrgId}/`, ids).then(res => res.data);
}
