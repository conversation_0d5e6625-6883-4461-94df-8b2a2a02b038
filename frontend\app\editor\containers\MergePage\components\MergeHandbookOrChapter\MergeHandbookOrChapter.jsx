import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Link } from 'react-router-dom';
import { Card, Column, Button, Field, Label, Input, Columns, Title, Subtitle } from 'kf-bui';
import { toast } from 'kf-toaster';

import moment from 'moment';
import injectT from '../../../../../common/i18n';
import mergeTitle from './elementMerger';
import { ChapterShape, HandbookShape } from '../../../../shapes';
import { fetchLatestChapterVersion, fetchHandbook } from '../../../CentralTree/api';
import { selectHandbookEntities, selectChapterEntities } from '../../../EditorPage/selectors';
import { saveLocalChapter, saveLocalHandbook } from '../../../EditorPage/actions';

// Since we use the same component for handbooks and chapters
// This one could get either based on the url
function getElementToMerge(props) {
  if (props.match.params.handbookId) {
    return props.handbooks[props.match.params.handbookId];
  }
  return props.chapters[props.match.params.chapterId];
}

class MergeHandbookOrChapter extends React.Component {
  state = {
    title: '',
    isSaving: false,
    centralElement: undefined
  };

  componentDidMount() {
    const { centralElement } = this.state;
    if (!centralElement) {
      this.fetchCentralElement(this.props);
    }
  }

  componentWillReceiveProps(nextProps) {
    const { centralElement } = this.state;
    if (!centralElement) {
      this.fetchCentralElement(nextProps);
    }
  }

  onSave = event => {
    const { match } = this.props;
    const { centralElement, title } = this.state;
    event.preventDefault();
    this.setState({ isSaving: true });

    const merged = mergeTitle(getElementToMerge(this.props), centralElement, title);

    const centralChange = centralElement.title === title ? 'KF' : 'local';
    if (match.params.handbookId) {
      this.props.saveLocalHandbook({
        ...merged,
        title: `${merged.title}`
      });
    } else {
      this.props.saveLocalChapter({ chapter: merged, centralChange });
    }
  };

  // Gets the central element that we want to merge with and puts it in the state
  fetchCentralElement(props) {
    const { errorToast, t } = this.props;
    const elementToMerge = getElementToMerge(props);
    if (!elementToMerge) {
      return;
    }

    let fetch;

    if (props.match.params.handbookId) {
      fetch = fetchHandbook(elementToMerge.importedHandbookId);
    } else {
      fetch = fetchLatestChapterVersion(
        elementToMerge.importedHandbookId,
        elementToMerge.importedHandbookChapterId
      );
    }

    fetch
      .then(centralElement => this.setState({ centralElement }))
      .catch(() => errorToast(t('fetchCentralElementFail')));
  }

  renderInfo() {
    const { t, match } = this.props;

    if (match.params.chapterId) {
      return (
        <div>
          <Title className="text-center">{t('mergeChapter')}</Title>
          <Subtitle>{t('mergeChapterParagraph')}</Subtitle>
        </div>
      );
    }
    return (
      <div>
        <Title className="text-center">{t('mergeManual')}</Title>
        <Subtitle>{t('mergeManualParagraph')}</Subtitle>
      </div>
    );
  }

  render() {
    const { t } = this.props;
    const { centralElement, title, isSaving } = this.state;
    const elementToMerge = getElementToMerge(this.props);

    // Defer rendering until we have content
    if (!elementToMerge || !centralElement) {
      return null;
    }

    let abortLink;
    if (elementToMerge.handbookId) {
      abortLink = `/editor/${elementToMerge.handbookId}/chapter/${elementToMerge.id}`;
    } else {
      abortLink = `/editor/${elementToMerge.id}`;
    }

    return (
      <div>
        <Columns>
          <Column>
            {this.renderInfo()}
            <hr />
          </Column>
        </Columns>
        <Columns>
          <Column>
            <Card>
              <Card.Header title={t('headerCentral')} />
              <Card.Content>
                <Title as="em" size="5">
                  {centralElement.title}
                </Title>
                <div className="pending-timestamp">
                  {`${t('lastModified')} ${moment(
                    centralElement.centralChapterUpdatedDateBeforePublish
                  ).format('DD.MM.YYYY')}`}
                </div>
              </Card.Content>
              <Card.Footer>
                <Card.Footer.Item
                  as="a"
                  role="button"
                  href=""
                  onClick={event => {
                    event.preventDefault();
                    this.setState({ title: centralElement.title });
                  }}
                >
                  {t('useCentral')}
                </Card.Footer.Item>
              </Card.Footer>
            </Card>
          </Column>
          <Column>
            <Card>
              <Card.Header title={t('headerExisting')} />
              <Card.Content>
                <Title as="em" size="5">
                  {elementToMerge.title}
                </Title>
                <div className="pending-timestamp">
                  {`${t('lastModified')} ${moment(elementToMerge.localChapterUpdatedDate).format(
                    'DD.MM.YYYY'
                  )}`}
                </div>
              </Card.Content>
              <Card.Footer>
                <Card.Footer.Item
                  as="a"
                  role="button"
                  href=""
                  onClick={event => {
                    event.preventDefault();
                    this.setState({ title: elementToMerge.title });
                  }}
                >
                  {t('useLocal')}
                </Card.Footer.Item>
              </Card.Footer>
            </Card>
          </Column>
        </Columns>
        <hr />
        <form onSubmit={this.onSave}>
          <Field>
            <Label htmlFor="title">Tittel</Label>
            <Input
              id="title"
              placeholder="Tittel"
              onChange={e => this.setState({ title: e.target.value })}
              value={title}
              required
              readOnly={isSaving}
            />
          </Field>
          <Columns responsive="mobile">
            <Column>
              <Button as={Link} to={abortLink}>
                Avbryt
              </Button>
            </Column>
            <Column narrow>
              <Button
                disabled={!title}
                loading={isSaving}
                onClick={this.onSave}
                color="primary"
                type="submit"
              >
                {t('saveButton')}
              </Button>
            </Column>
          </Columns>
        </form>
      </div>
    );
  }
}

MergeHandbookOrChapter.propTypes = {
  /* eslint-disable react/no-unused-prop-types */
  handbooks: PropTypes.objectOf(HandbookShape).isRequired,
  chapters: PropTypes.objectOf(ChapterShape).isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      handbookId: PropTypes.string,
      chapterId: PropTypes.string
    }).isRequired
  }).isRequired,
  saveLocalChapter: PropTypes.func.isRequired,
  saveLocalHandbook: PropTypes.func.isRequired,
  errorToast: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

export default compose(
  injectT('editor.components.MergeHandbookOrChapter'),
  connect(
    state => ({
      chapters: selectChapterEntities(state),
      handbooks: selectHandbookEntities(state),
      errorToast: toast.error
    }),
    { saveLocalChapter, saveLocalHandbook }
  )
)(MergeHandbookOrChapter);
