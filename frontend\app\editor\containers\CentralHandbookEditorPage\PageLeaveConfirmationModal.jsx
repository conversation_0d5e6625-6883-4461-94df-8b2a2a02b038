import React from 'react';
import PropTypes from 'prop-types';
import { Button, Modal } from 'kf-bui';

const PageLeaveConfirmationModal = ({ onCancel, onConfirm, message, title, t }) => (
  <Modal onClose={onCancel}>
    <Modal.Header onClose={onCancel}>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <p>{message}</p>
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={onCancel}>{t('leaveEditorConfirmationCancel')}</Button>
      <Button color="danger" outlined className="modal-leave-button" onClick={onConfirm}>
        {t('leaveEditorConfirmationLeave')}
      </Button>
    </Modal.Footer>
  </Modal>
);

PageLeaveConfirmationModal.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  t: PropTypes.func.isRequired
};

export default PageLeaveConfirmationModal;
