// @flow

import React from 'react';
import { connect } from 'react-redux';

import { Icon, Tree } from 'kf-bui';

import SectionItem from './SectionItem';
import { selectSubChaptersAndSections } from '../HandbookPage/selectors';

import type { ChapterType, ChildrenType } from '../../../types';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

type Props = {
  chapter: ChapterType,
  chaptersAndSections: ChildrenType[],
  externalOrgId: string,
  rootChapterId: ?string,
  // eslint-disable-next-line react/require-default-props
  onClick?: () => void,
  activeSections: string[],
  expandedChapters: string[],
  onCollapseClick: (item: ChapterType) => void
};

class ChapterItem extends React.Component<Props> {
  shouldComponentUpdate = (nextProps: Props) => {
    const wasActiveUnderThis = !!this.props.activeSections.find(sectionId =>
      this.props.chaptersAndSections.find(e => e.id === sectionId)
    );
    const isActiveUnderThis = !!nextProps.activeSections.find(sectionId =>
      nextProps.chaptersAndSections.find(e => e.id === sectionId)
    );

    const gotUnderChapters = !!nextProps.chaptersAndSections.filter(
      c => c.type === 'LOCAL_CHAPTER'
    );
    const isExpanded = !!nextProps.expandedChapters.includes(nextProps.chapter.id);

    const thereMightHaveDecendantChapterWithActiveSections = isExpanded && !!gotUnderChapters;
    const thereWasOrIsActiveSectionsUnderThisChapter = wasActiveUnderThis || isActiveUnderThis;

    return (
      thereWasOrIsActiveSectionsUnderThisChapter || thereMightHaveDecendantChapterWithActiveSections
    );
  };

  render() {
    const {
      chapter,
      chaptersAndSections,
      externalOrgId,
      rootChapterId,
      onClick,
      activeSections,
      expandedChapters,
      onCollapseClick
    } = this.props;

    const items = chaptersAndSections.map(child =>
      child.type === 'LOCAL_CHAPTER' ? (
        <ConnectedChapterItem
          chapter={child}
          externalOrgId={externalOrgId}
          key={child.id}
          rootChapterId={rootChapterId || chapter.id}
          activeSections={activeSections}
          expandedChapters={expandedChapters}
          onCollapseClick={onCollapseClick}
        />
      ) : (
        // $FlowFixMe Flow doesn't like mixed type arrays
        <SectionItem
          key={child.id}
          externalOrgId={externalOrgId}
          section={child}
          rootChapterId={rootChapterId || chapter.id}
          onClick={onClick}
          activeSections={activeSections}
        />
      )
    );

    const to = `/${externalOrgId}/${chapter.handbookId}/chapter/${rootChapterId || chapter.id}`;

    return (
      <Tree.ItemLink
        to={rootChapterId ? `${to}?id=${chapter.id}` : to}
        id={chapter.id}
        items={items}
        active={false}
        collapsed={!(expandedChapters && expandedChapters.includes(chapter.id))}
        onCollapseClick={() => onCollapseClick(chapter)}
        elementColor="info"
      >
        <Icon icon="bookmark-o" size="small" /> {chapter.title}
      </Tree.ItemLink>
    );
  }
}

const makeMapStateToProps = (_, ownProps) => {
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(ownProps.chapter.id);

  const mapStateToProps = state => ({
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  });
  return mapStateToProps;
};

const ConnectedChapterItem = connect(makeMapStateToProps)(ChapterItem);

export default ConnectedChapterItem;
