// @flow

import React from 'react';
import { connect } from 'react-redux';
import {
  Group,
  Subtitle,
  Button,
  Label,
  Modal,
  Box,
  Radio,
  Control,
  Icon,
  FormattedDate
} from 'kf-bui';
import type { ReadingLinkType, CentralSectionType } from '../../../types';
import { persistReadingLink, retrieveReadingLink, deleteLink } from './actions';
import { makeSelectReadingLink } from './selectors';

type Props = {
  centralSection: CentralSectionType,
  toggleLinkModal: () => void,
  persistReadingLinkFun: (link: ReadingLinkType) => void,
  retrieveReadingLinkFun: (sectionId: string) => void,
  deleteLinkFun: (readingLink: ReadingLinkType) => void,
  readingLink: ?ReadingLinkType
};

type State = {
  numberOfMonths: ?string
};

class ReadingLinkModal extends React.Component<Props, State> {
  state = {
    numberOfMonths: undefined
  };

  componentDidMount() {
    const { centralSection, readingLink, retrieveReadingLinkFun } = this.props;
    if (readingLink === undefined) {
      retrieveReadingLinkFun(centralSection.id);
    }
  }

  handleMonthUpdate = event => {
    const { value } = event.target;
    this.setState({ numberOfMonths: value });
  };

  isLinkValid = validTo => {
    return new Date(validTo).getTime() > new Date().getTime();
  };

  monthsAhead = months => {
    const now = new Date();
    const noMonths = parseInt(months, 10) + now.getMonth();
    now.setMonth(noMonths);
    now.setHours(23);
    now.setMinutes(59);
    now.setSeconds(59);
    now.setMilliseconds(999);
    return now;
  };

  persistUserdefinedReadingLink = months => {
    const { centralSection, persistReadingLinkFun } = this.props;
    const initialReadingLink = {
      centralSectionId: centralSection.id,
      link: 'test',
      validTo: this.monthsAhead(months)
    };

    persistReadingLinkFun(initialReadingLink);
  };

  deleteLink = () => {
    const { readingLink, deleteLinkFun } = this.props;
    if (readingLink) {
      deleteLinkFun(readingLink);
    }
  };

  copyUrl = () => {
    // Procedure to copy the URL to clipboard
    const { readingLink } = this.props;
    const copyFrom = document.createElement('textarea');
    if (readingLink && document.body != null) {
      copyFrom.textContent = readingLink.link;
      document.body.appendChild(copyFrom);
      copyFrom.select();
      document.execCommand('copy');
      copyFrom.blur();
      if (document.body != null) {
        document.body.removeChild(copyFrom);
      }
    }
  };

  render() {
    const { numberOfMonths } = this.state;
    const { toggleLinkModal, readingLink } = this.props;

    const listOfMonths = [1, 12, 24, 60];
    const pluralWeek = months => (months < 12 ? `${months} måned` : `${months / 12} år`);

    return (
      <div>
        <Modal id="linkForm">
          <Box>
            <Subtitle>Generer lesevisningslenke</Subtitle>
            <Label htmlFor="linkForm">
              Obs! Alle som har lenken vil kunne åpne den. Lenken vil være gyldig til tidsrommet du
              velger er over.
            </Label>
            <Control name="months" id="months" style={{ marginBottom: '10px' }}>
              {listOfMonths.map(months => (
                <Radio
                  key={`radio${months}`}
                  id={`radio${months}`}
                  name={`uke ${months}`}
                  value={months}
                  checked={numberOfMonths === months.toString()}
                  onChange={this.handleMonthUpdate}
                >
                  {` ${pluralWeek(months)}`}
                </Radio>
              ))}
            </Control>
            {readingLink && (
              <div>
                <Label htmlFor="link">Lesevisningslenke:</Label>
                <a id="link" style={{ display: 'block' }} href={readingLink.link}>
                  {readingLink.link}
                </a>
                <Control style={{ marginTop: '10px', marginBottom: '5px' }}>
                  <Button as="a" onClick={() => this.copyUrl()}>
                    <Icon icon="copy" />
                    <span>Kopier lenke</span>
                  </Button>
                </Control>
                {this.isLinkValid(readingLink.validTo) ? (
                  <div>
                    <Label htmlFor="validTo">Varer frem til:</Label>
                    <FormattedDate
                      id="validTo"
                      name="validTo"
                      value={readingLink.validTo}
                      format="DD.MM.YYYY"
                    />
                  </div>
                ) : (
                  <Label htmlFor="validTo">Lenken er utgått</Label>
                )}
              </div>
            )}
            <Group right>
              <Button
                disabled={!numberOfMonths}
                control
                onClick={() => this.persistUserdefinedReadingLink(numberOfMonths)}
              >
                {readingLink ? 'Generer ny lenke' : 'Generer lenke'}
              </Button>
              <Button
                onClick={() => this.deleteLink()}
                control
                color="danger"
                disabled={!readingLink}
              >
                Slett lenke
              </Button>
              <Button onClick={toggleLinkModal} control>
                Avbryt
              </Button>
            </Group>
          </Box>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = (state, props) => {
  const { centralSection } = props;
  const selectReadingLink = makeSelectReadingLink(centralSection.id);
  return {
    readingLink: selectReadingLink(state)
  };
};

const mapDispatchToProps = {
  persistReadingLinkFun: persistReadingLink,
  retrieveReadingLinkFun: retrieveReadingLink,
  deleteLinkFun: deleteLink
};

export default connect(mapStateToProps, mapDispatchToProps)(ReadingLinkModal);
