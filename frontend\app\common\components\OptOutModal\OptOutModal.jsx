// @flow
import React, { useState, useRef, useEffect } from 'react';
import { Mo<PERSON>, Button } from 'kf-bui';
import Cookies from 'js-cookie';

/* eslint-disable no-underscore-dangle */
/* eslint-disable jsx-a11y/label-has-associated-control */
// Get Matomo tracking host name from .properties
const matomoBase = window.__MATOMOBASE__;
delete window.__MATOMOBASE__;

type Props = {
  toggleHide: () => void
};

const OutOutModal = ({ toggleHide }: Props) => {
  const content = useRef(null);

  const [isMatomoEnabled, setIsMatomoEnabled] = useState(false);
  const [active, setActive] = useState(false);
  const [height, setHeight] = useState('0px');

  const handleToggleChange = isEnabled => {
    setIsMatomoEnabled(isEnabled);
    localStorage.setItem('matomoEnabled', JSON.stringify(isEnabled));

    if (!isEnabled) {
      Cookies.remove('MATOMO_SESSID', { path: '' });

      const allCookies = Cookies.get();

      Object.keys(allCookies).forEach(cookieName => {
        if (cookieName.startsWith('_pk_')) {
          Cookies.remove(cookieName, { path: '' });
        }
      });
    }
  };

  const toggleAccordion = () => {
    setActive(!active);
    setHeight(active ? '0px' : `${content.current ? content.current.scrollHeight : 0}px`);
  };

  const handleRadioChange = event => {
    const { value } = event.target;
    if (value === 'all') {
      handleToggleChange(true);
    } else if (value === 'necessary') {
      handleToggleChange(false);
    }
  };

  useEffect(() => {
    const savedMatomoSetting = localStorage.getItem('matomoEnabled');
    setIsMatomoEnabled(savedMatomoSetting ? JSON.parse(savedMatomoSetting) : false);
  }, []);

  return (
    <Modal onClose={toggleHide}>
      <Modal.Header onClose={toggleHide}>
        <Modal.Title>Innstillinger for informasjonskapsler</Modal.Title>
      </Modal.Header>
      <Modal.Body className="comments">
        <p className="cookie-desc">
          Vi bruker informasjonskapsler til å samle inn informasjon om deg til ulike formål.
        </p>
        <p className="cookie-desc">
          Noen er nødvendige å innhente for at du kan gjenkjennes og logge inn som bruker. Andre er
          frivillige, og benyttes til å se bruksstatistikk.{' '}
        </p>
        <p className="cookie-desc">
          KF bruker Matomo, en personvernvennlig statistikkløsning, til å analysere bruken av
          nettstedet. Statistikkrapportene Matomo gir KF, brukes til å analysere og forbedre
          brukervennligheten til nettsiden, og til oppfølging av kunder.
        </p>

        <a
          href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_H%C3%A5ndb%C3%B8ker.html"
          target="_blank"
          rel="noopener noreferrer"
          className="cookie-link-desc"
        >
          Les mer om informasjonskapsler her.
        </a>

        <div className="cookie-type-section-container">
          <label>
            <input
              type="radio"
              name="cookie-type"
              value="all"
              checked={isMatomoEnabled}
              onChange={handleRadioChange}
              disabled={!matomoBase}
            />
            <span>Godta alle</span>
          </label>
          <label>
            <input
              type="radio"
              name="cookie-type"
              value="necessary"
              checked={!isMatomoEnabled}
              onChange={handleRadioChange}
            />
            <span>Godta bare nødvendige</span>
          </label>
        </div>

        <div className="toggle-cookies-container">
          <button type="button" className="toggle-cookies-button" onClick={toggleAccordion}>
            Se hvilke informasjonskapsler vi benytter
          </button>
          <i
            onClick={toggleAccordion}
            className={`fa fa-chevron-down ${active ? 'rotate' : ''}`}
            aria-hidden="true"
          />
        </div>

        <div className="cookies-wrapper" ref={content} style={{ maxHeight: `${height}` }}>
          <h4 className="cookie-type-title">Nødvendige</h4>
          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="JSESSIONID" type="checkbox" disabled checked />
              <label htmlFor="JSESSIONID" data-on-text="På" data-off-text="Av" />
              <div className="toggle-button__icon" />
            </div>

            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">JSESSIONID</span>
              </h4>
              <p className="cookie-provider">Mottaker: KF</p>
              <p className="cookie-description">
                Benyttes til sesjonshåndtering når du er logget inn
              </p>
            </div>
          </div>

          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="XSRF-TOKEN" type="checkbox" disabled checked />
              <label htmlFor="XSRF-TOKEN" data-on-text="På" data-off-text="Av" />
              <div className="toggle-button__icon" />
            </div>

            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">XSRF-TOKEN</span>
              </h4>
              <p className="cookie-provider">Mottaker: KF</p>
              <p className="cookie-description">
                Denne informasjonskapselen brukes når du er logget inn, og sikrer at andre ikke kan
                utgi seg for å være deg.
              </p>
            </div>
          </div>

          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="TGC" type="checkbox" disabled checked />
              <label htmlFor="TGC" data-on-text="På" data-off-text="Av" />
              <div className="toggle-button__icon" />
            </div>

            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">TGC</span>
              </h4>
              <p className="cookie-provider">Mottaker: KF</p>
              <p className="cookie-description">Innlogging</p>
            </div>
          </div>

          <h4 className="cookie-type-title">Frivillige</h4>
          {matomoBase && (
            <div className="cookie-item">
              <div className="toggle-button toggle-button--aava">
                <input
                  id="Matomo"
                  type="checkbox"
                  checked={isMatomoEnabled}
                  onChange={() => handleToggleChange(!isMatomoEnabled)}
                />
                <label htmlFor="Matomo" data-on-text="På" data-off-text="Av" />
                <div className="toggle-button__icon" />
              </div>

              <div className="cookie-details">
                <h4 className="cookie-title">
                  <span className="cookie-name">KF Analytics</span>
                </h4>
                <p className="cookie-provider">Mottaker: Matomo</p>
                <div className="cookie-sub-list">
                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_id</span>
                    <span className="cookie-sub-details">
                      Brukes til å skille brukere fra hverandre
                    </span>
                  </div>

                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_ref</span>
                    <span className="cookie-sub-details">
                      Brukes til å se hvilket nettsted eller søkemotor brukeren kom fra
                    </span>
                  </div>

                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_ses</span>
                    <span className="cookie-sub-details">
                      Brukes til å skille økter/besøk fra hverandre når bruker er logget inn i
                      løsningen.
                    </span>
                  </div>

                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">MATOMO_SESSID</span>
                    <span className="cookie-sub-details">
                      Sikkerhet. Lagrer ingen brukerdata som kan identifisere besøkende.
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {matomoBase && isMatomoEnabled ? (
            <iframe
              title="Tracking Opt-Out"
              className="comments"
              style={{
                border: 0,
                height: 0
              }}
              src={`https://${matomoBase}/index.php?module=CoreAdminHome&action=optOut&language=nb`}
            />
          ) : null}

          {!matomoBase && 'Kunne ikke hente informasjon fra statistikktjeneste.'}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button control onClick={toggleHide}>
          Lagre
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default OutOutModal;
