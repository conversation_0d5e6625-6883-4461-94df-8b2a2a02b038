/* eslint-env jest */
import '@babel/polyfill';

import reducer, { initialState } from '../reducer';
import * as actions from '../actions';
import { setOrganizationSuccess } from '../../App/actions';

const handbooks = {
  1: { id: 'hb1', title: 'handbook' },
  2: { id: 'hb2', title: 'HMT' },
  3: { id: 'hb3', title: 'Handbook without children' }
};

const chapters = {
  chap1: { id: 'chap1', title: 'Avvik', handbookId: 'hb1' },
  chap2: { id: 'chap2', title: 'Risko', handbookId: 'hb2' },
  chap3: { id: 'chap3', title: 'Chapter without children', handbookId: 'hb1' }
};

const sections = {
  section1: { id: 'section1', title: 'Avvik skjema', handbookId: 'hb1' },
  section2: { id: 'section2', title: 'Brann', handbookId: 'hb2' }
};

describe('handbooksReducer', () => {
  let state;

  // This gives us some state to work with. Most of the tests needs data in the store.
  beforeEach(() => {
    state = {
      ...initialState,
      handbooks,
      chapters,
      sections
    };
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should return the initial state when changing the external org', () => {
    expect(reducer(undefined, setOrganizationSuccess)).toEqual(initialState);
  });

  it('should handle fetch handbooks success', () => {
    const next = reducer(
      state,
      actions.fetchLocalHandbooksSuccess({
        handbooks: Object.values(handbooks),
        sections: Object.values(sections),
        chapters: Object.values(chapters)
      })
    );

    expect(next).toMatchSnapshot();
  });

  it('should handle subscribe and unsubscribe success', () => {
    let next = reducer(state, actions.subscribeSuccess('hb1', true));
    expect(next).toMatchSnapshot();

    next = reducer(next, actions.subscribeSuccess('hb1', false));
    expect(next).toMatchSnapshot();
  });

  it('should handle create local handbook success', () => {
    const next = reducer(
      state,
      actions.saveLocalHandbookSuccess({
        id: 'new',
        title: 'New test handbook'
      })
    );
    expect(next).toMatchSnapshot();
  });

  it('should handle update local handbook success', () => {
    const handbook = {
      ...handbooks.hb1,
      title: 'Updated title'
    };

    const next = reducer(state, actions.saveLocalChapterSuccess(handbook));
    expect(next).toMatchSnapshot();
  });

  it('should handle delete local handbook success', () => {
    const next = reducer(state, actions.deleteLocalHandbookSuccess('hb1'));
    expect(next).toMatchSnapshot();
  });

  it('should handle create local chapter success', () => {
    const newChapter = { id: 'new', title: 'New test handbook' };
    const next = reducer(state, actions.saveLocalChapterSuccess(newChapter));
    expect(next).toMatchSnapshot();
  });

  it('should handle update local chapter success', () => {
    const chapter = {
      ...chapters.chap1,
      title: 'Updated title'
    };

    const next = reducer(state, actions.saveLocalChapterSuccess(chapter));
    expect(next).toMatchSnapshot();
  });

  it('should handle create local section success', () => {
    const newSection = { id: 'new', title: 'New test handbook' };

    const next = reducer(state, actions.saveLocalChapterSuccess(newSection));
    expect(next).toMatchSnapshot();
  });

  it('should handle update local section success', () => {
    const section = {
      ...sections.section1,
      title: 'Updated title'
    };

    const next = reducer(state, actions.saveLocalChapterSuccess(section));
    expect(next).toMatchSnapshot();
  });

  it('should handle delete local section success', () => {
    const next = reducer(state, actions.deleteLocalSectionSuccess('section1'));
    expect(next).toMatchSnapshot();
  });

  it('should handle fetch section success and update the section with text', () => {
    expect(state.sections.section1.text).toBeUndefined();

    const section = { ...sections.section1, text: '<b>Text</b>' };

    const next = reducer(state, actions.fetchSectionSuccess(section));
    expect(next).toMatchSnapshot();
  });
});
