/**
 * COMMON WEBPACK CONFIGURATION
 */

const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = options => ({
  // Prefix '@babel/polyfill' to multiple entry
  entry: Object.entries(options.entry).reduce((acc, value) => {
    acc[value[0]] = ['@babel/polyfill'].concat(value[1]);
    return acc;
  }, {}),
  output: Object.assign(
    {
      // Compile into js/build.js
      path: path.resolve(process.cwd(), 'build'),
      publicPath: '/'
    },
    options.output
  ), // Merge with env dependent settings
  module: {
    rules: [
      {
        test: /\.jsx?$/, // Transform all .js and .jsx files required somewhere with Babel
        use: 'babel-loader', // See .babelrc
        exclude: /node_modules/
      },
      {
        // TinyMCE -- enable import/export of TinyMCE itself
        test: require.resolve('tinymce/tinymce'),
        use: ['imports-loader?this=>window', 'exports-loader?window.tinymce']
      },
      {
        // TinyMCE -- enable import themes/plugins to TinyMCe
        test: /tinymce[\\/](themes|plugins)[\\/]/,
        use: ['imports-loader?this=>window']
      },
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader
          },
          'css-loader'
        ]
      },
      {
        test: /\.html$/,
        use: 'html-loader'
      }
    ]
  },
  plugins: options.plugins.concat([
    // Always expose NODE_ENV to webpack, in order to use `process.env.NODE_ENV`
    // inside your code for any environment checks; UglifyJS will automatically
    // drop any unreachable code.
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: JSON.stringify(process.env.NODE_ENV)
      }
    }),
    new webpack.NamedModulesPlugin()
    // prints more readable module names in the browser console on HMR updates
  ]),
  resolve: {
    modules: ['app', 'node_modules'],
    extensions: ['.js', '.jsx', '.json'],
    mainFields: ['browser', 'jsnext:main', 'main']
  },
  devtool: options.devtool,
  target: 'web' // Make web variables accessible to webpack, e.g. window
});
