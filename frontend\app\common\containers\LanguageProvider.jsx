import React from 'react';
import PropTypes from 'prop-types';
import { IntlProvider } from 'react-intl';
import { connect } from 'react-redux';

import { translationMessages } from '../i18n';

const LanguageProvider = ({ locale, children }) => {
  // Make sure we supply a valid locale setting, otherwise the UI won't render properly
  const withDefaultLocale = locale !== 'nb' && locale !== 'nn' ? 'nb' : locale;

  return (
    <IntlProvider
      locale={withDefaultLocale}
      key={withDefaultLocale}
      messages={translationMessages[withDefaultLocale]}
    >
      {React.Children.only(children)}
    </IntlProvider>
  );
};

LanguageProvider.propTypes = {
  locale: PropTypes.oneOf(['nb', 'nn']).isRequired,
  children: PropTypes.element.isRequired
};

const makeMapStateToProps = selector => {
  const mapStateToProps = state => ({
    locale: selector(state)
  });
  return mapStateToProps;
};

export default function(localeSelector) {
  return connect(makeMapStateToProps(localeSelector))(LanguageProvider);
}
