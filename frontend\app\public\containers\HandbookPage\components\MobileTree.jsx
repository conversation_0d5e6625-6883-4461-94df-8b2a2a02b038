// @flow

import React from 'react';
import Sticky from 'react-sticky-el';
import { Column, Card, Icon } from 'kf-bui';

import type { ChapterType } from '../../../../types';

import Tree from '../../Tree';

type Props = {
  toggleTree: () => void,
  showTree: boolean,
  activeSections: string[],
  expandedChapters: string[],
  onCollapseClick: (item: ChapterType) => void
};

const MobileTree = ({
  toggleTree,
  showTree,
  activeSections,
  expandedChapters,
  onCollapseClick
}: Props) => (
  <Column hiddenTablet style={{ width: '100%', margin: '0', padding: '0' }}>
    <Sticky style={{ borderTopWidth: '0.75em' }}>
      <Card style={{ boxShadow: 'none' }}>
        <Card.Header
          as="header"
          title=" "
          style={{
            boxShadow: 'none',
            borderBottomStyle: 'solid',
            borderBottomColor: 'rgba(10, 10, 10, 0.1)',
            borderBottomWidth: '1px'
          }}
        >
          <Card.Header.Icon onClick={toggleTree} aria-label="Vis/lukk tre">
            <a>
              <Icon icon="align-justify" />
            </a>
          </Card.Header.Icon>
        </Card.Header>
      </Card>
    </Sticky>

    {showTree && (
      <Tree
        onClick={toggleTree}
        activeSections={activeSections}
        expandedChapters={expandedChapters}
        onCollapseClick={onCollapseClick}
      />
    )}
  </Column>
);

export default MobileTree;
