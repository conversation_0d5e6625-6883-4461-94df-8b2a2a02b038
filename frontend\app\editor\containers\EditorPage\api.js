import axios from 'axios';

const BASE_URL = '/handbooks/local';

export function fetchLocalHandbooks() {
  return axios.get(BASE_URL).then(res => res.data);
}

export function fetchLocalEditors(handbookId) {
  return axios.get(`${BASE_URL}/editors/${handbookId}`).then(res => new Set(res.data));
}
export function insertLocalEditor(localEditor) {
  return axios.post(`${BASE_URL}/editors/`, localEditor).then(res => res.data);
}
export function deleteLocalEditor(id) {
  return axios.delete(`${BASE_URL}/editors/${id}`).then(res => res);
}

export function fetchComments(handbookId) {
  return axios.get(`${BASE_URL}/comments/${handbookId}`).then(res => res.data);
}
export function persistComment(comment) {
  return axios.post(`${BASE_URL}/comments/`, comment).then(res => res.data);
}
export function deleteComment(id) {
  return axios.delete(`${BASE_URL}/comments/${id}`).then(res => res);
}

/**
 * @param {String} sectionId
 */
export function fetchSection(sectionId) {
  return axios.get(`${BASE_URL}/section/${sectionId}/`).then(res => res.data);
}
/**
 * @param {String} chapterId
 */
export function fetchChapter(chapterId) {
  return axios.get(`${BASE_URL}/chapter/${chapterId}/`).then(res => res.data);
}
/**
 * @param {String} handbookId
 */
export function fetchHandbook(handbookId) {
  return axios.get(`${BASE_URL}/handbook/${handbookId}/`).then(res => res.data);
}

export function fetchSectionVersions(sectionId) {
  return axios.get(`${BASE_URL}/section/versions/${sectionId}/`).then(res => res.data);
}

export function fetchFullSection(sectionVersionId) {
  return axios.get(`${BASE_URL}/section/version/${sectionVersionId}/`).then(res => res.data);
}

/**
 * @param {Object} handbook
 */
export function saveLocalHandbook(handbook) {
  return axios
    .post(`${BASE_URL}/handbook/${handbook.id ? handbook.id : ''}`, handbook)
    .then(res => res.data);
}

/**
 * @param {Array} ids
 */
export function saveSortOrder(ids) {
  return axios.post(`${BASE_URL}/sort/`, ids).then(res => res.data);
}

/**
 * @param {id} handbookId
 */
export function deleteLocalHandbook(id) {
  return axios.delete(`${BASE_URL}/${id}`).then(res => res.data);
}

/**
 * @param {id} chapterId
 */
export function deleteLocalChapter(id) {
  return axios.delete(`${BASE_URL}/chapter/${id}`).then(res => res.data);
}

/**
 * @param {Object} chapter
 */
export function saveLocalChapter(chapter, centralChange) {
  return axios
    .post(`${BASE_URL}/chapter${chapter.id ? `/${chapter.id}/${centralChange}` : ''}`, chapter)
    .then(res => res.data);
}

/**
 * @param {Object} section
 */
export function saveLocalSection(section, centralChange, centralTextChange) {
  return axios
    .post(
      `${BASE_URL}/section${
        section.id ? `/${section.id}/${centralChange}/${centralTextChange}` : ''
      }`,
      section
    )
    .then(res => res.data);
}

/**
 * @param {id} sectionId
 */
export function deleteLocalSection(id) {
  return axios.delete(`${BASE_URL}/section/${id}`).then(res => res.data);
}

/**
 * @param {id} handbookId
 */
export function subscribeToHandbook(id, subscribe) {
  if (subscribe) {
    return axios.post(`${BASE_URL}/subscribe/${id}`).then(res => res.data);
  }
  return axios.post(`${BASE_URL}/unsubscribe/${id}`).then(res => res.data);
}

/**
 * @returns {Set} handbookIds
 */
export function fetchSubscriptions() {
  return axios.get(`${BASE_URL}/subscriptions/`).then(res => new Set(res.data));
}
