// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`fetchCentralHandbooks calls the API and notifies the reducer on success 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchAllCentralHandbooks",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": Object {
          "payload": Array [
            "HMS",
            "HMT",
          ],
          "type": "CentralHandbooksPage/FETCH_CENTRAL_HANDBOOKS_SUCCESS",
        },
        "channel": null,
      },
    },
  ],
}
`;

exports[`fetchCentralHandbooks can fail gracefully 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchAllCentralHandbooks",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": "@@redux-saga-test-plan/json/function/<anonymous>",
        "channel": null,
      },
    },
  ],
}
`;
