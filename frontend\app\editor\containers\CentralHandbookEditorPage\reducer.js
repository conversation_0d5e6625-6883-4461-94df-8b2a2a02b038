import { handleActions } from 'redux-actions';
import { merge, omit, omitBy } from 'lodash';
import {
  saveCentralHandbookSuccess,
  fetchCentralHandbooksSuccess,
  saveCentralChapterSuccess,
  fetchCentralChaptersSuccess,
  saveCentralSectionSuccess,
  fetchCentralSectionsSuccess,
  deleteCentralHandbookSuccess,
  deleteCentralChapterSuccess,
  deleteCentralSectionSuccess,
  fetchAllReadingLinksSuccess
} from './actions';

export const initialState = {
  centralHandbooks: {},
  centralChapters: {},
  centralSections: {},
  readingLinks: {}
};

export default handleActions(
  {
    [fetchCentralHandbooksSuccess]: (state, { payload: handbooks }) => ({
      ...state,
      centralHandbooks: handbooks.reduce((acc, handbook) => {
        return { ...acc, [handbook.id]: { ...handbook, type: 'HANDBOOK' } };
      }, {})
    }),
    [saveCentralHandbookSuccess]: (state, { payload: handbook }) => ({
      ...state,
      centralHandbooks: {
        ...state.centralHandbooks,
        [handbook.id]: { ...handbook, type: 'HANDBOOK' }
      }
    }),
    [deleteCentralHandbookSuccess]: (state, { payload: handbookId }) => ({
      ...state,
      centralHandbooks: omit(state.centralHandbooks, [handbookId]),
      centralChapters: omitBy(
        state.centralChapters,
        chapter => chapter.centralHandbookId === handbookId
      ),
      centralSections: omitBy(
        state.centralSections,
        section => section.centralHandbookId === handbookId
      )
    }),

    [fetchCentralChaptersSuccess]: (state, { payload: chapters }) => ({
      ...state,
      centralChapters: merge(
        {},
        state.centralChapters,
        chapters.reduce((acc, chapter) => {
          acc[chapter.id] = { ...chapter, type: 'CHAPTER' };
          return acc;
        }, {})
      )
    }),
    [saveCentralChapterSuccess]: (state, { payload: chapter }) => ({
      ...state,
      centralChapters: {
        ...state.centralChapters,
        [chapter.id]: { ...chapter, type: 'CHAPTER' }
      }
    }),
    [deleteCentralChapterSuccess]: (state, { payload: chapterId }) => ({
      ...state,
      centralChapters: omit(state.centralChapters, [chapterId])
    }),

    [fetchCentralSectionsSuccess]: (state, { payload: sections }) => ({
      ...state,
      centralSections: merge(
        {},
        state.centralSections,
        sections.reduce((acc, section) => {
          acc[section.id] = { ...section, type: 'SECTION' };
          return acc;
        }, {})
      )
    }),
    [saveCentralSectionSuccess]: (state, { payload: section }) => ({
      ...state,
      centralSections: {
        ...state.centralSections,
        [section.id]: { ...section, type: 'SECTION' }
      }
    }),
    [deleteCentralSectionSuccess]: (state, { payload: sectionId }) => ({
      ...state,
      centralSections: omit(state.centralSections, [sectionId])
    }),
    [fetchAllReadingLinksSuccess]: (state, { payload: readingLinks }) => ({
      ...state,
      readingLinks
    })
  },
  initialState
);
