// @flow

import React from 'react';
import Chapter from './Chapter';
import Section from './Section';

import type { ChapterType, SectionType } from '../../../../types';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict

type Props = {
  chaptersAndSections: (ChapterType | SectionType)[],
  setSection: (section: SectionType) => void,
  removeSection: (section: SectionType) => void
};

class ChildrenList extends React.Component<Props> {
  shouldComponentUpdate = ({ chaptersAndSections: nextChaptersAndSections }: Props) =>
    JSON.stringify(this.props.chaptersAndSections.map(cs => cs.id)) !==
    JSON.stringify(nextChaptersAndSections.map(cs => cs.id));

  render() {
    const { chaptersAndSections, setSection, removeSection } = this.props;

    return (
      <div>
        {chaptersAndSections.map(child =>
          child.type === 'LOCAL_CHAPTER' ? (
            <Chapter
              key={child.id}
              chapter={child}
              setSection={setSection}
              removeSection={removeSection}
            />
          ) : (
            <Section
              key={child.id}
              sectionId={child.id}
              setSection={setSection}
              removeSection={removeSection}
            />
          )
        )}
      </div>
    );
  }
}

export default ChildrenList;
