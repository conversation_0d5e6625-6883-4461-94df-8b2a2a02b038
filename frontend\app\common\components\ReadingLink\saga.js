import { call, put, takeLatest } from 'redux-saga/effects';
import { toast } from 'kf-toaster';
import * as api from './api';
import * as actions from './actions';

export function* retrieveReadingLink({ payload: regulationId }) {
  try {
    const link = yield call(api.retrieveReadingLink, regulationId);
    yield put(actions.retrieveReadingLinkSuccess(link));
  } catch (error) {
    yield put(toast.backendError(error, 'En feil inntraff', 'Klarte ikke hente leselenke'));
  }
}

export function* deleteReadingLink({ payload: link }) {
  try {
    yield call(api.deleteReadingLink, link.id);
    yield put(actions.deleteLinkSuccess(link));
    yield put(toast.success('Leselenke slettet'));
  } catch (error) {
    yield put(toast.backendError(error, 'En feil inntraff', 'Klarte ikke slette leselenke'));
  }
}

export function* persistReadingLink({ payload: link }) {
  try {
    const persistedLink = yield call(api.persistLink, link);
    yield put(actions.persistReadingLinkSuccess(persistedLink));
    yield put(toast.success('Leselenke generert'));
  } catch (error) {
    yield put(toast.backendError(error, 'En feil inntraff', 'Klarte ikke opprette leselenke'));
  }
}

export default function* rootSaga() {
  yield takeLatest(actions.retrieveReadingLink, retrieveReadingLink);
  yield takeLatest(actions.persistReadingLink, persistReadingLink);
  yield takeLatest(actions.deleteLink, deleteReadingLink);
}
