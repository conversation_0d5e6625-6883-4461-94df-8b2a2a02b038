/**
 * DEVELOPMENT WEBPACK CONFIGURATION
 */

const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

function generateEntry(template) {
  path.join(process.cwd(), `app/${template}/index.jsx`) // Start with app/{editor|public}/index.jsx
}

module.exports = require('./webpack.config.base')({
  // Add hot reloading in development
  entry: {
    editor: generateEntry('editor'),
    public: generateEntry('public')
  },

  // Don't use hashes in dev mode for better performance
  output: {
    filename: '[name].js',
    chunkFilename: '[name].chunk.js',
    publicPath: '/handboker/'

  },

  // Add development plugins
  plugins: [
    new webpack.HotModuleReplacementPlugin(), // Tell webpack we want hot reloading
    new webpack.NoEmitOnErrorsPlugin(),
    new HtmlWebpackPlugin({
      xhtml: true,
      inject: true, // Inject all files that are generated by webpack, e.g. bundle.js
      template: templateContent('editor'),
      excludeChunks: ['public']
    }),
    new HtmlWebpackPlugin({
      xhtml: true,
      inject: true, // Inject all files that are generated by webpack, e.g. bundle.js
      template: './app/editor/index.html',
      excludeChunks: ['editor']
    }),
    new MiniCssExtractPlugin({
      filename: 'css/[name].css',
      chunkFilename: 'css/[name].css'
    })
  ],

  // Load the CSS in a style tag in development
  cssLoaders: ['style-loader', 'css-loader'],

  // Emit a source map for easier debugging
  devtool: 'cheap-module-eval-source-map',

  optimization: {
    minimizer: [
      new UglifyJsPlugin({
        // react-intl skaper en feil i UglifyJs, da UglifyJs ikke forstår ES6
        exclude: [/js\/npm.react-intl/]
      })
    ],
  }
});

/**
 * We dynamically generate the HTML content in development
 */
function templateContent(template) {
  const html = fs
    .readFileSync(path.resolve(process.cwd(), `app/${template}/index.html`))
    .toString();

  return html;
}
