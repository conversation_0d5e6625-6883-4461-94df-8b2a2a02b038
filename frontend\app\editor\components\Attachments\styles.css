.attachment-modal .modal-card-body {
  padding: 24px 48px;
  border-radius: 16px;
}

.attachment-modal .title {
  text-align: center;
}

.file-upload-wrapper {
  background-color: #ffffff;
  border: 2px dashed #9a9999;
  padding: 30px;
  text-align: center;
  margin-top: 24px;
  margin-bottom: 24px;
  border-radius: 16px;
}

.drag-drop-area {
  cursor: pointer;
}

.drag-drop-area p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
}

.upload-link {
  text-decoration: underline;
  font-weight: bold;
  color: #050037;
  cursor: pointer;
  border: none;
  background: none;
  padding: 4px 10px;
  font-size: 16px;
  margin-bottom: 10px;
  transition: ease-in-out 0.3s;
  border-radius: 4px;
}

.upload-link:hover {
  background-color: #f7f7f7;
  color: #0000ff;
}

p.or-text {
  margin: 10px 0;
}

.file-list {
  margin-top: 20px;
  max-height: 300px;
  overflow: auto;
  margin-left: -16px;
  margin-right: -16px;
  padding: 0 10px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 6px;
  gap: 12px;
  border-radius: 4px;
  transition: ease-in-out 0.2s;
}

.file-item:hover {
  background-color: #f7f7f7;
}

.attachments-action-wrapper {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.attachments-action-wrapper .button {
  border-radius: 40px;
  min-width: 142px;
}

.attachment-publish-btn {
  background-color: #d6f1d0;
  color: #050037;
}

.remove-file-button {
  border: none;
  background: none;
  color: #000000;
  font-weight: bold;
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  transition: ease-in-out 0.2s;
  border-radius: 4px;
}

.remove-file-button .icon {
  border: 2px solid #000000;
  border-radius: 50%;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.remove-file-button:hover {
  color: #cd5c5c;
}

.remove-file-button:hover .icon {
  border: 2px solid #cd5c5c;
}

.remove-file-button .icon .fa.fa-close {
  font-size: 15px;
  position: relative;
  height: 16px;
}

.file-list-title h3 {
  font-size: 28px;
  font-weight: 400;
  line-height: 33.89px;
  text-align: center;
}

.attachment-file-name {
  font-size: 18px;
  font-weight: 400;
  line-height: 21.78px;
  text-align: left;
  color: #363636;
}

.file-item.new-item {
  background-color: #fafaff;
}

.file-item.new-item * {
  font-weight: 600;
}

.file-more-info {
  font-size: 16px;
  font-style: italic;
  font-weight: 300;
  line-height: 19.36px;
  text-align: left;
  color: #363636;
}

.error-message {
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
}

.error-message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 28px;
  padding: 0 2rem;
  margin-bottom: 34px;
}

.attachment-file-details {
  flex: 1;
  cursor: pointer;
  transition: ease-in-out 0.2s;
}

.attachment-file-details:hover * {
  font-weight: 500;
  text-decoration: underline;
}

.attachment-modal ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.attachment-modal ::-webkit-scrollbar-track {
  background: #ffffff;
}

.attachment-modal ::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 9px;
}

.attachment-modal ::-webkit-scrollbar-thumb:hover {
  background: #d1d1d1;
}

.attachment-modal ::-webkit-scrollbar-thumb:active {
  background: #d1d1d1;
}

.oversized-file {
  background-color: #f5dede;
}

.oversized-file:hover * {
  font-weight: inherit;
  background-color: #f5dede;
  text-decoration: none;
}

.oversized-file:hover .file-more-info {
  font-weight: 300;
}

.oversized-file:hover .attachment-file-details {
  cursor: default;
}

.oversized-file:hover .attachment-file-name {
  font-weight: 400;
}

.oversized-file:hover .file-oversized-error {
  font-weight: 600;
}

.oversized-file:hover {
  background-color: #f5dede;
}

.file-oversized-error-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
}

.file-oversized-error {
  font-weight: 600;
  font-style: normal;
  color: #b20129;
}

.file-oversized-error-wrapper svg {
  width: 18px;
}

.attachment-loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  gap: 12px;
}

.attachment-loading-message .loader {
  height: 1.5em;
  width: 1.5em;
}

.add-attachment-btn svg {
  width: 9px;
  margin-right: 5px;
}

.attachment-count {
  background-color: #d6f1d0;
  margin-left: 5px;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #363636;
  min-width: 20px;
  text-align: center;
}

.toggle-cookies-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  width: fit-content;
  transition: background-color 0.1s ease;
  padding-right: 6px;
  border-radius: 4px;
}

.toggle-cookies-container:hover {
  background-color: #d6d6e5;
}

.toggle-cookies-container i {
  font-size: 14px;
  transition: transform 0.6s ease;
  cursor: pointer;
}

.toggle-cookies-container i.rotate {
  transform: rotate(180deg);
}

.cookies-wrapper {
  overflow: hidden;
  transition: max-height 0.6s ease;
}

.cookie-type-title {
  margin: 24px 0;
  font-size: 16px;
  font-weight: 700;
  text-align: left;
}

.cookie-type-section-container label {
  display: flex;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.cookie-type-section-container label input {
  position: absolute;
  left: -9999px;
}

.cookie-type-section-container label input:checked + span {
  background-color: #050037;
  color: #ffffff;
}

.cookie-type-section-container label input:checked + span:before {
  box-shadow: inset 0 0 0 0.4375em #00005c;
}

.cookie-type-section-container label span {
  display: flex;
  align-items: center;
  border-radius: 99em;
  transition: 0.25s ease;
  padding: 8px 32px;
  justify-content: center;
  background-color: #d9d9d9;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  color: #021815;
}

.cookie-type-section-container label span:hover {
  background-color: #d6d6e5;
}

.cookie-type-section-container {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  justify-content: center;
}

.cookie-desc {
  margin-bottom: 12px;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc {
  text-decoration: underline;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc:hover {
  color: #3273dc;
}

.toggle-cookies-button {
  font-size: 16px;
  font-weight: 700;
  line-height: 19.36px;
  text-align: left;
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #363636;
}

.cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
}

/* Circular Progress Bar */
.circular-progress {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 44px;
  border-radius: 50%;
}
.circular-progress svg {
  transform: rotate(270deg);
}
.circular-progress circle {
  stroke-width: 6;
  fill: none;
  stroke-linecap: round;
}
.circular-progress circle:nth-of-type(1) {
  stroke: #dee2e6;
}
.circular-progress circle:nth-of-type(2) {
  stroke: #050037;
  stroke-dasharray: 251.4285714286;
  stroke-dashoffset: 75.4285714286;
}
.circular-progress .pct {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
}

#slider {
  margin: 0 50px;
  width: 50%;
}
#slider::-moz-range-thumb {
  background-color: #050037;
}
#slider::-webkit-slider-thumb {
  background-color: #050037;
}
#slider::-ms-thumb {
  background-color: #050037;
}
#slider:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(216, 80, 58, 0.25);
}
#slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(216, 80, 58, 0.25);
}
#slider:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(216, 80, 58, 0.25);
}
/* Circular Progress Bar */
