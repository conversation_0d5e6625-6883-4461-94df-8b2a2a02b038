// @flow
import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { Modal, Field, Label, Button, Checkbox, DatePicker } from 'kf-bui';
import { publiserHandbook } from '../api';

import type { CentralHandbookType } from '../../../../types';

type Props = {
  handbook: CentralHandbookType,
  toggleHide: () => void,
  handlePublish: () => void
};
const PubliseringsModal = ({ handbook, toggleHide, handlePublish }: Props) => {
  const [publiciseNow, setPubliciseNow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { handleSubmit, errors, control } = useForm();

  const onSubmit = e => {
    const dato = e.publiseringsdato;
    if (publiciseNow || dato) {
      setIsLoading(true);
      publiserHandbook(handbook, dato)
        .then(() => {
          setTimeout(() => {
            setIsLoading(false);
            handlePublish();
          }, 500);
        })
        .catch(error => {
          setIsLoading(false);
          console.log(error);
        });
    }
  };

  return (
    <Modal onClose={toggleHide} autoFocus={false}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Modal.Header>
          <Modal.Title>Publiser håndbok</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Field>
            <Label htmlFor="title">Publisering av {handbook.title} </Label>
          </Field>
          <Field>
            <Checkbox checked={publiciseNow} onChange={e => setPubliciseNow(e.target.checked)}>
              {' '}
              Publiser nå
            </Checkbox>
          </Field>
          {!publiciseNow ? (
            <Field>
              <Label htmlFor="publiseringsdato">Dato for publisering</Label>
              <Controller
                name="publiseringsdato"
                id="publiseringsdato"
                as={DatePicker}
                control={control}
                rules={{
                  required: 'Du må sette en dato for publisering',
                  validate: value => {
                    return new Date(value) > Date.now() || 'Dato må være i fremtiden';
                  }
                }}
              />
              {errors.publiseringsdato && <span>{errors.publiseringsdato.message}</span>}
            </Field>
          ) : (
            undefined
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={toggleHide}>Avbryt</Button>
          <Button type="submit" loading={isLoading}>
            Publiser
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default PubliseringsModal;
