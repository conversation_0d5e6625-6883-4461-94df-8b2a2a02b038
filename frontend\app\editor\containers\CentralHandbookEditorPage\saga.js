import { call, put, takeLatest } from 'redux-saga/effects';
import { toast } from 'kf-toaster';

import * as api from './api';
import * as actions from './actions';

export function* fetchCentralHandbooks() {
  try {
    const centralHandbooks = yield call(api.fetchAllCentralHandbooks);
    yield put(actions.fetchCentralHandbooksSuccess(centralHandbooks));
  } catch (error) {
    yield put(toast.error('Kunne ikke hente sentrale håndbøker'));
  }
}

export function* fetchAllReadingLinks() {
  try {
    const readingLinks = yield call(api.fetchAllReadingLinks);
    yield put(actions.fetchAllReadingLinksSuccess(readingLinks));
  } catch (error) {
    yield put(toast.error('Kunne ikke hente leselenker '));
    yield put(toast.error(error));
  }
}

export function* fetchPublishedCentralHandbooks() {
  try {
    const centralHandbooks = yield call(api.fetchPublishedHandbooks);
    yield put(actions.fetchCentralHandbooksSuccess(centralHandbooks));
  } catch (error) {
    yield put(toast.error('Kunne ikke hente publiserte sentrale håndbøker'));
  }
}

export function* fetchCentralChapters() {
  try {
    const centralChapters = yield call(api.fetchAllCentralChapters);
    yield put(actions.fetchCentralChaptersSuccess(centralChapters));
  } catch (error) {
    yield put(toast.error('Kunne ikke hente sentrale kapitler'));
  }
}

export function* fetchCentralSections() {
  try {
    const centralSections = yield call(api.fetchAllCentralSections);
    yield put(actions.fetchCentralSectionsSuccess(centralSections));
  } catch (error) {
    yield put(toast.error('Kunne ikke hente sentrale avsnitt'));
  }
}

export function* saveCentralHandbook({ payload: handbook }) {
  try {
    const savedHandbook = yield call(api.saveCentralHandbook, handbook);
    yield put(actions.saveCentralHandbookSuccess(savedHandbook));
  } catch (error) {
    yield put(toast.error('Kunne ikke lagre sentral håndbok'));
  }
}
export function* saveCentralChapter({ payload: chapter }) {
  try {
    const savedChapter = yield call(api.saveCentralChapter, chapter);
    yield put(actions.saveCentralChapterSuccess(savedChapter));
  } catch (error) {
    yield put(toast.error('Kunne ikke lagre sentral kapittel'));
  }
}
export function* saveCentralSection({ payload: centralSection }) {
  try {
    const savedSection = yield call(api.saveCentralSection, centralSection);
    yield put(actions.saveCentralSectionSuccess(savedSection));
  } catch (error) {
    yield put(toast.error('Kunne ikke lagre sentralt avsnitt'));
  }
}

export function* deleteCentralHandbook({ payload: centralHandbookId }) {
  try {
    yield call(api.deleteCentralHandbook, centralHandbookId);
    yield put(toast.success('Slettet sentral håndbok'));
    yield put(actions.deleteCentralHandbookSuccess(centralHandbookId));
  } catch (error) {
    yield put(toast.error('Kunne ikke slette sentral håndbok.'));
  }
}

export function* deleteCentralChapter({ payload: centralChapterId }) {
  try {
    yield call(api.deleteCentralChapter, centralChapterId);
    yield put(toast.success('Slettet sentralt kapittel'));
    yield put(actions.deleteCentralChapterSuccess(centralChapterId));
  } catch (error) {
    yield put(toast.error('Kunne ikke slette sentralt kapittel.'));
  }
}

export function* deleteCentralSection({ payload: centralSectionId }) {
  try {
    yield call(api.deleteCentralSection, centralSectionId);
    yield put(toast.success('Slettet sentralt avsnitt'));
    yield put(actions.deleteCentralSectionSuccess(centralSectionId));
  } catch (error) {
    yield put(toast.error('Kunne ikke slette sentralt avsnitt.'));
  }
}

export function* sortCentralItems({ payload: centralIds }) {
  try {
    yield call(api.sortCentralItems, centralIds);
    yield call(fetchCentralChapters);
    yield call(fetchCentralSections);
  } catch (error) {
    yield put(toast.error('Kunne ikke oppdatere sortering'));
  }
}

export default function* rootSaga() {
  yield takeLatest(actions.fetchCentralHandbooks, fetchCentralHandbooks);
  yield takeLatest(actions.fetchAllReadingLinks, fetchAllReadingLinks);
  yield takeLatest(actions.fetchPublishedCentralHandbooks, fetchPublishedCentralHandbooks);
  yield takeLatest(actions.fetchCentralChapters, fetchCentralChapters);
  yield takeLatest(actions.fetchCentralSections, fetchCentralSections);

  yield takeLatest(actions.saveCentralHandbook, saveCentralHandbook);
  yield takeLatest(actions.saveCentralChapter, saveCentralChapter);
  yield takeLatest(actions.saveCentralSection, saveCentralSection);

  yield takeLatest(actions.deleteCentralHandbook, deleteCentralHandbook);
  yield takeLatest(actions.deleteCentralChapter, deleteCentralChapter);
  yield takeLatest(actions.deleteCentralSection, deleteCentralSection);

  yield takeLatest(actions.sortCentralItems, sortCentralItems);
}
