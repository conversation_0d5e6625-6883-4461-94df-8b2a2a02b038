// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`handbooksReducer should handle create local chapter success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
    "new": Object {
      "id": "new",
      "title": "New test handbook",
      "type": "LOCAL_CHAPTER",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle create local handbook success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
    "new": Object {
      "id": "new",
      "title": "New test handbook",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle create local section success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
    "new": Object {
      "id": "new",
      "title": "New test handbook",
      "type": "LOCAL_CHAPTER",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle delete local handbook success 1`] = `
Object {
  "chapters": Object {
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle delete local section success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle fetch handbooks success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
      "type": "LOCAL_CHAPTER",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
      "type": "LOCAL_CHAPTER",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
      "type": "LOCAL_CHAPTER",
    },
  },
  "handbooks": Object {
    "hb1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "hb2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "hb3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
      "type": "LOCAL_SECTION",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
      "type": "LOCAL_SECTION",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle fetch section success and update the section with text 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "text": "<b>Text</b>",
      "title": "Avvik skjema",
      "type": "LOCAL_SECTION",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle subscribe and unsubscribe success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {
    "hb1",
  },
}
`;

exports[`handbooksReducer should handle subscribe and unsubscribe success 2`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle update local chapter success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Updated title",
      "type": "LOCAL_CHAPTER",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle update local handbook success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
    "undefined": Object {
      "title": "Updated title",
      "type": "LOCAL_CHAPTER",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;

exports[`handbooksReducer should handle update local section success 1`] = `
Object {
  "chapters": Object {
    "chap1": Object {
      "handbookId": "hb1",
      "id": "chap1",
      "title": "Avvik",
    },
    "chap2": Object {
      "handbookId": "hb2",
      "id": "chap2",
      "title": "Risko",
    },
    "chap3": Object {
      "handbookId": "hb1",
      "id": "chap3",
      "title": "Chapter without children",
    },
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Updated title",
      "type": "LOCAL_CHAPTER",
    },
  },
  "handbooks": Object {
    "1": Object {
      "id": "hb1",
      "title": "handbook",
    },
    "2": Object {
      "id": "hb2",
      "title": "HMT",
    },
    "3": Object {
      "id": "hb3",
      "title": "Handbook without children",
    },
  },
  "localEditors": Set {},
  "localEditorsForOrg": Set {},
  "sections": Object {
    "section1": Object {
      "handbookId": "hb1",
      "id": "section1",
      "title": "Avvik skjema",
    },
    "section2": Object {
      "handbookId": "hb2",
      "id": "section2",
      "title": "Brann",
    },
  },
  "subscriptions": Set {},
}
`;
