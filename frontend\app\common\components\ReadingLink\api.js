import axios from 'axios';

const BASE_URL = '/handbooks/readinglink';

export function retrieveReadingLink(centralSectionId) {
  return axios.get(`${BASE_URL}/link/${centralSectionId}`).then(res => res.data);
}

export function persistLink(link) {
  return axios.post(`${BASE_URL}/`, link).then(res => res.data);
}

export function deleteReadingLink(linkId) {
  return axios.delete(`${BASE_URL}/${linkId}`);
}
