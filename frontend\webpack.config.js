const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

module.exports = env => {
  const isEnvProduction = env.production;
  // Inline source maps burde gi mer nøyaktige konsoll meldinger, inspect, og feilmeldinger (Men treigere reload)
  const inlineSource = env.inline ? 'inline' : 'eval';
  return {
    devtool: isEnvProduction ? undefined : `${inlineSource}-source-map`,
    mode: isEnvProduction ? 'production' : 'development',

    entry: {
      editor: ['@babel/polyfill', path.join(process.cwd(), 'app/editor/index.jsx')],
      public: ['@babel/polyfill', path.join(process.cwd(), 'app/public/index.jsx')]
    },

    // Utilize long-term caching by adding content hashes (not compilation hashes) to compiled assets
    output: {
      filename: isEnvProduction ? 'js/[name].[hash].js' : '[name].[hash].js',
      chunkFilename: isEnvProduction
        ? 'js/[name].[chunkhash].chunk.js'
        : '[name].[chunkhash].chunk.js',
      path: path.resolve(process.cwd(), 'build'),
      publicPath: isEnvProduction ? '/handboker/' : '/'
    },

    module: {
      rules: [
        {
          test: /\.jsx?$/, // Transform all .js and .jsx files required somewhere with Babel
          use: 'babel-loader', // See .babelrc
          exclude: /node_modules/
        },
        {
          // TinyMCE -- enable import/export of TinyMCE itself
          test: require.resolve('tinymce/tinymce'),
          use: ['imports-loader?this=>window', 'exports-loader?window.tinymce']
        },
        {
          // TinyMCE -- enable import themes/plugins to TinyMCe
          test: /tinymce[\\/](themes|plugins)[\\/]/,
          use: ['imports-loader?this=>window']
        },
        {
          test: /\.css$/,
          use: [
            {
              loader: MiniCssExtractPlugin.loader
            },
            'css-loader'
          ]
        },
        {
          test: /\.html$/,
          use: 'html-loader'
        }
      ]
    },

    plugins: [
      // Minify and optimize the index.html
      new HtmlWebpackPlugin({
        xhtml: true,
        inject: true,
        template: './app/editor/index.html',
        favicon: './favicon.ico',
        title: 'editor',
        filename: 'editor/index.html',
        excludeChunks: ['public'],
        minify: isEnvProduction
          ? {
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true
            }
          : false
      }),
      new HtmlWebpackPlugin({
        xhtml: true,
        inject: true,
        template: './app/public/index.html',
        favicon: './favicon.ico',
        title: 'public',
        filename: 'public/index.html',
        excludeChunks: ['editor'],
        minify: isEnvProduction
          ? {
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true
            }
          : false
      }),

      new MiniCssExtractPlugin({
        filename: isEnvProduction ? 'css/[name].css' : '[name].css',
        chunkFilename: isEnvProduction ? 'css/[name].css' : '[name].css'
      })
    ],

    optimization: isEnvProduction
      ? {
          minimizer: [
            new UglifyJsPlugin({
              // react-intl skaper en feil i UglifyJs, da UglifyJs ikke forstår ES6
              exclude: [/js\/npm.react-intl/]
            })
          ]
        }
      : undefined,

    resolve: {
      modules: ['app', 'node_modules'],
      extensions: ['.js', '.jsx', '.json'],
      mainFields: ['browser', 'jsnext:main', 'main'],
      alias: {
        'react-hook-form': 'react-hook-form/dist/index.ie11'
      }
    },
    devServer: {
      contentBase: path.join(__dirname, 'dist'),
      port: 3000,
      historyApiFallback: true,
      proxy: {
        '/handboker/**': {
          target: 'http://localhost:5600/handboker',
          pathRewrite: { '^/handboker': '' },
          secure: false,
          changeOrigin: true
        }
      }
    }
  };
};
