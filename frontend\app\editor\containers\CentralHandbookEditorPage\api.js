import axios from 'axios';

const BASE_URL = '/handbooks/central';

export function fetchAllCentralHandbooks() {
  return axios.get(`${BASE_URL}/handbooks/`).then(res => res.data);
}

/**
 * @param {String} centralHandbookId
 */
export function fetchAllHandbookVersions(centralHandbookId) {
  return axios.get(`${BASE_URL}/versions/${centralHandbookId}`).then(res => res.data);
}

export function fetchPublishedHandbooks() {
  return axios.get(`${BASE_URL}/published/`).then(res => res.data);
}

export function fetchAllCentralChapters() {
  return axios.get(`${BASE_URL}/chapters/`).then(res => res.data);
}

export function fetchAllCentralSections() {
  return axios.get(`${BASE_URL}/sections/`).then(res => res.data);
}

export function saveCentralHandbook(centralHandbook) {
  return axios.post(`${BASE_URL}/handbook/`, centralHandbook).then(res => res.data);
}

export function saveCentralChapter(centralChapter) {
  return axios.post(`${BASE_URL}/chapter/`, centralChapter).then(res => res.data);
}

export function saveCentralSection(centralSection) {
  return axios.post(`${BASE_URL}/section/`, centralSection).then(res => res.data);
}

export function deleteCentralHandbook(centralHandbookId) {
  return axios.delete(`${BASE_URL}/handbook/${centralHandbookId}`).then(res => res.data);
}
export function deleteCentralChapter(centralChapterId) {
  return axios.delete(`${BASE_URL}/chapter/${centralChapterId}`).then(res => res.data);
}
export function deleteCentralSection(centralSectionId) {
  return axios.delete(`${BASE_URL}/section/${centralSectionId}`).then(res => res.data);
}

export function sortCentralItems(idsToSort) {
  return axios.post(`${BASE_URL}/sort/`, idsToSort).then(res => res.data);
}

export function fetchAllReadingLinks() {
  return axios.get(`${BASE_URL}/readingLinks/`).then(res => res.data);
}

/**
 * @param {String} externalOrgId
 */
export function fetchAccess(externalOrgId) {
  return axios.get(`${BASE_URL}/access/${externalOrgId}/`).then(res => res.data);
}

/**
 * @param {String} externalOrgId
 * @param {Array} List of central handbook ids
 */
export function saveAccess(externalOrgId, ids) {
  return axios.post(`${BASE_URL}/access/${externalOrgId}/`, ids).then(res => res.data);
}

export function publiserHandbook(handbook, date) {
  return axios.post(`${BASE_URL}/publish/${handbook.id}/`, date).then(res => res.data);
}

export function pendingPublications(handbookId) {
  return axios.get(`${BASE_URL}/${handbookId}/pending-publications`).then(res => res.data);
}
