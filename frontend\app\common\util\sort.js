import { has } from 'lodash';

import norwegianSort from 'kf-norwegian-sort';

export default function sortBy(key) {
  return (a, b) => norwegianSort(a[key], b[key]);
}

/**
 * If sortOrder is defined, it trumphs the title
 * TODO: If one only one of the items has a sort order,
 * it should be sorted before the one without. (uncomment unit test when fixed)
 */
export function sortBySortOrderAndTitle(a, b) {
  if (has(a, 'sortOrder') && has(b, 'sortOrder')) {
    return a.sortOrder - b.sortOrder;
  }
  return norwegianSort(a.title, b.title);
}
