// @flow
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Link } from 'react-router-dom';
import { Button, Icon, Title, Control, Columns, Column, Group } from 'kf-bui';

import injectT from '../../../../common/i18n';
import Metadata from '../../../components/Metadata';
import DeleteButton from '../../../components/DeleteButton';
import { selectSpellcheckUrl } from '../../App/selectors';
import { makeSelectCentralSectionById, makeSelectReadingLinksBySectionId } from '../selectors';
import type { CentralSectionType, ReadingLinkType } from '../../../../types';
import PendingChangeWarning from '../../../components/PendingChangeWarning';
import Wysiwyg from '../../../components/Wysiwyg';
import { deleteCentralSection, fetchAllReadingLinks } from '../actions';
import ReadingLinkModal from '../../../../common/components/ReadingLink/ReadingLinkModal';

type Props = {
  centralSection: ?CentralSectionType,
  readingLinks: ReadingLinkType[],
  match: {
    params: {
      sectionId: string
    }
  },
  history: { replace: string => void },
  t: string => string,
  deleteCentralSectionFunction: (sectionId: string) => void,
  spellCheckUrl: string,
  fetchAllReadingLinksFunction: () => void
};

const CentralSectionScreen = ({
  centralSection,
  readingLinks,
  t,
  history,
  deleteCentralSectionFunction,
  spellCheckUrl,
  fetchAllReadingLinksFunction
}: Props) => {
  const [showLinkModal, linkModalToggler] = useState(false);

  const toggleLinkModal = () => {
    linkModalToggler(!showLinkModal);
  };

  useEffect(() => {
    fetchAllReadingLinksFunction();
  }, [fetchAllReadingLinksFunction]);

  if (!centralSection) return null;

  return (
    <div>
      <Columns>
        <Column>
          <Title>
            <Icon icon="file-text-o" size="small" style={{ marginRight: '1rem' }} />
            <span>{centralSection.title}</span>
          </Title>
        </Column>
      </Columns>
      <Columns>
        <Column>
          <Group>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralSection.centralHandbookId}/section/edit/${centralSection.id}`}
              size="small"
            >
              <Icon icon="pencil" size="small" />
              <span>{t('editButton')}</span>
            </Button>
            <Button control size="small" onClick={toggleLinkModal}>
              Hent lenke
            </Button>
          </Group>
        </Column>
        <Column narrow>
          <Group>
            <Control>
              <PendingChangeWarning
                element={centralSection}
                mergeLink={`/merge/section/${centralSection.id}/`}
              />
            </Control>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralSection.centralHandbookId}/section/move/${centralSection.id}`}
              size="small"
            >
              {t('moveButton')}
            </Button>
            <DeleteButton
              toDelete={centralSection}
              onDelete={() => {
                deleteCentralSectionFunction(centralSection.id);
                history.replace(
                  `/central-editor/${centralSection.centralHandbookId}/chapter/${centralSection.parentId}`
                );
              }}
              t={t}
              readlinkExists={readingLinks && readingLinks.length > 0}
            />
          </Group>
        </Column>
      </Columns>
      <Columns>
        <Column>
          <hr />
          <Metadata element={centralSection} />
          <hr />
        </Column>
      </Columns>
      <Wysiwyg value={centralSection.html} spellCheckerRpcUrl={spellCheckUrl} disabled />
      {showLinkModal && (
        <ReadingLinkModal centralSection={centralSection} toggleLinkModal={toggleLinkModal} />
      )}
    </div>
  );
};

const mapStateToProps = (state, props) => {
  const { sectionId } = props.match.params;

  const selectCentralSection = makeSelectCentralSectionById(sectionId);
  const selectReadingLinksBySectionId = makeSelectReadingLinksBySectionId(sectionId);
  return {
    centralSection: selectCentralSection(state),
    spellCheckUrl: selectSpellcheckUrl(state),
    readingLinks: selectReadingLinksBySectionId(state)
  };
};

const mapDispatchToProps = {
  deleteCentralSectionFunction: deleteCentralSection,
  fetchAllReadingLinksFunction: fetchAllReadingLinks
};

export default compose(
  injectT('editor.containers.SectionSelection'),
  connect(mapStateToProps, mapDispatchToProps)
)(CentralSectionScreen);
