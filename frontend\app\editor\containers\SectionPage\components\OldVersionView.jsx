// @flow
import React, { Fragment, useState, useEffect } from 'react';
import { Icon, Title, Field, Select, FormattedDate } from 'kf-bui';
import { FormattedMessage } from 'react-intl';
import Wysiwyg from '../../../components/Wysiwyg';
import Metadata from '../../../components/Metadata';
import { fetchSectionVersions, fetchFullSection } from '../../EditorPage/api';
import type { SectionType } from '../../../../types';

type Props = {
  section: SectionType,
  spellCheckUrl: string
};

const getLatestUpdate = section => {
  const date = new Date(section.updatedDate);
  const textDate = new Date(section.textUpdatedDate);
  return date > textDate ? date : textDate;
};

const OldVersionView = ({ section, spellCheckUrl }: Props) => {
  const [oldVersions, setOldVersions] = useState<SectionType[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<?SectionType>(undefined);

  useEffect(() => {
    fetchSectionVersions(section.id).then(retrievedVersions =>
      setOldVersions(retrievedVersions.sort((v1, v2) => getLatestUpdate(v2) - getLatestUpdate(v1)))
    );
  }, [section]);
  const fetchSection = sectionVersion => {
    fetchFullSection(sectionVersion.id).then(retrievedSection =>
      setSelectedVersion(retrievedSection)
    );
  };

  const versionRender = version => {
    const latestUpdate =
      new Date(version.updatedDate) > new Date(version.textUpdatedDate)
        ? { date: version.updatedDate, user: version.updatedBy }
        : { date: version.textUpdatedDate, user: version.textUpdatedBy };
    return (
      <Fragment>
        <FormattedDate value={latestUpdate.date} format="YYYY.MM.DD-HH:mm" />
        <span style={{ marginLeft: '5px' }}>oppdatert av {latestUpdate.user}</span>
      </Fragment>
    );
  };
  return (
    <div>
      <h2 style={{ 'font-weight': 'bold', 'font-size': '1.5em', 'margin-bottom': '0.5em' }}>
        <FormattedMessage id="editor.containers.SectionPage.components.OldVersionView.title" />
      </h2>
      {oldVersions.length > 0 ? (
        <Fragment>
          <Field style={{ 'margin-bottom': '1em' }}>
            {/* <Label htmlFor="sectionVersion"><FormattedMessage
                id="editor.containers.SectionPage.components.OldVersionView.pickVersion"/></Label> */}
            <FormattedMessage id="editor.containers.SectionPage.components.OldVersionView.pickVersion" />
            <Select
              id="sectionVersion"
              name="handbookVersion"
              aria-label="Velg versjon av håndboken du vil eksportere"
              options={oldVersions}
              value={selectedVersion}
              onChange={e => {
                fetchSection(e);
              }}
              valueKey="id"
              labelKey="updatedDate"
              optionRenderer={versionRender}
              valueRenderer={versionRender}
            />
          </Field>
          {selectedVersion && (
            <Fragment>
              <Title>
                <Icon icon="file-text-o" size="small" style={{ marginRight: '1rem' }} />
                <span>{selectedVersion.title}</span>
              </Title>
              <hr />
              <Metadata element={selectedVersion} />
              <hr />
              <label htmlFor="old-section-text" style={{ fontWeight: 'bold', fontSize: 'large' }}>
                Avsnittstekst
              </label>
              <Wysiwyg
                id="old-section-text"
                spellCheckerRpcUrl={spellCheckUrl}
                value={selectedVersion.text}
                disabled
                menubar=""
                toolbar={false}
              />
            </Fragment>
          )}
        </Fragment>
      ) : (
        <p>Det er ingen eldre versjoner av dette avsnittet</p>
      )}
    </div>
  );
};

export default OldVersionView;
