/* eslint-env jest */
import { initialState } from '../reducer';
import { selectSelectedElement } from '../selectors';

describe('UI selectors', () => {
  let state;
  beforeEach(() => {
    state = {
      ui: initialState
    };
  });

  describe('selectTreeNode', () => {
    it('selects the selected node ', () => {
      const handbook = { id: 'book1', title: 'handbook' };
      state.ui.selected = handbook;

      expect(selectSelectedElement(state)).toBe(handbook);
    });
  });
});
