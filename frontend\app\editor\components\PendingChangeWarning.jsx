import React, { useState, Fragment } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import { Tag, Icon, Button } from 'kf-bui';
import { HandbookShape, ChapterShape, SectionShape } from '../shapes';

import DeleteModal from './DeleteButton/DeleteModal';

const PendingChangeWarning = ({ element, mergeLink, onDelete, deleteText }) => {
  const [showDeleteModal, setShowModal] = useState(false);

  if (element.pendingChange) {
    return (
      <Link to={mergeLink} title="Håndter endring">
        <Tag color="warning">
          <Icon icon="exclamation" size="small" />{' '}
          <FormattedMessage id="editor.components.Metadata.mergeLink" />
        </Tag>
      </Link>
    );
  }

  if (element.pendingTextChange || element.pendingTitleChange) {
    return (
      <Link to={mergeLink} title="Håndter endring">
        <Tag color="warning">
          <Icon icon="exclamation" size="small" />{' '}
          <FormattedMessage id="editor.components.Metadata.mergeLink" />
        </Tag>
      </Link>
    );
  }

  if (element.pendingDeletion) {
    const { buttonText, title, text } = deleteText;
    return (
      <Fragment>
        <Button
          key="button"
          onClick={() => setShowModal(true)}
          icon="trash"
          outlined
          color="warning"
          size="small"
        >
          {buttonText}
        </Button>
        {showDeleteModal ? (
          <DeleteModal
            title={title}
            text={
              <div>
                <p>{text}</p>
              </div>
            }
            keepButton
            onDelete={onDelete}
            onHide={() => setShowModal(false)}
          />
        ) : null}
      </Fragment>
    );
  }
  return null;
};

PendingChangeWarning.propTypes = {
  element: PropTypes.oneOfType([HandbookShape, SectionShape, ChapterShape]).isRequired,
  mergeLink: PropTypes.string.isRequired,
  onDelete: PropTypes.func,
  deleteText: PropTypes.shape({
    buttonText: PropTypes.string,
    title: PropTypes.string,
    text: PropTypes.string
  })
};

PendingChangeWarning.defaultProps = {
  onDelete: undefined,
  deleteText: {}
};

export default PendingChangeWarning;
