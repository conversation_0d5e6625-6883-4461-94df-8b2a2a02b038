import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Tree } from 'kf-bui';
import ChapterNode from './ChapterNode';
import { HandbookShape, ChapterShape } from '../../../shapes';
import { selectRootChapters } from '../../EditorPage/selectors';

const HandbookNode = ({ handbook, chapters, location }) => {
  const items = chapters.map(child => (
    <ChapterNode key={child.id} chapter={child} location={location} />
  ));

  return (
    <Tree.ItemLink
      exact
      to={`/editor/${handbook.id}/`}
      id={handbook.id}
      items={items}
      key={handbook.id}
    >
      <Icon icon="book" size="small" /> {handbook.title}
    </Tree.ItemLink>
  );
};

HandbookNode.propTypes = {
  handbook: HandbookShape.isRequired,
  chapters: PropTypes.arrayOf(ChapterShape).isRequired,
  location: PropTypes.object.isRequired
};

const makeMapStateToProps = (_, ownProps) => {
  const selectRootChaptersSelector = selectRootChapters(ownProps.handbook.id);

  const mapStateToProps = state => ({
    chapters: selectRootChaptersSelector(state)
  });

  return mapStateToProps;
};

export default connect(makeMapStateToProps)(HandbookNode);
