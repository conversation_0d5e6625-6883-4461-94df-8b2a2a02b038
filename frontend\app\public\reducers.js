/**
 * Combine all reducers in this file and export the combined reducers.
 * If we were to do this in store.js, reducers wouldn't be hot reloadable.
 */

import { combineReducers } from 'redux';
import { reducer as toasterReducer } from 'kf-toaster';
import global from './containers/App/reducer';
import handbook from './containers/HandbookPage/reducer';
import search from './containers/SearchPage/reducer';

/**
 * Creates the main reducer with the asynchronously loaded ones
 */
export default function createReducer(asyncReducers) {
  return combineReducers({
    toaster: toasterReducer,
    global,
    handbook,
    search,
    ...asyncReducers
  });
}
