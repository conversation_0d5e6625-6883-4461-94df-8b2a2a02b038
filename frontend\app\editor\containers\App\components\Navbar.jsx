import React from 'react';
import PropTypes from 'prop-types';
import { Link, NavLink } from 'react-router-dom';

import { Icon, Nav, Container, Tag, SrOnly } from 'kf-bui';
import { SessionShape } from '../../../shapes';

import injectT from '../../../../common/i18n';

class Navbar extends React.Component {
  state = {
    isOpen: false
  };

  toggleOpen = () => this.setState(state => ({ isOpen: !state.isOpen }));

  render() {
    const { session, pendingCount, t, hasCentralAccess } = this.props;
    const { isOpen } = this.state;

    return (
      <Nav shadow>
        <Container>
          <Nav.Brand onClick={this.toggleOpen}>
            <Nav.Item as={Link} to="/editor">
              {t('brand')}
            </Nav.Item>
            <Nav.Burger active={isOpen} onClick={this.toggleOpen} color="white" />
          </Nav.Brand>
          <Nav.Menu active={isOpen}>
            <Nav.Left style={{ justifyContent: 'center' }}>
              <Nav.Item hiddenMobile to="/editor" as={NavLink} activeClassName="is-active">
                {t('editor')}
              </Nav.Item>

              {session.isKfAdmin && (
                <Nav.Item hiddenMobile to="/central" as={NavLink} activeClassName="is-active">
                  {t('central')}
                </Nav.Item>
              )}
              {session.isKfAdmin && (
                <Nav.Item
                  hiddenMobile
                  to="/central-editor"
                  as={NavLink}
                  activeClassName="is-active"
                >
                  {t('central-editor')}
                </Nav.Item>
              )}
            </Nav.Left>

            <Nav.Right>
              <Nav.Item hiddenTablet to="/editor" as={NavLink} activeClassName="is-active">
                {t('editor')}
              </Nav.Item>

              {session.isKfAdmin && (
                <Nav.Item hiddenTablet to="/central" as={NavLink} activeClassName="is-active">
                  {t('central')}
                </Nav.Item>
              )}

              {session.isKfAdmin && (
                <Nav.Item
                  hiddenTablet
                  to="/central-editor"
                  as={NavLink}
                  activeClassName="is-active"
                >
                  {t('central-editor')}
                </Nav.Item>
              )}

              <Nav.Item
                as={NavLink}
                to="/search"
                activeClassName="is-active"
                title="Søk"
                aria-label="Søk"
              >
                <Icon icon="search" />
              </Nav.Item>

              {(pendingCount > 0 || hasCentralAccess) && (
                <Nav.Item
                  to="/pending"
                  title={t('pending')}
                  as={NavLink}
                  activeClassName="is-active"
                >
                  <Icon icon="inbox" fw />{' '}
                  {pendingCount > 0 && (
                    <Tag color="danger">
                      <SrOnly>Varsler </SrOnly>
                      {pendingCount}
                    </Tag>
                  )}
                </Nav.Item>
              )}

              {session.userOrgsWithAccess.length > -1 && (
                <Nav.Item
                  as={NavLink}
                  to="/select"
                  title={t('settings')}
                  aria-label={t('settings')}
                  activeClassName="is-active"
                >
                  <Icon icon="cog" />
                </Nav.Item>
              )}
              {session.ssoLogOutUrl && (
                <Nav.Item
                  title="Logg ut"
                  aria-label="Logg ut"
                  href={session.ssoLogOutUrl}
                  rel="noopener noreferrer"
                >
                  <Icon icon="sign-out" />
                  <span>Logg ut </span>
                </Nav.Item>
              )}
            </Nav.Right>
          </Nav.Menu>
        </Container>
      </Nav>
    );
  }
}

Navbar.propTypes = {
  session: SessionShape.isRequired,
  pendingCount: PropTypes.number.isRequired,
  hasCentralAccess: PropTypes.bool.isRequired,
  t: PropTypes.func.isRequired
};

export default injectT('editor.components.Header')(Navbar);
