import immutable from 'object-path-immutable';
import { isString, isBoolean } from 'lodash';

/* A small collection of form utilty helpers */

/**
 * Group all the form stuff together under one key in the component state,
 * and this little baby will help you update it!
 *
 * If key is not defined, it is assumed this operates diretly on the state
 */
export function updateFormState(event, key) {
  const { name, type, checked } = event.target;
  const value = type === 'checkbox' ? checked : event.target.value;

  if (!key) {
    return { [name]: value };
  }

  return function update(state) {
    return {
      [key]: immutable.set(state[key], name, value)
    };
  };
}

export function updateFormValue(name, value, key) {
  return function update(state) {
    return {
      [key]: immutable.set(state[key], name, value)
    };
  };
}

export function merge(master, copy) {
  return Object.entries(master).reduce((acc, [key, value]) => {
    if (isBoolean(value)) {
      acc[key] = copy[key] != null ? copy[key] : value;
    } else {
      acc[key] = copy[key] || value;
    }
    return acc;
  }, {});
}

/**
 * Trims the string in the form. Empty strings and other null values won't be included in the result
 */
export function trim(form) {
  return Object.entries(form).reduce((acc, [key, value]) => {
    if (isString(value)) {
      const trimmed = value.trim();
      if (trimmed.length > 0) {
        acc[key] = trimmed;
      }
    } else if (value != null) {
      acc[key] = value;
    }
    return acc;
  }, {});
}

// Convert bytes to human readable format
export function formatFileSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex += 1;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// Convert bytes to KB
export function bytesToKB(bytes) {
  return bytes / 1024;
}

// Convert KB to human readable format
export function formatKilobytes(kilobytes) {
  if (kilobytes >= 1024) {
    const megabytes = kilobytes / 1024;
    return `${megabytes.toFixed(2)} MB`;
  }
  return `${kilobytes.toFixed(2)} KB`;
}
