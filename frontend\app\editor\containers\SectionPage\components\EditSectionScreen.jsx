import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import queryString from 'query-string';
import {
  Button,
  Columns,
  Column,
  Help,
  Icon,
  Field,
  Label,
  Input,
  Radio,
  Delete,
  Title,
  Subtitle
} from 'kf-bui';

import injectT from '../../../../common/i18n';
import CentralTreeModal from '../../CentralTree';
import BooleanToggler from '../../../components/BooleanToggler';
import Wysiwyg from '../../../components/Wysiwyg';
import { saveLocalSection, fetchSection as fetchLocalSection } from '../../EditorPage/actions';
import { fetchSection as fetchCentralSection } from '../../CentralTree/api';
import { selectSectionEntities } from '../../EditorPage/selectors';
import { selectHandbooks as selectCentralHandbooks } from '../../CentralTree/selectors';
import { HandbookShape, SectionShape } from '../../../../common/shapes';
import { selectSpellcheckUrl } from '../../App/selectors';
import PageLeaveConfirmationModal from '../../CentralHandbookEditorPage/PageLeaveConfirmationModal';

class EditSectionScreen extends React.Component {
  state = {
    radio: 'central',
    centralSection: null,
    isSaving: false,
    title: this.props.section ? this.props.section.title : '',
    isDirty: false,
    isIntendedNavigation: false,
    showPopup: false,
    nextLocation: null
  };

  componentDidMount() {
    const { section, fetchLocalSectionFun } = this.props;
    if (section) {
      // Ensure we have the text of the section we are editing
      fetchLocalSectionFun(section.id);
    }

    this.unblock = this.props.history.block(location => {
      if (this.state.isDirty && !this.state.isIntendedNavigation) {
        this.setState({ showPopup: true, nextLocation: location });
        return false;
      }
      return true;
    });
  }

  componentWillReceiveProps(nextProps) {
    const { section } = this.props;
    if (section !== nextProps.section) {
      this.setState({ title: nextProps.section.title });
    }
  }

  componentWillUnmount() {
    if (this.unblock) {
      this.unblock();
    }
  }

  onEditorContentChange = hasChanged => {
    this.setState({ isDirty: hasChanged });
  };

  handleCancel = () => {
    this.setState({ isIntendedNavigation: true });
  };

  handlePageLeavePopupConfirm = () => {
    this.setState({ showPopup: false }, () => {
      this.unblock();
      this.props.history.push(this.state.nextLocation.pathname);
    });
  };

  handlePageLeavePopupCancel = () => {
    this.setState({ showPopup: false });
  };

  onTitleChange = e => {
    const { value } = e.target;
    this.setState(prevState => ({
      title: value,
      isDirty: prevState.isDirty || value !== (this.props.section ? this.props.section.title : '')
    }));
  };

  onRadioChange = e => {
    const { value } = e.target;
    if (value !== this.state.radio) {
      this.setState({ radio: value });
      if (value === 'central') {
        // Reset the text back to the central text if we back to the central text
        this.wysiwyg.setValue(this.state.centralSection.text);
      }
    }
  };

  onSubmit = event => {
    event.preventDefault();
    this.setState({ isSaving: true, isIntendedNavigation: true });
    const { section } = this.props;
    if (section) {
      // If we have initialValues, we are doing an update
      this.handleUpdateSection();
    } else {
      // Otherwise, we are creating a new section
      this.handleNewSection();
    }
  };

  handleUpdateSection = () => {
    const { section, saveLocalSectionFun } = this.props;
    const { title } = this.state;
    return this.wysiwyg.uploadImagesAndGetContent().then(content => {
      // skipping image resizing as it's a new requirement
      // const newContent = this.addSizeToImageTags(content);
      saveLocalSectionFun({
        section: {
          ...section,
          title: title.trim(),
          text: content,
          // Always set localChange to true when editing
          // something that was based on a central section
          localChange: Boolean(section.importedHandbookId)
        }
      });
    });
  };

  addSizeToImageTags = content => {
    const regex = /<img.*?>/g;
    const imageTags = [];
    while (true) {
      const result = regex.exec(content);
      if (result == null) break;
      imageTags.push(result[0]);
    }

    const imageTagsWithoutSize = imageTags.filter(
      imageTag => imageTag.includes('width="') === false || imageTag.includes('height="') === false
    );

    let newContent = content;

    imageTagsWithoutSize.forEach(imageTag => {
      if (!imageTag) return;
      const index = imageTags.findIndex(tag => tag === imageTag);
      if (index < 0) return;
      const [width, height] = [69, 69]; // make function mby?
      imageTags[index] = imageTag
        .replace(' />', ` width="${width}" />`)
        .replace(' />', ` height="${height}" />`);
      newContent = newContent.replace(imageTag, imageTags[index]);
    });
    return newContent;
  };

  handleNewSection = () => {
    const { location, saveLocalSectionFun } = this.props;
    const { centralSection, title, radio } = this.state;

    const section = {
      title: title.trim(),
      handbookId: this.context.handbook.id,
      parentId: queryString.parse(location.search).parent,
      importedHandbookSectionId: centralSection ? centralSection.importedHandbookSectionId : null,
      importedHandbookId: centralSection ? centralSection.importedHandbookId : null,
      localChange: centralSection && (centralSection.title !== title.trim() || radio === 'local')
    };

    // If we have opted in to using the central text, use the central text directly...
    // this means... DO NOT COPY IT FROM THE WYSIWYG
    if (centralSection && radio === 'central') {
      return saveLocalSectionFun({
        section: {
          ...section,
          text: centralSection.text
        }
      });
    }

    // If not using central text, we get the text from the wysiwyg this way
    return this.wysiwyg.uploadImagesAndGetContent().then(content => {
      // skipping image resizing as it's a new requirement
      // const newContent = this.addSizeToImageTags(content);
      saveLocalSectionFun({ section: { ...section, text: content } });
    });
  };

  selectCentralSection = section => {
    this.setState({ centralSection: section });
    if (section) {
      fetchCentralSection(section.handbookId, section.id).then(sectionWithText => {
        this.setState({ centralSection: sectionWithText });
        this.wysiwyg.setValue(sectionWithText.text);
      });
      this.setState({ title: section.title });
    }
  };

  isValid() {
    const { title } = this.state;
    return title.trim() !== '';
  }

  render() {
    const invalid = !this.isValid();
    const { t, section, centralHandbooks, location } = this.props;
    const { title, centralSection, radio, isSaving, showPopup } = this.state;
    const { handbook } = this.context;

    // The abort button either takes us to the section or the parent chapter
    let abortLink;
    // If we are editing a chapter, take us there on abort
    if (section) {
      abortLink = `/editor/${section.handbookId}/section/${section.id}/`;
      // If we are adding a new child chapter, take us to the parent on abort
    } else {
      abortLink = `/editor/${handbook.id}/chapter/${queryString.parse(location.search).parent}`;
    }

    return (
      <div>
        <Title>{section ? 'Rediger avsnitt' : 'Opprett nytt avsnitt'}</Title>
        {section && <Subtitle>{section.title}</Subtitle>}
        <hr />
        {!section && centralHandbooks.length > 0 && (
          <SelectCentralSection
            selectSection={this.selectCentralSection}
            section={centralSection}
            t={t}
          />
        )}
        <form onSubmit={this.onSubmit}>
          <Field>
            <Label htmlFor="title">Tittel</Label>
            <Input
              id="title"
              name="title"
              placeholder="Tittel"
              readOnly={isSaving}
              value={title}
              onChange={this.onTitleChange}
              required
            />
          </Field>
          {centralSection && (
            <Field>
              <Radio
                name="radio"
                value="central"
                checked={radio === 'central'}
                onChange={this.onRadioChange}
              >
                {' '}
                {t('centralRadio')}
              </Radio>
              <Radio
                name="radio"
                value="local"
                checked={radio === 'local'}
                onChange={this.onRadioChange}
              >
                {' '}
                {t('localRadio')}
              </Radio>
            </Field>
          )}
          <Field>
            <Label>{t('textLabel')}</Label>
            <Wysiwyg
              disabled={centralSection && radio === 'central'}
              ref={c => {
                this.wysiwyg = c;
              }}
              value={section ? section.text : null}
              spellCheckerRpcUrl={this.props.spellCheckUrl}
              onChange={this.onChange}
              onEditorContentChange={this.onEditorContentChange}
            />
            {centralSection && (
              <Help>
                {radio === 'central' && centralSection.title === title ? (
                  t('autoSync')
                ) : (
                  <span>
                    <Icon icon="warning" size="small" /> {t('manualSync')}
                  </span>
                )}
              </Help>
            )}
          </Field>

          <Columns responsive="mobile">
            <Column>
              <Button as={Link} to={abortLink} onClick={this.handleCancel}>
                {t('cancelButton')}
              </Button>
            </Column>
            <Column narrow>
              <Button loading={isSaving} disabled={invalid} type="submit" color="primary">
                {t('saveButton')}
              </Button>
            </Column>
          </Columns>
        </form>

        {showPopup && (
          <PageLeaveConfirmationModal
            onConfirm={this.handlePageLeavePopupConfirm}
            onCancel={this.handlePageLeavePopupCancel}
            message={t('leaveEditorConfirmationMessage')}
            title={t('leaveEditorConfirmationModalTitle')}
            t={t}
          />
        )}
      </div>
    );
  }
}

EditSectionScreen.propTypes = {
  centralHandbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  fetchLocalSectionFun: PropTypes.func.isRequired,
  section: SectionShape,
  saveLocalSectionFun: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  spellCheckUrl: PropTypes.string,
  location: PropTypes.shape({
    search: PropTypes.string
  }).isRequired,
  history: PropTypes.object.isRequired
};

EditSectionScreen.defaultProps = {
  section: null,
  spellCheckUrl: ''
};

EditSectionScreen.contextTypes = {
  handbook: HandbookShape.isRequired
};

const mapStateToProps = (state, ownProps) => ({
  centralHandbooks: selectCentralHandbooks(state),
  spellCheckUrl: selectSpellcheckUrl(state),
  section: ownProps.match.params
    ? selectSectionEntities(state)[ownProps.match.params.sectionId]
    : null
});

export default compose(
  withRouter,
  connect(mapStateToProps, {
    saveLocalSectionFun: saveLocalSection,
    fetchLocalSectionFun: fetchLocalSection
  }),
  injectT('editor.containers.EditSection')
)(EditSectionScreen);

const SelectCentralSection = ({ section, selectSection, t }) => (
  <BooleanToggler>
    {(toggle, value) => (
      <Field>
        <Label>{t('centralTitle')}</Label>
        <Columns responsive="mobile" vCentered>
          <Column narrow>
            <Button key="button" onClick={toggle}>
              {section ? section.title : t('centralButton')}
            </Button>
          </Column>
          {section && (
            <Column>
              <Delete
                onClick={() => selectSection(null)}
                title={t('removeCentralSelection')}
                aria-label={t('removeCentralSelection')}
                size="medium"
              />
            </Column>
          )}
        </Columns>
        {value ? (
          <CentralTreeModal
            title={t('centralTitle')}
            onSectionClick={selectSection}
            key="modal"
            onHide={toggle}
          />
        ) : null}
      </Field>
    )}
  </BooleanToggler>
);

SelectCentralSection.propTypes = {
  selectSection: PropTypes.func.isRequired,
  section: SectionShape,
  t: PropTypes.func.isRequired
};

SelectCentralSection.defaultProps = {
  section: null
};
