/* eslint-env jest */
import mergeTitle from '../elementMerger';

describe('mergeTitle', () => {
  it("should set the title, remove pendingChanges and don't flag localChange if the title isn't different from the central element's", () => {
    const element = {
      id: 'id',
      importedHandbookId: 'importedId',
      title: 'old title',
      pendingChange: true,
      localChange: false
    };

    const centralElement = {
      title: 'new title'
    };

    const expected = {
      ...element,
      title: centralElement.title,
      pendingChange: false,
      localChange: false
    };

    const actual = mergeTitle(element, centralElement, centralElement.title);
    expect(actual.title).toEqual(expected.title);
    expect(actual.pendingChange).toEqual(expected.pendingChange);
    expect(actual.localChange).toEqual(expected.localChange);
    expect(actual.pendingChangesUpdatedDate).toBeDefined();
  });

  it('should set the title, remove pendingChanges and set localChange to true if titles are different', () => {
    const element = {
      id: 'id',
      importedHandbookId: 'importedId',
      title: 'old title',
      pendingChange: true,
      localChange: false
    };

    const centralElement = {
      title: 'new title'
    };

    const expected = {
      ...element,
      title: 'whole other new title',
      pendingChange: false,
      localChange: true
    };

    const actual = mergeTitle(element, centralElement, 'whole other new title');
    expect(actual.title).toEqual(expected.title);
    expect(actual.pendingChange).toEqual(expected.pendingChange);
    expect(actual.localChange).toEqual(expected.localChange);
    expect(actual.pendingChangesUpdatedDate).toBeDefined();
  });
});
