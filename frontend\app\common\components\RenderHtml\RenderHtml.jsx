/* eslint-disable react/no-danger, react/prefer-stateless-function */
import React from 'react';
import PropTypes from 'prop-types';
import { Content } from 'kf-bui';
import styled from 'styled-components';

const StyledContent = styled(Content)`
  tbody tr:not(:first):not(:last) {
    border-top: 1px solid #dbdbdb !important;
  }
  tbody tr td {
    border-left: 1px solid black;
  }
  tbody tr {
    border: 1px solid black;
  }
  word-break: break-word;
`;

function createMarkup(html) {
  return { __html: html };
}

const RenderHtml = ({ html }) => (
  <StyledContent style={{ overflow: 'auto' }} dangerouslySetInnerHTML={createMarkup(html)} />
);

RenderHtml.propTypes = {
  html: PropTypes.string
};

RenderHtml.defaultProps = {
  html: null
};

export default RenderHtml;
