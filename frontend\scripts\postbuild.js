const fs = require('fs');
const path = require('path');

// Create directories for editor and public apps
const buildDir = path.join(__dirname, '..', 'build');
const editorDir = path.join(buildDir, 'editor');
const publicDir = path.join(buildDir, 'public');

// Ensure directories exist
if (!fs.existsSync(editorDir)) {
  fs.mkdirSync(editorDir, { recursive: true });
}

if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Copy index.html to both locations
const indexHtmlPath = path.join(buildDir, 'index.html');
const editorIndexPath = path.join(editorDir, 'index.html');
const publicIndexPath = path.join(publicDir, 'index.html');

if (fs.existsSync(indexHtmlPath)) {
  fs.copyFileSync(indexHtmlPath, editorIndexPath);
  fs.copyFileSync(indexHtmlPath, publicIndexPath);
  console.log('✓ Copied index.html to /editor/ and /public/ directories');
} else {
  console.error('✗ index.html not found in build directory');
  process.exit(1);
}
