import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Link, Redirect } from 'react-router-dom';

import { Button, Column, Control, Icon, Columns, Menu, Title, Group, Heading } from 'kf-bui';

import { FormattedMessage } from 'react-intl';
import injectT from '../../../../common/i18n';
import Metadata from '../../../components/Metadata';
import { selectPublicBasename, selectSession } from '../../App/selectors';

import {
  selectSubscriptions,
  selectRootChapters,
  selectHandbookEntities,
  selectLocalEditorsForOrg,
  selectLocalEditors
} from '../../EditorPage/selectors';
import {
  deleteLocalHandbook,
  subscribe,
  sortItems,
  fetchHandbook,
  saveLocalEditor,
  deleteLocalEditor,
  fetchLocalEditors
} from '../../EditorPage/actions';
import { ChapterShape, HandbookShape } from '../../../shapes';
import DeleteButton from '../../../components/DeleteButton';
import EditHandbookButton from './EditHandbookButton';
import CommentModal from './CommentModal';
import ExportModal from './ExportModal';
import SortChildrenScreen from '../../../components/SortChildrenScreen';
import PendingChangeWarning from '../../../components/PendingChangeWarning';
import LocalEditorsModal from './LocalEditorsModal';

function publicUrl(publicBasename, handbook) {
  let base = publicBasename.endsWith('/') ? publicBasename : `${publicBasename}/`;

  if (process.env.NODE_ENV === 'development') {
    // If we are using the dev server, we can't use the webapppath defined in .properties
    base = '/public/';
  }

  return `${base}${handbook.externalOrgId}/${handbook.id}`;
}

class HandbookScreen extends React.Component {
  state = {
    isSorting: false,
    showComments: false,
    showExportModal: false,
    showLocalEditorsModal: false
  };

  componentDidMount() {
    const { match, fetchHandbookFun } = this.props;
    fetchHandbookFun(match.params.handbookId);
  }

  componentWillReceiveProps(nextProps) {
    const { match, fetchHandbookFun } = this.props;
    if (match.params.handbookId !== nextProps.match.params.handbookId) {
      fetchHandbookFun(nextProps.match.params.handbookId);
    }
  }

  toggleSubscribe = e => {
    const { subscribeFun, match } = this.props;
    subscribeFun(match.params.handbookId, e.target.checked);
  };

  toggleSort = () => this.setState(state => ({ isSorting: !state.isSorting }));

  renderChildren() {
    const { chapters, sortItemsFun } = this.props;
    const { isSorting } = this.state;

    if (isSorting) {
      return (
        <SortChildrenScreen
          items={chapters}
          onCancel={this.toggleSort}
          sortFunction={sortItemsFun}
        />
      );
    }

    return (
      <Menu>
        <Menu.List>
          {chapters.map(chapter => (
            <Menu.Item
              key={chapter.id}
              as={Link}
              to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/`}
            >
              <Icon icon="bookmark-o" size="small" />
              {chapter.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    );
  }

  render() {
    const {
      chapters,
      handbooks,
      subscriptions,
      localEditorsForOrg,
      localEditors,
      publicBasename,
      session,
      match,
      t,
      deleteHandbookFun,
      saveLocalEditorFun,
      deleteLocalEditorFun,
      fetchLocalEditorsFun
    } = this.props;
    const { isSorting, showComments, showExportModal, showLocalEditorsModal } = this.state;
    const handbook = handbooks[match.params.handbookId];

    if (!handbook) {
      // If we have handbooks, but cannot find the one we are looking for, redirect to 404 page
      return Object.values(handbooks).length > 0 ? <Redirect to="/404" /> : null;
    }

    const metadataAppend = (
      <label htmlFor="subscribe">
        <Heading htmlFor="subscribe">{t('subscribeCheck')}</Heading>
        <input
          id="subscribe"
          type="checkbox"
          onChange={this.toggleSubscribe}
          checked={subscriptions.has(handbook.id)}
          title={t('subscribeCheckMouseOver')}
        />
      </label>
    );

    return (
      <div>
        <Columns>
          <Column>
            <Title>
              <Icon icon="book" size="small" style={{ marginRight: '1rem' }} />
              <span>{handbook.title}</span>
              <Heading style={{ fontSize: '14px' }}>
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href={publicUrl(publicBasename, handbook)}
                  title={t('publishedLink')}
                >
                  <FormattedMessage id="editor.containers.HandbookPage.components.HandbookScreen.readLink" />
                  <Icon icon="external-link" />
                </a>
              </Heading>
            </Title>
          </Column>
        </Columns>
        <Columns multiline>
          <Column narrow>
            <EditHandbookButton handbook={handbook} t={t} />{' '}
          </Column>
          <Column narrow>
            <Button
              control
              onClick={() => {
                fetchLocalEditorsFun(match.params.handbookId);
                this.setState({ showLocalEditorsModal: true });
              }}
              size="small"
            >
              <Icon icon="user-plus" size="small" />
              <span>Gi tilgang</span>
            </Button>
            {showLocalEditorsModal && (
              <LocalEditorsModal
                handbookId={handbook.id}
                localEditorsForOrg={localEditorsForOrg}
                localEditors={localEditors}
                saveLocalEditorFun={saveLocalEditorFun}
                deleteLocalEditorFun={deleteLocalEditorFun}
                onHide={() => this.setState({ showLocalEditorsModal: false })}
                userEmail={session.user.email}
              />
            )}
          </Column>
          <Column narrow>
            <Button control as={Link} to={`/editor/${handbook.id}/chapter/add-new`} size="small">
              <Icon icon="plus" size="small" />
              <span>{t('newChapter')}</span>
            </Button>
          </Column>
          <Column narrow>
            <Button control as={Link} to={`/editor/${handbook.id}/links`} size="small">
              <Icon icon="link" size="small" />
              <span>Opprett lenkesamling</span>
            </Button>
          </Column>
          <Column narrow>
            <Button control onClick={() => this.setState({ showComments: true })} size="small">
              <Icon icon="comment-o" size="small" />
              <span>Interne kommentarer</span>
            </Button>
            {showComments && (
              <CommentModal
                handbookId={handbook.id}
                userEmail={session.user.email}
                toggleHide={() => this.setState(this.setState({ showComments: false }))}
              />
            )}
          </Column>
          <Column narrow>
            <Button control size="small" onClick={() => this.setState({ showExportModal: true })}>
              <span>Eksporter</span>
            </Button>
            {showExportModal && (
              <ExportModal
                handbook={handbook}
                onHide={() => this.setState({ showExportModal: false })}
              />
            )}
          </Column>
          <Column narrow>
            <Control>
              <PendingChangeWarning
                element={handbook}
                mergeLink={`/merge/handbook/${handbook.id}/`}
              />
            </Control>
          </Column>
          <Column narrow>
            <Group>
              <Button
                control
                active={isSorting}
                onClick={this.toggleSort}
                disabled={chapters.length <= 1}
                title="Sorter kapitlene til Håndboken"
                size="small"
              >
                {t('sortButton')}
              </Button>
              <DeleteButton
                toDelete={handbook}
                // eslint-disable-next-line react/destructuring-assignment
                onDelete={() => deleteHandbookFun(handbook)}
                t={t}
              />
            </Group>
          </Column>
        </Columns>
        <Columns>
          <Column>
            <hr />
            <Metadata prepend={undefined} element={handbook} append={metadataAppend} />
            <hr />
          </Column>
        </Columns>
        {this.renderChildren()}
      </div>
    );
  }
}

HandbookScreen.propTypes = {
  handbooks: PropTypes.objectOf(HandbookShape).isRequired,
  chapters: PropTypes.arrayOf(ChapterShape).isRequired,
  subscriptions: PropTypes.instanceOf(Set).isRequired,

  localEditorsForOrg: PropTypes.instanceOf(Set).isRequired,
  localEditors: PropTypes.instanceOf(Set).isRequired,

  subscribeFun: PropTypes.func.isRequired,
  deleteHandbookFun: PropTypes.func.isRequired,
  sortItemsFun: PropTypes.func.isRequired,
  fetchHandbookFun: PropTypes.func.isRequired,
  fetchLocalEditorsFun: PropTypes.func.isRequired,
  deleteLocalEditorFun: PropTypes.func.isRequired,
  saveLocalEditorFun: PropTypes.func.isRequired,

  t: PropTypes.func.isRequired,

  publicBasename: PropTypes.string.isRequired,
  session: PropTypes.shape({ user: PropTypes.shape({ email: PropTypes.string }) }).isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      handbookId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired
};

const mapStateToProps = (state, ownProps) => {
  const selectChaptersSelector = selectRootChapters(ownProps.match.params.handbookId);
  return {
    handbooks: selectHandbookEntities(state),
    chapters: selectChaptersSelector(state),
    subscriptions: selectSubscriptions(state),
    localEditorsForOrg: selectLocalEditorsForOrg(state),
    localEditors: selectLocalEditors(state),
    publicBasename: selectPublicBasename(state),
    session: selectSession(state)
  };
};

const mapDispatchToProps = {
  subscribeFun: subscribe,
  deleteHandbookFun: deleteLocalHandbook,
  sortItemsFun: sortItems,
  fetchHandbookFun: fetchHandbook,
  fetchLocalEditorsFun: fetchLocalEditors,
  saveLocalEditorFun: saveLocalEditor,
  deleteLocalEditorFun: deleteLocalEditor
};

export default compose(
  injectT('editor.containers.HandbookSelection'),
  connect(mapStateToProps, mapDispatchToProps)
)(HandbookScreen);
