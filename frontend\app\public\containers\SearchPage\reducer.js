import { handleActions } from 'redux-actions';
import * as actions from './actions';

const intialState = {
  result: null,
  isSearching: false,
  query: '',
  page: 1
};

export default handleActions(
  {
    [actions.searchQueryChange]: (state, action) => ({
      ...intialState,
      query: action.payload
    }),
    [actions.search]: state => ({
      ...state,
      isSearching: true
    }),
    [actions.searchSuccess]: (state, action) => ({
      ...state,
      isSearching: true,
      result: action.payload
    })
  },
  intialState
);
