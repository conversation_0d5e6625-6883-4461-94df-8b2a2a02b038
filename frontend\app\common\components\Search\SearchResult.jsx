import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

import { Heading, Subtitle, Columns, Column, Title, Media, Icon } from 'kf-bui';
import { SearchResultShape, HitShape } from '../../shapes';
import RenderHtml from '../RenderHtml';

// eslint-disable-next-line react/prefer-stateless-function
export default class SearchResult extends React.PureComponent {
  render() {
    const { result, query, linkFunc } = this.props;

    if (result.results.length === 0) {
      return <EmptySearchResult query={query} />;
    }

    return (
      <div>
        <Columns>
          <Column>
            <Media>
              {/* Add empty icon for alignment */}
              <Media.Left aria-hidden>
                <Icon />
              </Media.Left>
              <Media.Content>
                <Title as="p" size="6">
                  Side {result.page} av {Math.ceil(result.totalHits / result.pageSize)}. Viser{' '}
                  {result.totalHits} resultater.
                </Title>
              </Media.Content>
            </Media>
          </Column>
        </Columns>
        <Columns>
          <Column>
            {result.results.map(hit => (
              <SearchHit key={hit.id} hit={hit} linkFunc={linkFunc} />
            ))}
          </Column>
        </Columns>
      </div>
    );
  }
}

SearchResult.propTypes = {
  query: PropTypes.string.isRequired,
  result: SearchResultShape.isRequired,
  // A function that knows how to create a RR4 link for each hit
  // The links are different in the public page and editor page
  linkFunc: PropTypes.func.isRequired
};

function getIcon(hit) {
  if (hit.isChapter) {
    return 'bookmark-o';
  }
  if (hit.isSection) {
    return 'file-text-o';
  }
  return 'book';
}

const SearchHit = ({ hit, linkFunc }) => (
  <Media>
    <Media.Left>
      <Icon icon={getIcon(hit)} size="small" />
    </Media.Left>
    <Media.Content>
      <Subtitle as="p">
        <Link to={linkFunc(hit)}>{hit.title}</Link>
      </Subtitle>
      {hit.textHighlight && <RenderHtml html={hit.textHighlight} />}
    </Media.Content>
  </Media>
);

SearchHit.propTypes = {
  hit: HitShape.isRequired,
  linkFunc: PropTypes.func.isRequired
};

const EmptySearchResult = ({ query }) => (
  <div>
    <Title as="p" textCentered size="4">
      Ingen treff funnet for «<em>{query}</em>»
    </Title>
    <Heading textCentered as="div">
      <Columns>
        <Column>Tips</Column>
      </Columns>
      <p>Forsøk å bruke mer generelle søketermer.</p>
      <p>Hvis du filtrerer på håndbok, forsøk å fjerne eller justere filteret.</p>
    </Heading>
  </div>
);

EmptySearchResult.propTypes = {
  query: PropTypes.string.isRequired
};
