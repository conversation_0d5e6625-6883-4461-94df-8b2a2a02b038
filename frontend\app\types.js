// @flow
export type UserType = {|
  +email: string
|};

export type OrganizationType = {|
  +id: string,
  +name: string
|};

export type SessionType = {|
  +appVersion: string,
  +isKfAdmin: boolean,
  +user: UserType,
  +userOrgsWithAccess: OrganizationType[],
  +organization: ?OrganizationType,
  +ssoLogOutUrl: ?string,
  +spellCheckUrl: ?string
|};

export type SectionType = {|
  +id: string,
  +handbookId: string,
  +parentId: string,
  +text: ?string,
  +title: string,
  +updatedBy: string,
  +updatedDate: Date,
  +textUpdatedBy: string,
  +textUpdatedDate: Date,
  +importedHandbookId: ?string,
  +importedHandbookSectionId: ?string,
  +localTitleChange: ?boolean,
  +localTextChange: ?boolean,
  +pendingTitleChange: ?boolean,
  +pendingTextChange: ?boolean,
  +type: 'LOCAL_SECTION',
  +sortOrder?: number
|};

export type ChapterType = {|
  +id: string,
  +handbookId: string,
  +parentId: ?string,
  +title: string,
  +importedHandbookId: ?string,
  +importedHandbookChapterId: ?string,
  +localChange: ?boolean,
  +pendingChange: ?boolean,
  +type: 'LOCAL_CHAPTER',
  +sortOrder?: number
|};

export type HandbookType = {|
  +id: string,
  +title: string,
  +importedHandbookId: ?string,
  +localChange: ?boolean,
  +pendingChange: ?boolean
|};

export type CentralHandbookType = {|
  +id: string,
  +title: string,
  +pendingChange: boolean,
  +createdDate: string,
  +updatedDate: string,
  +type: 'HANDBOOK',
  +createdBy: string,
  +updatedBy: string
|};

export type CentralChapterType = {|
  +id: string,
  +title: string,
  +parentId: ?string,
  +centralHandbookId: string,
  +sortOrder: number,
  +createdDate: string,
  +updatedDate: string,
  +type: 'CHAPTER',
  +createdBy: string,
  +updatedBy: string
|};

export type CentralSectionType = {|
  +id: string,
  +title: string,
  +parentId: string,
  +centralHandbookId: string,
  +text: ?string,
  +html: ?string,
  +sortOrder: number,
  +createdDate: string,
  +updatedDate: string,
  +type: 'SECTION',
  +createdBy: string,
  +updatedBy: string
|};

export type ReadingLinkType = {|
  +id?: string,
  +link: string,
  +centralSectionId: string,
  +createdDate?: string,
  +validTo: Date
|};

export type ChildrenType = ChapterType | SectionType;
export type CentralChildrenType = CentralChapterType | CentralSectionType;
export type CentralTreeNodeType = CentralHandbookType | CentralChildrenType;

export type CentralEditorHandbooksType = {|
  +centralHandbooks: CentralHandbookType[],
  +centralChapters: CentralChapterType[],
  +centralSections: CentralSectionType[],
  +readingLinks: ReadingLinkType[]
|};

export type LinkType = {|
  +id: ?string,
  +title: string,
  +url: string,
  +sortOrder: number
|};

export type LinkCollectionType = {|
  +id: ?string,
  +title: string,
  +handbookId: string,
  +sortOrder: number,
  +links: LinkType[]
|};

export type StateType = {
  +form: {},
  +toaster: {
    toasts: string[]
  },
  +global: SessionType,
  +handbooks: HandbookType[],
  +centralAccess: string[],
  +centralHandbooks: {
    +selectedHandbook: ?HandbookType,
    +handbooks: HandbookType[],
    +chapters: ChapterType[],
    +sections: SectionType[]
  },
  centralEditorHandbooks: CentralEditorHandbooksType,
  readingLinks: {| [string]: ReadingLinkType |}
};
