/* eslint-env jest */
import { expectSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import * as api from '../api';
import * as saga from '../saga';

describe('fetchCentralHandbooks', () => {
  it('calls the API and notifies the reducer on success', () =>
    expectSaga(saga.fetchCentralHandbooks)
      .provide([[matchers.call.fn(api.fetchAllCentralHandbooks), ['HMS', 'HMT']]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));

  it('can fail gracefully', () =>
    expectSaga(saga.fetchCentralHandbooks)
      .provide([[matchers.call.fn(api.fetchAllCentralHandbooks), throwError(new Error('oops'))]])
      .run()
      .then(result => expect(result.toJSON()).toMatchSnapshot()));
});
