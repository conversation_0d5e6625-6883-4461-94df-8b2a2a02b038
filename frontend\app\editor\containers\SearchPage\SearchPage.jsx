import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import queryString from 'query-string';
import { <PERSON>ton, Container, Section, Columns, Column, Hero, Menu, ImageHero } from 'kf-bui';
import { toast } from 'kf-toaster';

import { selectHandbooks } from '../EditorPage/selectors';
import SearchField from './components/SearchField';
import { SearchResult, SearchPagination } from '../../../common/components/Search';
import { HandbookShape, SessionShape } from '../../shapes';
import * as api from './api';
import { selectIsKfAdmin, selectBannerUrl, selectSession } from '../App/selectors';

// Generate RR4 to link for the different search results
function createLink(hit) {
  if (hit.isChapter) {
    return `/editor/${hit.handbookId}/chapter/${hit.id}`;
  }
  if (hit.isSection) {
    return `/editor/${hit.handbookId}/section/${hit.id}`;
  }
  return `/editor/${hit.id}/`;
}

class SearchPage extends Component {
  constructor(props) {
    super(props);

    const { location } = this.props;
    // Init state from params
    const params = queryString.parse(location.search);
    this.state = {
      searchQuery: params.q || '',
      handbookId: params.id,
      searchResult: null,
      page: params.p || 1,
      masterResetComplete: false
    };
  }

  componentDidMount() {
    const { searchQuery } = this.state;
    if (searchQuery) {
      this.doSearch();
    }
  }

  componentWillReceiveProps(nextProps) {
    const { location } = this.props;
    if (location !== nextProps.location) {
      // FIXME: eternal loop this.doSearch();
    }
  }

  onPageClick = page => {
    this.setState({ page }, () => this.doSearch());
  };

  handleSubmit = event => {
    event.preventDefault();
    this.doSearch(this.state.searchQuery);
  };

  handleChange = e => this.setState({ searchQuery: e.target.value, searchResult: null, page: 1 });

  handleHandbookClick = (event, handbookId) => {
    event.preventDefault();
    // set undefined here if no id
    // querystring.stringify drops undefined values, but it includes null values
    this.setState({ handbookId: handbookId || undefined }, () => this.doSearch());
  };

  doSearch = () => {
    const { history, match } = this.props;
    const query = this.state.searchQuery.toLowerCase();
    if (!query) {
      return;
    }
    const params = {
      q: query,
      id: this.state.handbookId,
      p: this.state.page
    };
    history.replace({
      pathname: match.path,
      search: queryString.stringify(params)
    });

    api
      .getSearchResult(query, this.state.page, this.state.handbookId)
      .then(res => {
        this.setState({ searchResult: res });
      })
      .catch(() => {
        this.props.dispatch(toast.error('En feil inntraff under søking.'));
      });
  };

  doReset = () => {
    api
      .resetIndexes()
      .then(() => {
        this.props.dispatch(toast.success('Reindekserte håndbøker'));
      })
      .catch(() => {
        this.props.dispatch(toast.error('Kunne ikke reindeksere håndbøker'));
      });
  };

  doMasterReset = async () => {
    this.setState({ masterResetComplete: false });
    console.log(
      'Started resetting indexes for organizations ',
      this.props.session.user.organizations
    );
    const result = await api.resetManyIndexes(this.props.session.user.organizations);
    console.log(
      `Number of organisations complete: ${result[0].length}/${result[0].length + result[1].length}`
    );
    this.setState({ masterResetComplete: true });
  };

  render() {
    const { handbooks, isKfAdmin, bannerUrl } = this.props;
    const { handbookId, searchQuery, searchResult } = this.state;
    const activeHandbookId = handbookId;

    document.title = 'Søk - KF Håndbøker';

    return (
      <div>
        <ImageHero color="primary" imageUrl={bannerUrl}>
          <Hero.Body>
            <Container>
              <SearchField
                onChange={this.handleChange}
                onSubmit={this.handleSubmit}
                query={searchQuery}
              />
            </Container>
          </Hero.Body>
        </ImageHero>
        <Section>
          <Container>
            <Columns>
              <Column size="1/4">
                <Menu>
                  <Menu.Label>Håndbøker</Menu.Label>
                  <Menu.List>
                    <Menu.Item
                      onClick={event => this.handleHandbookClick(event, null)}
                      active={!activeHandbookId}
                      href=""
                      role="button"
                    >
                      Alle
                    </Menu.Item>
                    {handbooks.map(handbook => (
                      <Menu.Item
                        active={activeHandbookId === handbook.id}
                        key={handbook.id}
                        onClick={event => this.handleHandbookClick(event, handbook.id)}
                        role="button"
                        href=""
                      >
                        {handbook.title}
                      </Menu.Item>
                    ))}
                  </Menu.List>
                </Menu>
                {isKfAdmin && (
                  <div style={{ marginTop: '1rem' }}>
                    <Menu.Label>Administrator</Menu.Label>
                    <Button outlined fullWidth onClick={this.doReset}>
                      Reindekser håndbøker
                    </Button>
                  </div>
                )}
                {isKfAdmin && (
                  <div style={{ marginTop: '1rem' }}>
                    <Button outlined onClick={this.doMasterReset}>
                      Reindekser håndbøker for all organisasjoner
                    </Button>
                    <i>
                      NB! Bare organisasjoner denne brukeren har tilgang til vil bli reindeksert, og
                      dette vil kunne ta lang tid
                    </i>
                    {this.state.masterResetComplete && (
                      <div className="masterResetComplete">
                        Reindekseringen er ferdig. Se i konsoll for status
                      </div>
                    )}
                  </div>
                )}
              </Column>
              <Column>
                <Columns>
                  <Column>
                    {searchResult && (
                      <SearchResult
                        linkFunc={createLink}
                        query={searchQuery}
                        result={searchResult}
                      />
                    )}
                  </Column>
                </Columns>
                <Columns>
                  <Column>
                    {searchResult && (
                      <SearchPagination onPageClick={this.onPageClick} result={searchResult} />
                    )}
                  </Column>
                </Columns>
              </Column>
            </Columns>
          </Container>
        </Section>
      </div>
    );
  }
}

SearchPage.propTypes = {
  history: PropTypes.shape({
    replace: PropTypes.func.isRequired
  }).isRequired,
  match: PropTypes.shape({
    path: PropTypes.string.isRequired
  }).isRequired,
  location: PropTypes.shape({
    search: PropTypes.string.isRequired
  }).isRequired,
  isKfAdmin: PropTypes.bool.isRequired,
  handbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  dispatch: PropTypes.func.isRequired,
  bannerUrl: PropTypes.string,
  session: SessionShape.isRequired
};

SearchPage.defaultProps = {
  bannerUrl: undefined
};

export default connect(state => ({
  isKfAdmin: selectIsKfAdmin(state),
  handbooks: selectHandbooks(state),
  bannerUrl: selectBannerUrl(state),
  session: selectSession(state)
}))(SearchPage);
