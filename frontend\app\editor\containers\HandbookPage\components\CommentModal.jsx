// @flow
import React, { Fragment, useState, useEffect } from 'react';
import { Modal, Button, Content } from 'kf-bui';

import { FormattedMessage } from 'react-intl';
import { fetchComments, persistComment, deleteComment } from '../../EditorPage/api';
import CommentCard, { type Comment } from './CommentCard';

type Props = {
  handbookId: string,
  userEmail: string,
  toggleHide: () => void
};

const CommentModal = ({ handbookId, userEmail, toggleHide }: Props) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState<boolean>(false);
  const [newText, setNewText] = useState<string>('');

  const resetNewComment = () => {
    setNewComment(false);
    setNewText('');
  };

  useEffect(() => {
    fetchComments(handbookId).then(retrievedComments => setComments(retrievedComments));
  }, [handbookId]);

  return (
    <Modal onClose={toggleHide}>
      <Modal.Header onClose={toggleHide}>
        <Modal.Title>
          <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.title" />
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="comments">
        <Content>
          {comments
            .sort((c1, c2) => new Date(c2.editedDate) - new Date(c1.editedDate))
            .map(c => (
              <CommentCard
                key={c.id}
                comment={c}
                saveFunction={text =>
                  persistComment({
                    ...c,
                    text,
                    editedBy: userEmail
                  }).then(updatedComment =>
                    setComments(old => [...old.filter(f => f.id !== c.id), updatedComment])
                  )
                }
                deleteFunction={id =>
                  deleteComment(id).then(e => {
                    if (e.status === 204) {
                      setComments(old => old.filter(f => f.id !== c.id));
                    }
                  })
                }
              />
            ))}
          {newComment && (
            <Fragment>
              <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.newCommentButton" />
              <textarea
                id="kommentar"
                value={newText}
                onChange={e => setNewText(e.target.value)}
                style={{
                  display: 'block',
                  width: '100%',
                  marginBottom: '5px',
                  height: '4em',
                  padding: '5px'
                }}
              />
              <Button
                size="small"
                onClick={() =>
                  persistComment({
                    text: newText,
                    editedBy: userEmail,
                    editedDate: new Date(),
                    handbookId
                  })
                    .then(updatedComment => setComments(old => [...old, updatedComment]))
                    .then(resetNewComment)
                }
              >
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.saveButton" />
              </Button>
              <Button size="small" onClick={resetNewComment}>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.cancelButton" />
              </Button>
            </Fragment>
          )}
          {!newComment && (
            <Button onClick={() => setNewComment(true)}>
              <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.newCommentButton" />
            </Button>
          )}
        </Content>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={toggleHide}>
          <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.closeButton" />
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CommentModal;
