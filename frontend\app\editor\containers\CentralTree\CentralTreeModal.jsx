import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Modal, Field, Label, Select, Tree } from 'kf-bui';

import { ChapterShape, HandbookShape } from '../../../common/shapes';
import { fetchHandbookContent } from './actions';
import { selectHandbooks, selectRootChapters, selectSelectedHandbook } from './selectors';
import ConnectedChapterNode from './ChapterItem';

class CentralTreeModal extends React.Component {
  onChapterClick = chapter => {
    if (this.props.onChapterClick) {
      this.props.onChapterClick(chapter);
      this.props.onHide();
    }
  };

  onSectionClick = section => {
    if (this.props.onSectionClick) {
      this.props.onSectionClick(section);
      this.props.onHide();
    }
  };

  renderTree() {
    const { chapters, selectedHandbook } = this.props;

    const items =
      chapters &&
      chapters.map(chapter => {
        return (
          <ConnectedChapterNode
            chapter={chapter}
            key={chapter.id}
            onChapterClick={this.onChapterClick}
            onSectionClick={this.onSectionClick}
          />
        );
      });

    if (!selectedHandbook) {
      return null;
    }

    return <Tree>{items}</Tree>;
  }

  render() {
    const { handbooks, selectedHandbook, title, onHide, fetchHandbookContentFun } = this.props;

    return (
      <Modal onClose={onHide}>
        <Modal.Header onClose={onHide}>
          <Modal.Title>{title}</Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ minHeight: '300px' }}>
          <Field>
            <Label htmlFor="centralhb">Sentral håndbok</Label>
            <Select
              aria-labelledby="centralhb"
              options={handbooks}
              value={selectedHandbook}
              labelKey="title"
              onChange={fetchHandbookContentFun}
            />
          </Field>
          {this.renderTree()}
        </Modal.Body>
        <Modal.Footer />
      </Modal>
    );
  }
}

CentralTreeModal.propTypes = {
  chapters: PropTypes.arrayOf(ChapterShape),
  fetchHandbookContentFun: PropTypes.func.isRequired,
  handbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  selectedHandbook: HandbookShape,
  title: PropTypes.string.isRequired,
  onHide: PropTypes.func.isRequired,
  onSectionClick: PropTypes.func,
  onChapterClick: PropTypes.func
};

CentralTreeModal.defaultProps = {
  selectedHandbook: null,
  onChapterClick: null,
  onSectionClick: null,
  chapters: null
};

const mapStateToProps = state => ({
  handbooks: selectHandbooks(state),
  selectedHandbook: selectSelectedHandbook(state),
  chapters: selectRootChapters(state)
});

const mapDispatchToProps = {
  fetchHandbookContentFun: fetchHandbookContent
};

export default connect(mapStateToProps, mapDispatchToProps)(CentralTreeModal);
