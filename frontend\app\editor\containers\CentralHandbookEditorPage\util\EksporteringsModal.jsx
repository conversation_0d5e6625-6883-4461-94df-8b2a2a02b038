// @flow
import React, { useState, useEffect, Fragment } from 'react';
import { connect } from 'react-redux';
import { Modal, Field, Button, Select, Label, FormattedDate, Icon } from 'kf-bui';

import { fetchAllHandbookVersions } from '../api';
import { makeSelectHandbookDescendants } from '../selectors';

import type { CentralHandbookType, CentralChildrenType } from '../../../../types';

type Props = {
  centralHandbook: CentralHandbookType,
  handbookDescendants: CentralChildrenType[],
  onHide: () => void
};

const EksporteringsModal = ({ onHide, centralHandbook, handbookDescendants }: Props) => {
  const [selectedVersion, setSelectedVersion] = useState<?CentralHandbookType>(undefined);
  const [publishedVersions, setPublishedVersions] = useState<CentralHandbookType[]>([]);
  useEffect(() => {
    fetchAllHandbookVersions(centralHandbook.id).then(versions => setPublishedVersions(versions));
  }, []);
  const makeUrl = (fileType: string, id: string) => {
    const BASE_URL = '/handboker/handbooks/download/central';

    return `${BASE_URL}/${fileType}/${id}`;
  };
  const versionRender = version =>
    version.versionOf ? (
      <Fragment>
        <FormattedDate value={version.createdDate} format="YYYY.MM.DD-HH:mm" />
        <span style={{ marginLeft: '5px' }}>publisert av {version.createdBy}</span>
      </Fragment>
    ) : (
      <span>Upublisert versjon</span>
    );
  const sortedVersions = publishedVersions.sort(
    (v1, v2) => new Date(v2.createdDate) - new Date(v1.createdDate)
  );
  const latestVersion = sortedVersions.length ? sortedVersions[0] : undefined;

  const unpublishedChanges = latestVersion
    ? new Date(centralHandbook.updatedDate) > new Date(latestVersion.createdDate) ||
      handbookDescendants.find(
        descendant => new Date(descendant.updatedDate) > new Date(latestVersion.createdDate)
      )
    : true;

  const versionOptions = unpublishedChanges
    ? [centralHandbook].concat(publishedVersions)
    : publishedVersions;

  return (
    <Modal onClose={onHide}>
      <Modal.Header>
        <Modal.Title>Eksport av {centralHandbook.title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div style={{ height: '10em' }}>
          <Field>
            <Label htmlFor="handbookVersion">
              Velg hvilken versjon av håndboken du vil eksportere
            </Label>
            <Select
              id="handbookVersion"
              name="handbookVersion"
              aria-label="Velg versjon av håndboken du vil eksportere"
              options={versionOptions}
              value={selectedVersion}
              onChange={e => {
                setSelectedVersion(e);
              }}
              valueKey="id"
              labelKey="createdDate"
              optionRenderer={versionRender}
              valueRenderer={versionRender}
            />
          </Field>
          {selectedVersion && (
            <Fragment>
              <p style={{ marginBottom: '5px' }}>Vil du eksportere dokumentet?</p>
              <Button size="small" href={makeUrl('pdf', selectedVersion.id)}>
                <Icon icon="file-pdf-o" size="small" />
                <span>Eksporter som PDF</span>
              </Button>
              <Button
                style={{ 'margin-left': '5px' }}
                size="small"
                href={makeUrl('word', selectedVersion.id)}
              >
                <Icon icon="file-word-o" size="small" />
                <span>Eksporter som Word</span>
              </Button>
            </Fragment>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onHide}>Lukk</Button>
      </Modal.Footer>
    </Modal>
  );
};

const mapStateToProps = (state, ownProps) => {
  const { centralHandbook } = ownProps;
  const selectHandbookDescendants = makeSelectHandbookDescendants(centralHandbook.id);
  return {
    handbookDescendants: selectHandbookDescendants(state)
  };
};

export default connect(mapStateToProps)(EksporteringsModal);
