// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`setOrganization calls the API, updates the reducer 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          "9900",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/setExternalOrganization",
      },
    },
  ],
  "fork": Array [
    Object {
      "@@redux-saga/IO": true,
      "FORK": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchHandbooks",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "FORK": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchHandbooks",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "FORK": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchSubscriptions",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "FORK": Object {
        "args": Array [],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/fetchLocalEditorsForOrganization",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": Object {
          "payload": Object {
            "organization": Object {
              "id": "9900",
              "name": "Kommuneforlgat",
            },
          },
          "type": "SET_ORGANIZATION_SUCCESS",
        },
        "channel": null,
      },
    },
  ],
  "select": Array [
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/selectSession",
      },
    },
  ],
}
`;

exports[`setOrganization toasts when something goes awry 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          Object {
            "organization": Object {
              "id": "other",
              "language": "nb",
              "name": "other",
            },
            "user": Object {},
          },
          "editor.containers.App.changedOrganizationFail1",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/translationWrapper",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          Object {
            "organization": Object {
              "id": "other",
              "language": "nb",
              "name": "other",
            },
            "user": Object {},
          },
          "editor.containers.App.changedOrganizationFail2",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/translationWrapper",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": "@@redux-saga-test-plan/json/function/<anonymous>",
        "channel": null,
      },
    },
  ],
  "select": Array [
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/selectSession",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/<anonymous>",
      },
    },
  ],
}
`;

exports[`setOrganization toasts when switching organizations 1`] = `
Object {
  "call": Array [
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          "9900",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/setExternalOrganization",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          Object {
            "organization": Object {
              "id": "other",
              "language": "nb",
              "name": "other",
            },
            "user": Object {},
          },
          "editor.containers.App.changedOrganizationFail1",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/translationWrapper",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "CALL": Object {
        "args": Array [
          Object {
            "organization": Object {
              "id": "other",
              "language": "nb",
              "name": "other",
            },
            "user": Object {},
          },
          "editor.containers.App.changedOrganizationFail2",
        ],
        "context": null,
        "fn": "@@redux-saga-test-plan/json/function/translationWrapper",
      },
    },
  ],
  "put": Array [
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": Object {
          "payload": Object {
            "organization": Object {
              "id": "9900",
              "name": "Kommuneforlgat",
            },
          },
          "type": "SET_ORGANIZATION_SUCCESS",
        },
        "channel": null,
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "PUT": Object {
        "action": "@@redux-saga-test-plan/json/function/<anonymous>",
        "channel": null,
      },
    },
  ],
  "select": Array [
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/selectSession",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/<anonymous>",
      },
    },
    Object {
      "@@redux-saga/IO": true,
      "SELECT": Object {
        "args": Array [],
        "selector": "@@redux-saga-test-plan/json/function/<anonymous>",
      },
    },
  ],
}
`;
