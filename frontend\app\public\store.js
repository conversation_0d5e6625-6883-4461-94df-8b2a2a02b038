/**
 * Create the store with asynchronously loaded reducers
 */

import { createStore, applyMiddleware, compose } from 'redux';
import thunk from 'redux-thunk';
import { routerMiddleware, connectRouter } from 'connected-react-router';
import createSagaMiddleware from 'redux-saga';
import createReducer from './reducers';
import sagas from './sagas';

// Redux devtools https://github.com/zalmoxisus/redux-devtools-extension
const devtools = window.devToolsExtension || (() => noop => noop);

export default function configureStore(initialState = {}, history) {
  const sagaMiddleware = createSagaMiddleware();

  // Create the store with two middlewares
  // 1. sagaMiddleware: Makes redux-sagas work
  // 2. routerMiddleware: Syncs the location/URL path to the state
  const middlewares = [thunk, sagaMiddleware, routerMiddleware(history)];

  const enhancers = [applyMiddleware(...middlewares), devtools()];
  const rootReducer = createReducer();
  const connectedReducer = connectRouter(history)(rootReducer);

  const store = createStore(connectedReducer, initialState, compose(...enhancers));

  // Extensions
  store.runSaga = sagaMiddleware.run;
  store.runSaga(sagas);

  return store;
}
