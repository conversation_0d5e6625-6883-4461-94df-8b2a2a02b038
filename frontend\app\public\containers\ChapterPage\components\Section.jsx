// @flow

import * as React from 'react';
import { connect } from 'react-redux';
import { Title, Icon, Section, Group, Column, Button } from 'kf-bui';
import Waypoint from 'react-waypoint';
import PropTypes from 'prop-types';
import RenderHtml from '../../../../common/components/RenderHtml';
import { fetchSection } from '../../HandbookPage/actions';
import { selectSectionEntities } from '../../HandbookPage/selectors';
import Attachments from './Attachments';
import { Attachment } from '../../../../editor/components/Attachments/icons';
import { getAttachmentCount } from '../../HandbookPage/api';

type Props = {
  sectionId: string,
  sections: Object,
  fetchSectionFun: (sectionId: string) => void,
  setSection: (section: Object) => void,
  removeSection: (section: Object) => void
};

type State = {
  showAttachments: boolean,
  attachmentCount: number
};

function getSection(sections: Object, sectionId: string): ?Object {
  return sections[sectionId];
}

class SectionPage extends React.Component<Props, State> {
  wrapperRef: { current: null | HTMLDivElement };

  constructor(props: Props) {
    super(props);

    this.wrapperRef = React.createRef();
    this.state = {
      showAttachments: false,
      attachmentCount: 0
    };
  }

  componentDidMount() {
    const { sections, sectionId, fetchSectionFun } = this.props;
    const section = getSection(sections, sectionId);
    if (section && !section.text) {
      fetchSectionFun(section.id);
    }
    document.addEventListener('mousedown', this.handleClickOutside);
    this.handleAttachmentCount();
  }

  componentWillReceiveProps(nextProps) {
    const { sectionId, fetchSectionFun } = this.props;
    if (sectionId !== nextProps.sectionId && !getSection(nextProps.sections, nextProps.sectionId)) {
      fetchSectionFun(nextProps.sectionId);
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleClickOutside);
  }

  handleAttachmentCount = async () => {
    try {
      const { sectionId } = this.props;
      const attachmentCount = await getAttachmentCount('section', sectionId);
      this.setState({ attachmentCount: attachmentCount.count });
    } catch (e) {
      console.error(e);
    }
  };

  handleClickOutside = (event: MouseEvent) => {
    if (
      this.wrapperRef.current &&
      event.target instanceof Node &&
      !this.wrapperRef.current.contains(event.target)
    ) {
      this.setState({ showAttachments: false });
    }
  };

  handleShowAttachments = (isShow: boolean) => {
    this.setState({ showAttachments: isShow });
  };

  render() {
    const { showAttachments, attachmentCount } = this.state;
    const { setSection, removeSection, sections, sectionId } = this.props;
    const section = getSection(sections, sectionId);

    if (!section) return null;
    return (
      <Section style={{ paddingBottom: '0rem', paddingTop: '0rem' }}>
        <hr />
        <Column className="section-content">
          <Group className="content-header">
            <Title id={`content-${section.id}`} size="4">
              <Icon icon="file-text-o" /> {section.title}
            </Title>

            {attachmentCount > 0 && (
              <div className="public-attachment-container">
                <Button
                  className="public-attachment-btn"
                  onClick={() => this.handleShowAttachments(!showAttachments)}
                >
                  <Attachment />
                </Button>

                {showAttachments && (
                  <div ref={this.wrapperRef}>
                    <Attachments
                      sectionId={sectionId}
                      type="section"
                      onClose={() => this.handleShowAttachments(false)}
                    />
                  </div>
                )}
              </div>
            )}
          </Group>
        </Column>
        <Waypoint onEnter={() => setSection(section)} onLeave={() => removeSection(section)}>
          <div>
            <RenderHtml html={section.text} />
          </div>
        </Waypoint>
      </Section>
    );
  }
}

SectionPage.propTypes = {
  sections: PropTypes.object.isRequired,
  sectionId: PropTypes.string.isRequired,
  setSection: PropTypes.func.isRequired,
  removeSection: PropTypes.func.isRequired,
  fetchSectionFun: PropTypes.func.isRequired
};

export default connect(state => ({ sections: selectSectionEntities(state) }), {
  fetchSectionFun: fetchSection
})(SectionPage);
