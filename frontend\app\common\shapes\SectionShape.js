import PropTypes from 'prop-types';

export default PropTypes.shape({
  id: PropTypes.string.isRequired,
  handbookId: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  text: PropTypes.string,
  title: PropTypes.string.isRequired,
  importedHandbookId: PropTypes.string,
  importedHandbookChapterId: PropTypes.string,
  localChange: PropTypes.bool,
  pendingChanges: PropTypes.bool
});
