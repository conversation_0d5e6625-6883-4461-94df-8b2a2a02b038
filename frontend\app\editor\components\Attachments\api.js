import axios from 'axios';

export async function uploadFile(file, onProgress) {
  const formData = new FormData();
  formData.append('file', file);
  const config = {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: progressEvent => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(percentCompleted);
    }
  };

  const response = await axios.post('/files', formData, config);
  return response.data;
}

export async function deleteFile(fileId) {
  const response = await axios.delete(`/files/${fileId}`);
  return response.data;
}

export async function fetchFile(fileId) {
  const response = await axios.get(`/files/${fileId}`, {
    responseType: 'blob'
  });
  return response.data;
}

export async function saveHandbookFileLinks(data) {
  const response = await axios.post('/handbooks/file-links', data);
  return response.data;
}

export async function fetchHandbookFileLinksByType(type, id) {
  const response = await axios.get(`/handbooks/file-links/${type}/${id}`);
  return response.data;
}

export async function fetchHandbookFileCount(type, id) {
  const response = await axios.get(`/handbooks/file-links/${type}/${id}/count`);
  return response.data;
}
