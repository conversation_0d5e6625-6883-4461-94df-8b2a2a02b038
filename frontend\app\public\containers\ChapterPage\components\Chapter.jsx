// @flow

import React from 'react';
import { connect } from 'react-redux';
import { Title, Icon } from 'kf-bui';

import Section from './Section';
import { selectSubChaptersAndSections } from '../../HandbookPage/selectors';
import type { ChapterType, ChildrenType, SectionType } from '../../../../types';

/* eslint-disable no-confusing-arrow */
// TODO: Remove after fixing prettier and eslint conflict a

type Props = {
  chapter: ChapterType,
  chaptersAndSections: ChildrenType[],
  setSection: (section: SectionType) => void,
  removeSection: (section: SectionType) => void
};

const Chapter = ({ chapter, chaptersAndSections, setSection, removeSection }: Props) => (
  <div>
    <hr />
    <Title id={`content-${chapter.id}`} size="4">
      <Icon icon="bookmark-o" size="small" /> {chapter.title}
    </Title>
    {chaptersAndSections.map(child =>
      child.type === 'LOCAL_CHAPTER' ? (
        <ConnectedChapter
          key={child.id}
          chapter={child}
          setSection={setSection}
          removeSection={removeSection}
        />
      ) : (
        <Section
          key={child.id}
          sectionId={child.id}
          setSection={setSection}
          removeSection={removeSection}
        />
      )
    )}
  </div>
);

const makeMapStateToProps = (_, ownProps) => {
  const selectSubChaptersAndSectionsSelector = selectSubChaptersAndSections(ownProps.chapter.id);
  const mapStateToProps = state => ({
    chaptersAndSections: selectSubChaptersAndSectionsSelector(state)
  });
  return mapStateToProps;
};

const ConnectedChapter = connect(makeMapStateToProps)(Chapter);
export default ConnectedChapter;
