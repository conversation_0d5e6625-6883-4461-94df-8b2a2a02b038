// @flow
/* eslint-disable react/prop-types */

import { withFormik } from 'formik';
import React from 'react';
import { compose } from 'redux';
import * as Yup from 'yup';
import { Addons, Button, Field, Form, Input, Label, SrOnly, Help } from 'kf-bui';
import injectT from '../../../../common/i18n';
import type { LinkCollectionType } from '../../../../types';

type Props = {
  id?: string,
  // eslint-disable-next-line react/no-unused-prop-types
  handbookId: string,
  // eslint-disable-next-line react/no-unused-prop-types
  sortOrder: number,
  t: string => string,
  // eslint-disable-next-line react/no-unused-prop-types
  onSave: LinkCollectionType => void,
  onCancel: () => void
};

type FormProps = {
  handleSubmit: () => void,
  handleChange: () => void,
  errors: { [string]: string },
  values: { [string]: string }
};

const LinkCollectionForm = ({
  handleSubmit,
  handleChange,
  errors,
  values,
  t,
  onCancel,
  id
}: Props & FormProps) => {
  const isEditing = !!id;

  return (
    <Form onSubmit={handleSubmit}>
      <Addons>
        <Field>
          <SrOnly>
            <Label>{t('srTitleLabel')}</Label>
          </SrOnly>
          <Input
            style={errors.title ? { borderColor: 'red' } : {}}
            size="small"
            value={values.title}
            placeholder={t('titleInputPlaceholder')}
            onChange={handleChange}
            id="title"
          />
          {errors.title && <Help color="danger">{errors.title}</Help>}
        </Field>
        {isEditing && (
          <Button control color="danger" size="small" onClick={onCancel}>
            {t('cancelButtonText')}
          </Button>
        )}
        <Button control size="small" color="primary" type="submit">
          {isEditing ? 'Lagre' : t('addButtonText')}
        </Button>
      </Addons>
    </Form>
  );
};

LinkCollectionForm.defaultProps = {
  id: undefined
};

export default compose(
  injectT('editor.containers.LinkPage.components.LinkCollectionForm'),
  withFormik({
    enableReinitialize: true,
    mapPropsToValues: ({ id, title, sortOrder, handbookId, links }) => ({
      id: id || null,
      title,
      sortOrder,
      handbookId,
      links: links || []
    }),
    validationSchema: Yup.object().shape({
      title: Yup.string().required('Lenken må ha en tittel')
    }),
    handleSubmit: (payload, { props, resetForm }) => {
      const { id, title, sortOrder, handbookId, links } = payload;

      const linkCollection = { id, title, sortOrder, handbookId, links };
      props.onSave(linkCollection);
      resetForm({ title: '', id: '' });
    }
  })
)(LinkCollectionForm);
