import PropTypes from 'prop-types';

export const HitShape = PropTypes.shape({
  id: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  highlight: PropTypes.string,
  isHandbook: PropTypes.bool,
  isChapter: PropTypes.bool,
  isSection: PropTypes.bool
});

export default PropTypes.shape({
  totalHits: PropTypes.number.isRequired,
  page: PropTypes.number.isRequired,
  pageSize: PropTypes.number.isRequired,
  results: PropTypes.arrayOf(HitShape).isRequired
});
