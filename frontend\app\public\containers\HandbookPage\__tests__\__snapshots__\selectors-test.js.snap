// Jest <PERSON>nap<PERSON> v1, https://goo.gl/fbAQLP

exports[`Handbooks selectors selectSubChaptersAndSections selects the sub chapters and sections and applies property to chapters so we can differentiate them from sections 1`] = `
Array [
  Object {
    "handbookId": "HMT",
    "id": "hmtChap3",
    "parentId": "hmtChap6",
    "sortOrder": 2,
    "title": "Risiko",
    "type": "LOCAL_CHAPTER",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc7",
    "parentId": "hmtChap6",
    "sortOrder": 7,
    "title": "Rutiner for barnehager",
    "type": "LOCAL_SECTION",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc8",
    "parentId": "hmtChap6",
    "sortOrder": 8,
    "title": "Rutiner for <PERSON><PERSON><PERSON>",
    "type": "LOCAL_SECTION",
  },
  Object {
    "handbookId": "HMT",
    "id": "hmtDoc9",
    "parentId": "hmtChap6",
    "sortOrder": 9,
    "title": "Rutiner for kontor",
    "type": "LOCAL_SECTION",
  },
]
`;
