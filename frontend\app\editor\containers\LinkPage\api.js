import axios from 'axios';

const BASE_URL = '/handbooks/links';

export function getLinkCollectionForHandbook(handbookId) {
  return axios.get(`${BASE_URL}/${handbookId}`).then(res => res.data);
}

export function persistLink(link, linkCollectionId) {
  return axios.post(`${BASE_URL}/link/${linkCollectionId}`, link).then(res => res.data);
}

export function persistLinkCollection(linkCollection) {
  return axios.post(`${BASE_URL}/link-collection`, linkCollection).then(res => res.data);
}

export function deleteLink(linkId) {
  return axios.delete(`${BASE_URL}/link/${linkId}`).then(res => res.data);
}

export function deleteLinkCollection(linkCollectionId) {
  return axios.delete(`${BASE_URL}/link-collection/${linkCollectionId}`).then(res => res.data);
}
