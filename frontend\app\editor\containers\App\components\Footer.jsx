import React from 'react';
import { Level, Container, Footer as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'kf-bui';
import PropTypes from 'prop-types';

import { SessionShape } from '../../../shapes';

// application={`${t('application')} ${applicationVersion}`}
// user={`${t('loggedIn')}: ${userEmail}`}
// TODO: i18n

const Footer = ({ session, showOptOutModal }) => (
  <RulmaFooter>
    <Container>
      <Level>
        <Level.Item textCentered flexible style={{ flexDirection: 'column' }}>
          <a href="http://www.kf.no/" target="_blank" rel="noopener noreferrer">
            KF
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Personvern_KF_Håndbøker.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Personvernerklæring
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_Håndbøker.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Informasjonskapsler
          </a>
          <button type="button" className="cookie-settings-btn" onClick={showOptOutModal}>
            Innstillinger for informasjonskapsler
          </button>
        </Level.Item>
        <Level.Item textCentered>
          KF Håndbøker
          <br />
          {session.appVersion}
        </Level.Item>
        <Level.Item textCentered>
          Innlogget{' '}
          {(() => {
            if (session.user.globalAdmin) return 'som KF-Administrator';
            if (session.user.localAdmin) return 'som lokal administrator';
            if (session.user.localUser) return 'som lokal redaktør';
            return 'uten rettigheter';
          })()}
          <br />
          {session.user.email}
        </Level.Item>
      </Level>
    </Container>
  </RulmaFooter>
);

Footer.propTypes = {
  session: SessionShape.isRequired,
  showOptOutModal: PropTypes.func.isRequired
};

export default Footer;
