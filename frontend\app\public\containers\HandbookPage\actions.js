import { replace } from 'connected-react-router';
import { createAction } from 'redux-actions';
import * as api from './api';

export const fetchHandbookSuccess = createAction('HandbookPage/FETCH_HANDBOOK_SUCCESS');

export const fetchSectionSuccess = createAction('HandbookPage/FETCH_SECTION_SUCCESS');

/**
 * @param {String} externalOrgId
 * @param {String} handbookId
 */
export function fetchHandbook(externalOrgId, handbookId) {
  return dispatch => {
    api
      .fetchHandbook(externalOrgId, handbookId)
      .then(handbook => dispatch(fetchHandbookSuccess(handbook)))
      .catch(error => {
        if (error.response) {
          // This probably means the error is from axios.
          // We assume that we don't have access (because IP filtering)
          // and redirect to the 'forbidden' page
          dispatch(replace('/forbidden'));
        }
      });
  };
}

/**
 * @param {String} sectionId
 */
export function fetchSection(sectionId) {
  return dispatch => {
    api.fetchSection(sectionId).then(section => dispatch(fetchSectionSuccess(section)));
  };
}
