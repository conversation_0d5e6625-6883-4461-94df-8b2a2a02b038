import { call, fork, put, select, takeLatest, all } from 'redux-saga/effects';
import { toast } from 'kf-toaster';

import { selectSession, selectLocale } from './selectors';
import * as api from './api';
import * as actions from './actions';
import { fetchHandbooks as fetchCentralHandbooks } from '../CentralTree/saga';
import {
  fetchHandbooks as fetchLocalHandbooks,
  fetchSubscriptions,
  fetchLocalEditorsForOrganization
} from '../EditorPage/saga';
import { translationMessages } from '../../../common/i18n';

// export for testing purposes
export function* translationWrapper(locale, identifier) {
  return yield call(translationMessages[locale][identifier]);
}

export function* setOrganization({ payload: id }) {
  try {
    const hasPreviousOrg = (yield select(selectSession)).organization;
    const session = yield call(api.setExternalOrganization, id);

    yield put(actions.setOrganizationSuccess(session));

    // If we are already logged into an organization, notify the user because we have switched
    if (hasPreviousOrg) {
      const locale = yield select(selectLocale);
      const i18identifier = 'editor.containers.App.changedOrganization';
      const successText = translationMessages[locale][i18identifier];
      yield put(toast.success(successText));
    }

    // We want to refetch central and local content when changing the organization
    yield all([
      fork(fetchLocalHandbooks),
      fork(fetchCentralHandbooks),
      fork(fetchSubscriptions),
      fork(fetchLocalEditorsForOrganization)
    ]);
  } catch (error) {
    const locale = yield select(selectLocale);
    const i18identifier1 = 'editor.containers.App.changedOrganizationFail1';
    const i18identifier2 = 'editor.containers.App.changedOrganizationFail2';
    const errorText1 = yield call(translationWrapper, locale, i18identifier1);
    const errorText2 = yield call(translationWrapper, locale, i18identifier2);
    yield put(toast.error(errorText1, errorText2));
  }
}

export default function* rootSaga() {
  yield takeLatest(actions.setOrganization, setOrganization);
}
