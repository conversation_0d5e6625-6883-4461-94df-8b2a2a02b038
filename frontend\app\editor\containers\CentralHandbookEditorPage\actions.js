// @flow
import { createAction } from 'redux-actions';
import type { CentralChapterType } from '../../../types';

export const fetchCentralHandbooks = createAction(
  'CentralEditorPage/FETCH_CENTRAL_HANDBOOKS_REQUEST'
);
export const fetchPublishedCentralHandbooks = createAction(
  'CentralEditorPage/FETCH_PUBLISHED_CENTRAL_HANDBOOKS_REQUEST'
);
export const fetchCentralHandbooksSuccess = createAction(
  'CentralEditorPage/FETCH_CENTRAL_HANDBOOKS_SUCCESS'
);

export const fetchAllReadingLinks = createAction('FETCH_READING_LINK');
export const fetchAllReadingLinksSuccess = createAction('FETCH_READING_LINK_SUCCESS');

export const saveCentralHandbook = createAction('CentralEditorPage/SAVE_CENTRAL_HANDBOOK');
export const saveCentralHandbookSuccess = createAction(
  'CentralEditorPage/SAVE_CENTRAL_HANDBOOK_SUCCESS'
);

export const deleteCentralHandbook = createAction('CentralEditorPage/DELETE_CENTRAL_HANDBOOK');
export const deleteCentralHandbookSuccess = createAction(
  'CentralEditorPage/DELETE_CENTRAL_HANDBOOK_SUCCESS',
  (centralHandbookId: string) => centralHandbookId
);

export const fetchCentralChapters = createAction('CentralEditorPage/FETCH_CENTRAL_CHAPTERS');
export const fetchCentralChaptersSuccess = createAction(
  'CentralEditorPage/FETCH_CENTRAL_CHAPTERS_SUCCESS',
  (centralChapters: CentralChapterType[]) => centralChapters
);

export const saveCentralChapter = createAction('CentralEditorPage/SAVE_CENTRAL_CHAPTER');
export const saveCentralChapterSuccess = createAction(
  'CentralEditorPage/SAVE_CENTRAL_CHAPTER_SUCCESS'
);
export const deleteCentralChapter = createAction('CentralEditorPage/DELETE_CENTRAL_CHAPTER');
export const deleteCentralChapterSuccess = createAction(
  'CentralEditorPage/DELETE_CENTRAL_CHAPTER_SUCCESS'
);

export const fetchCentralSections = createAction('FETCH_CENTRAL_SECTION');
export const fetchCentralSectionsSuccess = createAction('FETCH_CENTRAL_SECTION_SUCCESS');

export const saveCentralSection = createAction('SAVE_CENTRAL_SECTION');
export const saveCentralSectionSuccess = createAction('SAVE_CENTRAL_SECTION_SUCCESS');

export const deleteCentralSection = createAction('DELETE_CENTRAL_SECTION');
export const deleteCentralSectionSuccess = createAction('DELETE_CENTRAL_SECTION_SUCCESS');

export const sortCentralItems = createAction('CentralEditorPage/SORT_CENTRAL_ELEMENTS');
