/* eslint-env jest */
import reducer, { initialState } from '../reducer';
import { setOrganizationSuccess } from '../actions';

describe('sessionReducer', () => {
  let state;

  beforeEach(() => {
    state = initialState;
  });

  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toEqual(initialState);
  });

  it('should set entire state to action payload (session)', () => {
    const next = reducer(
      state,
      setOrganizationSuccess({
        appVersion: '6.6.6-TEST',
        organization: '9000',
        name: 'Kommuneforlaget '
      })
    );

    expect(next).toMatchSnapshot();
  });
});
