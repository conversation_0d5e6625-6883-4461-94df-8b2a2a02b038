import React from 'react';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';

import { Button, Icon, Modal, Help, Field, Input, Checkbox, Label, Select } from 'kf-bui';

import injectT from '../../../common/i18n';
import { saveLocalHandbook } from '../EditorPage/actions';
import { HandbookShape } from '../../shapes';
import { selectPublishedHandbooks } from '../CentralTree/selectors';
import { updateFormState, trim, merge, updateFormValue } from '../../util/form';

const initialFormState = {
  title: '',
  isPublic: true
};

class CreateOrUpdateHandbook extends React.Component {
  state = {
    form: merge(initialFormState, this.props.handbook || {}),
    isSaving: false,
    selectedCentralHandbook: null
  };

  componentWillReceiveProps(nextProps) {
    const { handbook, onHide } = this.props;
    const { isSaving } = this.state;

    if (isSaving && nextProps.handbook !== handbook) {
      onHide();
    }
  }

  onCentralHandbookChange = hb => {
    this.setState({ selectedCentralHandbook: hb });
    // If we haven't entered a title when selecting a central handbook
    // we prepopulate the title
    if (!this.state.form.title) {
      this.setState(updateFormValue('title', hb.title, 'form'));
    }
  };

  handleChange = event => this.setState(updateFormState(event, 'form'));

  handleSubmit = event => {
    event.preventDefault();
    this.setState({ isSaving: true });
    this.props.saveLocalHandbook({
      importedHandbookId: this.state.selectedCentralHandbook
        ? this.state.selectedCentralHandbook.id
        : null,
      ...this.props.handbook,
      ...trim(this.state.form),
      // Always set localChange to true when editing something that was based on a central handbook
      localChange: Boolean(this.props.handbook ? this.props.handbook.importedHandbookId : false)
    });
  };

  isValid() {
    const { form } = this.state;
    return form.title.trim() !== '';
  }

  render() {
    const { centralHandbooks, handbook, t, ...props } = this.props;
    const { isSaving, form, selectedCentralHandbook } = this.state;

    // While we are submitting, the onHide is a noop
    // so we can't close the modal until the server has responded
    const onHide = isSaving ? () => {} : props.onHide;

    return (
      <Modal onClose={onHide} autoFocus={false}>
        <form onSubmit={this.handleSubmit}>
          <Modal.Header onClose={onHide}>
            <Modal.Title>{handbook ? t('editTitle') : t('createTitle')}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {!handbook && centralHandbooks.length > 0 && (
              <Field>
                <Label htmlFor="centralHandbook">Sentral håndbok</Label>
                <Select
                  id="centralHandbook"
                  clearable
                  aria-label="Sentral håndbok"
                  name="centralHandbook"
                  options={centralHandbooks}
                  placeholder="Sentral Håndbok"
                  labelKey="title"
                  onChange={this.onCentralHandbookChange}
                  disabled={isSaving}
                  value={selectedCentralHandbook}
                />
                <Help>{t('centralBased')}</Help>
              </Field>
            )}
            <Field>
              <Label htmlFor="title">Tittel</Label>
              <Input
                id="title"
                name="title"
                placeholder="Tittel"
                autoFocus
                onChange={this.handleChange}
                value={form.title}
                required
              />
              {selectedCentralHandbook && form.title && (
                <Help>
                  {selectedCentralHandbook.title === form.title ? (
                    <span>{t('autoSync')}</span>
                  ) : (
                    <span>
                      <Icon icon="warning" size="small" /> {t('manualSync')}
                    </span>
                  )}
                </Help>
              )}
            </Field>
            <Field>
              <Checkbox onChange={this.handleChange} checked={form.isPublic} name="isPublic">
                {' '}
                {t('publicLabel')}
              </Checkbox>
            </Field>
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={onHide}>Avbryt</Button>
            <Button loading={isSaving} disabled={!this.isValid()} type="submit" color="success">
              {t('saveButton')}
            </Button>
          </Modal.Footer>
        </form>
      </Modal>
    );
  }
}

CreateOrUpdateHandbook.propTypes = {
  centralHandbooks: PropTypes.arrayOf(HandbookShape).isRequired,
  saveLocalHandbook: PropTypes.func.isRequired,
  // If we are editing a handbook
  handbook: HandbookShape,
  onHide: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired
};

CreateOrUpdateHandbook.defaultProps = {
  handbook: null
};

const mapStateToProps = state => ({ centralHandbooks: selectPublishedHandbooks(state) });

export default compose(
  injectT('editor.containers.CreateOrUpdateHandbook'),
  connect(mapStateToProps, {
    saveLocalHandbook
  })
)(CreateOrUpdateHandbook);
