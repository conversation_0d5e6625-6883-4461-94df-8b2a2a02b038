import { createSelector } from 'reselect';
import { flatMap } from 'lodash';
import { sortBySortOrderAndTitle } from '../../../common/util';

export const selectHandbookState = state => state.handbook;
export const selectHandbook = state => selectHandbookState(state).handbook;

export const selectChapterEntities = state => selectHandbookState(state).chapters;
export const selectSectionEntities = state => selectHandbookState(state).sections;

export const selectChaptersArray = state =>
  Object.values(selectChapterEntities(state)).map(chapter => ({
    ...chapter,
    type: 'LOCAL_CHAPTER'
  }));
export const selectSectionsArray = state =>
  Object.values(selectSectionEntities(state)).map(section => ({
    ...section,
    type: 'LOCAL_SECTION'
  }));

/**
 * Select the (sorted) root chapters for a handbook
 * @param {String} handbookId
 */
export const selectRootChapters = createSelector(selectChaptersArray, chapters =>
  chapters.filter(c => !c.parentId).sort(sortBySortOrderAndTitle)
);

/**
 * Select the (sorted) sub chapters and sections of a parent
 * @param {String} parentId
 */
export const selectSubChaptersAndSections = parentId =>
  createSelector(selectChaptersArray, selectSectionsArray, (chapters, sections) =>
    // When we mix chapters and sections together in the tree,
    // we need to be able to tell which is which
    chapters
      .concat(sections)
      .filter(c => c.parentId === parentId)
      .sort(sortBySortOrderAndTitle)
  );

export const flatSortedChapterStructure = state => {
  /**
   * Recursively replaces chapters with their subsections in the correct sort order
   */
  const resolveChapters = chaptersAndSections => {
    const chapters = chaptersAndSections.filter(
      chapterOrSection => chapterOrSection.type === 'LOCAL_CHAPTER'
    );
    if (chapters.length > 0) {
      const chapterSections = chapters.reduce((acc, val) => {
        const sections = selectSubChaptersAndSections(val.id)(state);
        if (sections.length > 0) {
          acc[val.id] = sections;
        }
        return acc;
      }, {});

      const chaptersReplacedWithSubChaptersAndSections = chaptersAndSections.reduce((acc, val) => {
        if (val.type === 'LOCAL_CHAPTER') {
          const sections = chapterSections[val.id];
          if (sections !== undefined) {
            acc.push(...sections);
          }
        } else {
          acc.push(val);
        }
        return acc;
      }, []);

      return resolveChapters(chaptersReplacedWithSubChaptersAndSections);
    }
    return chaptersAndSections;
  };

  const rootChapters = selectRootChapters(state);

  const subChaptersAndSections = flatMap(rootChapters, chapter =>
    selectSubChaptersAndSections(chapter.id)(state)
  );

  return resolveChapters(subChaptersAndSections);
};

export const selectFlatSortedChapterStructure = () =>
  createSelector(flatSortedChapterStructure, structure => structure);

export const selectLinkCollections = createSelector(
  selectHandbookState,
  state => state.linkCollections
);
