import axios from 'axios';

const BASE_URL = '/handbooks/central';

export function fetchCentralHandbooks() {
  return axios.get(`${BASE_URL}`).then(res => res.data);
}

/**
 * Get the children of a handbook
 * @param {String} handbookId
 */
export function fetchContent(handbookId) {
  return axios.get(`${BASE_URL}/${handbookId}/content`).then(res => res.data);
}

/**
 * @param {String} handbookId
 */
export function fetchHandbook(handbookId) {
  return axios.get(`${BASE_URL}/${handbookId}`).then(res => res.data);
}

/**
 * @param {String} handbookId
 * @param {String} chapterId
 */
export function fetchChapter(handbookId, chapterId) {
  return axios.get(`${BASE_URL}/${handbookId}/chapter/${chapterId}/`).then(res => res.data);
}

/**
 * @param {String} handbookId
 * @param {String} chapterId
 */
export function fetchLatestChapterVersion(handbookId, chapterId) {
  return axios.get(`${BASE_URL}/${handbookId}/chapter/${chapterId}/latest/`).then(res => res.data);
}

/**
 * @param {String} handbookId
 * @param {String} sectionId
 */
export function fetchSection(handbookId, sectionId) {
  return axios.get(`${BASE_URL}/${handbookId}/section/${sectionId}/`).then(res => res.data);
}

/**
 * @param {String} handbookId
 * @param {String} sectionId
 */
export function fetchLatestSectionVersion(handbookId, sectionId) {
  return axios.get(`${BASE_URL}/${handbookId}/section/${sectionId}/latest/`).then(res => res.data);
}
