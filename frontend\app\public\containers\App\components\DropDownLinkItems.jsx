// @flow

import React from 'react';
import { Dropdown } from 'kf-bui';
import type { LinkType } from '../../../../types';
import DropDownMobile from './DropdownNav/DropDownMobile';
import ColorForcedDropdown from './DropdownNav/ColorForcedDropdown';
import LinkItems from './DropdownNav/LinkItems';

type LinkCollectionType = {
  // eslint-disable-next-line react/no-unused-prop-types
  id: string,
  title: string,
  links: LinkType[]
};

type Props = {
  linkCollections: [LinkCollectionType]
};

const DropDownLinkItemsDesktop = ({ links, title }: LinkCollectionType) => (
  <ColorForcedDropdown hiddenTouch hoverable right title={title}>
    <Dropdown.Menu>
      <LinkItems links={links} />
    </Dropdown.Menu>
  </ColorForcedDropdown>
);

const DropDownLinkItems = ({ linkCollections }: Props) => (
  <div>
    {linkCollections.map(linkCollection => (
      <DropDownLinkItemsDesktop key={linkCollection.id} {...linkCollection} />
    ))}
    {linkCollections && linkCollections.length > 0 && (
      <DropDownMobile linkCollections={linkCollections} />
    )}
  </div>
);

export default DropDownLinkItems;
