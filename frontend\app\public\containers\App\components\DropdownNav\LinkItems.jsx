// @flow

import React from 'react';
import { Icon, Dropdown } from 'kf-bui';
import type { LinkType } from '../../../../../types';

type Props = {
  links: LinkType[]
};

const LinkItems = ({ links }: Props): Object =>
  links
    .sort((a: LinkType, b: LinkType) => a.sortOrder - b.sortOrder)
    .map((link: LinkType) => (
      <Dropdown.Item style={{ color: 'black' }} key={link.id} target="_blank" href={link.url}>
        <span>{link.title}</span>
        <Icon style={{ marginLeft: 10 }} size="small" icon="external-link" />
      </Dropdown.Item>
    ));

export default LinkItems;
