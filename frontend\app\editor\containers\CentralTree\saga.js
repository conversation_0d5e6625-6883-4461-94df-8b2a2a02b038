import { all, call, put, select, takeLatest } from 'redux-saga/effects';
import { toast } from 'kf-toaster';

import * as api from './api';
import * as actions from './actions';
import { selectRootChapters } from './selectors';
import { selectLocale } from '../App/selectors';
import { translationMessages } from '../../../common/i18n';

export function* fetchHandbooks() {
  try {
    const handbooks = yield call(api.fetchCentralHandbooks);
    yield put(actions.fetchHandbooksSuccess(handbooks));
  } catch (error) {
    yield put(toast.error('Klarte ikke hente sentrale håndbøker'));
  }
}

export function* fetchHandbookContent({ payload: handbook }) {
  try {
    const hasFetched = Boolean(yield select(selectRootChapters));
    if (!hasFetched) {
      const handbookContent = yield call(api.fetchContent, handbook.id);

      yield put(
        actions.fetchHandbookContentSuccess({
          handbookId: handbook.id,
          ...handbookContent
        })
      );
    }
  } catch (error) {
    const locale = yield select(selectLocale) || 'nb';
    const i18identifier = 'editor.containers.CentralTree.selectHandbookFail';
    const errorText = translationMessages[locale][i18identifier];
    yield put(toast.error(errorText));
  }
}

export default function* rootSaga() {
  yield all([
    takeLatest(actions.fetchHandbooks, fetchHandbooks),
    takeLatest(actions.fetchHandbookContent, fetchHandbookContent)
  ]);
}
