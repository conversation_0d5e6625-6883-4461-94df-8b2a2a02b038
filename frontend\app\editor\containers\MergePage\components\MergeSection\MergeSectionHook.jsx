// @flow
import React, { useState, useEffect, Fragment } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { toast } from 'kf-toaster';
import { Link, withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Columns, Column, Button, Input, Field, Icon, Title, Subtitle, Addons } from 'kf-bui';
import moment from 'moment';
import Wysiwyg from '../../../../components/Wysiwyg';
import Radio from './RightRadio';

import mergeSection from './sectionMerger';
import injectT from '../../../../../common/i18n';
import { saveLocalSection, fetchSection } from '../../../EditorPage/actions';
import { fetchLatestSectionVersion as fetchCentralSection } from '../../../CentralTree/api';
import { selectSectionEntities } from '../../../EditorPage/selectors';

const MergeSection = ({ t, section, errorToast, saveLocalSectionFun, match, fetchSectionFun }) => {
  if (!section) {
    return <div />;
  }

  const { sectionId } = match.params;

  const [centralSection, setCentralSection] = useState(undefined);
  const [titleRadio, setTitleRadio] = useState(section.localTitleChange ? undefined : 'central');
  const [textRadio, setTextRadio] = useState(section.localTextChange ? undefined : 'central');
  const [newTitle, setNewTitle] = useState<string>(section.title);
  const [newWysiwyg, setNewWysiwyg] = useState<Wysiwyg>({});
  const [isSaving, setIsSaving] = useState<boolean>(false);

  useEffect(() => {
    fetchCentralSection(section.importedHandbookId, section.importedHandbookSectionId)
      .then(newCentralSection => setCentralSection(newCentralSection))
      .catch(() => errorToast(t('fetchCentralElementFail')));
  }, [section]);

  useEffect(() => {
    if (section.text === undefined) {
      fetchSectionFun(sectionId);
    }
  }, []);

  if (!centralSection) {
    return <div />;
  }

  const onTitleRadioChange = e => {
    const { value } = e.target;

    if (value !== titleRadio) {
      setTitleRadio(value);
      setNewTitle(section.title);
    }
  };
  const onTextRadioChange = e => {
    const { value } = e.target;

    if (value !== textRadio) {
      setTextRadio(value);
      newWysiwyg.setValue(section.text);
    }
  };
  const onSave = () => {
    setIsSaving(true);

    const centralChange = titleRadio === 'central' ? 'KF' : 'local';
    const centralTextChange = textRadio === 'central' ? 'KF' : 'local';
    const title = titleRadio === 'central' ? centralSection.title : newTitle;

    if (textRadio === 'central') {
      const merged = mergeSection(section, centralSection, title, centralSection.text);
      return saveLocalSectionFun({
        section: merged,
        centralChange,
        centralTextChange
      });
    }

    return newWysiwyg
      .uploadImagesAndGetContent()
      .then(content => mergeSection(section, centralSection, title, content))
      .then(merged =>
        saveLocalSectionFun({
          section: merged,
          centralChange,
          centralTextChange
        })
      );
  };

  return (
    <div>
      <Title>{t('lead.title')}</Title>
      <Subtitle>{t('lead.text')}</Subtitle>
      <hr />
      <Columns>
        <Column>
          <Title size="5">{t('central.header')}</Title>
          <Field />
          <Field>
            <label htmlFor="centralTitle" style={{ fontWeight: 'bold', fontSize: 'large' }}>
              Tittel
            </label>

            {section.pendingTitleChange && (
              <Fragment>
                <i className="fa fa-exclamation-triangle pendingChange" aria-hidden="true" />
                <Radio
                  name="titleRadio"
                  value="central"
                  checked={titleRadio === 'central'}
                  onChange={onTitleRadioChange}
                >
                  <span className="conflict-text">{`Bruk sentral tittel.  (${t(
                    'lastModified'
                  )} ${moment(centralSection.updatedDate).format('DD.MM.YYYY')})`}</span>
                </Radio>
              </Fragment>
            )}
            <Addons>
              <Input
                id="centralTitle"
                placeholder="Tittel"
                value={centralSection.title}
                aria-label={t('central.text')}
                readOnly
                expanded
              />
              <Button
                aria-label="Bruk sentral tittel"
                disabled={newTitle === centralSection.title || titleRadio !== 'local'}
                onClick={() => setNewTitle(centralSection.title)}
                title="Bruk sentral tittel"
              >
                <Icon icon="copy" size="small" />
              </Button>
            </Addons>
          </Field>
          <label htmlFor="central" style={{ fontWeight: 'bold', fontSize: 'large' }}>
            Avsnittstekst
          </label>
          {section.pendingTextChange && (
            <Fragment>
              <i className="fa fa-exclamation-triangle pendingChange" aria-hidden="true" />
              <Radio
                name="textRadio"
                value="central"
                checked={textRadio === 'central'}
                onChange={onTextRadioChange}
              >
                <span className="conflict-text">{`Bruk sentral avsnittstekst.  (${t(
                  'lastModified'
                )} ${moment(centralSection.textUpdatedDate).format('DD.MM.YYYY')})`}</span>
              </Radio>
            </Fragment>
          )}
          <Wysiwyg value={centralSection.text} disabled id="central" />
        </Column>
        <Column>
          <Title size="5">{t('local.header')}</Title>

          <Field>
            <label htmlFor="localTitle" style={{ fontWeight: 'bold', fontSize: 'large' }}>
              Tittel
            </label>
            {section.pendingTitleChange && (
              <Radio
                name="titleRadio"
                value="local"
                checked={titleRadio === 'local'}
                onChange={onTitleRadioChange}
              >
                <span className="conflict-text">{`Bruk lokal tittel.  (${t(
                  'lastModified'
                )} ${moment(section.updatedDate).format('DD.MM.YYYY')})`}</span>
              </Radio>
            )}
            <Addons style={{ margin: 0 }}>
              <Input
                id="localTitle"
                placeholder="Tittel"
                onChange={e => setNewTitle(e.target.value)}
                value={newTitle}
                aria-label={t('local.text')}
                expanded
                disabled={titleRadio !== 'local' || !titleRadio}
              />
              <Button
                aria-label="Bruk original tittel"
                disabled={newTitle === section.title}
                onClick={() => setNewTitle(section.title)}
                title="Bruk original tittel"
              >
                <Icon icon="undo" size="small" />
              </Button>
            </Addons>
          </Field>

          <label htmlFor="localText" style={{ fontWeight: 'bold', fontSize: 'large' }}>
            Avsnittstekst
          </label>
          {section.pendingTextChange && (
            <Radio
              name="textRadio"
              value="local"
              checked={textRadio === 'local'}
              onChange={onTextRadioChange}
            >
              <span className="conflict-text">{`${t('local.radio')} for avsnittstekst.  (${t(
                'lastModified'
              )} ${moment(section.textUpdatedDate).format('DD.MM.YYYY')})`}</span>
            </Radio>
          )}
          <Wysiwyg
            value={section.text}
            id="localText"
            ref={c => {
              setNewWysiwyg(c);
            }}
            disabled={textRadio !== 'local' || !textRadio}
          />
        </Column>
      </Columns>
      <hr />
      <Columns responsive="mobile">
        <Column>
          <Button as={Link} to={`/editor/${section.handbookId}/section/${section.id}`}>
            Avbryt
          </Button>
        </Column>
        <Column narrow>
          <Button
            disabled={
              !newTitle ||
              isSaving ||
              (section.pendingTitleChange && !titleRadio) ||
              (section.pendingTextChange && !textRadio)
            }
            loading={isSaving}
            onClick={onSave}
            color="primary"
          >
            {t('saveButton')}
          </Button>
        </Column>
      </Columns>
    </div>
  );
};

export default compose(
  injectT('editor.components.MergeSection'),
  withRouter,
  connect(
    (state, ownProps) => ({
      section: selectSectionEntities(state)[ownProps.match.params.sectionId]
    }),
    {
      saveLocalSectionFun: saveLocalSection,
      fetchSectionFun: fetchSection,
      errorToast: toast.error
    }
  )
)(MergeSection);

MergeSection.propTypes = {
  t: PropTypes.func.isRequired,
  section: PropTypes.object.isRequired,
  saveLocalSectionFun: PropTypes.func.isRequired,
  fetchSectionFun: PropTypes.func.isRequired,
  errorToast: PropTypes.func.isRequired,
  match: PropTypes.shape({
    params: PropTypes.shape({
      sectionId: PropTypes.string
    }).isRequired
  }).isRequired
};
