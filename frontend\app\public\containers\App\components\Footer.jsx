import React from 'react';
import PropTypes from 'prop-types';
import { Level, Container, Footer as RulmaFooter } from 'kf-bui';

// application={`${t('application')} ${applicationVersion}`}
// user={`${t('loggedIn')}: ${userEmail}`}
// TODO: i18n

const Footer = ({ application, showOptOutModal }) => (
  <RulmaFooter>
    <Container>
      <Level>
        <Level.Item textCentered flexible style={{ flexDirection: 'column' }}>
          <a href="http://www.kf.no/" target="_blank" rel="noopener noreferrer">
            KF
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Tilgjengelighet_KF_Handbok.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Tilgjengelighetserklæring
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_Håndbøker.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Informasjonskapsler
          </a>
          <button type="button" className="cookie-settings-btn" onClick={showOptOutModal}>
            Innstillinger for informasjonskapsler
          </button>
        </Level.Item>
        <Level.Item textCentered>{application}</Level.Item>
      </Level>
    </Container>
  </RulmaFooter>
);

Footer.propTypes = {
  application: PropTypes.string.isRequired,
  showOptOutModal: PropTypes.func.isRequired
};

export default Footer;
