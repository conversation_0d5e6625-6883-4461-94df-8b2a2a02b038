import PropTypes from 'prop-types';

export default PropTypes.shape({
  appVersion: PropTypes.string.isRequired,
  isKfAdmin: PropTypes.bool.isRequired,
  user: PropTypes.shape({
    email: PropTypes.string.isRequired
  }).isRequired,
  userOrgsWithAccess: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired
    })
  ).isRequired,
  organization: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired
  }),
  ssoLogOutUrl: PropTypes.string
});
