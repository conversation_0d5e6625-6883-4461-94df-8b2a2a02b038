import { createSelector } from 'reselect';
import { selectHandbookState } from '../HandbookPage/selectors';

export const selectGlobalState = state => state.global;

export const selectOrganization = state => selectHandbookState(state).organization;

export const selectLogoUrl = state => {
  const org = selectOrganization(state);
  const startUrl = selectGlobalState(state).logoUrlStart;
  return org ? `${startUrl}${org.id}` : null;
};

export const selectBannerUrl = state => {
  const org = selectOrganization(state);
  const startUrl = selectGlobalState(state).bannerUrlStart;
  return org ? `${startUrl}${org.id}` : null;
};

export const selectLocale = createSelector(
  selectOrganization,
  org => (org && org.language) || 'nb'
);
