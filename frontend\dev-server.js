const path = require('path');
const express = require('express');
const request = require('request');

const config = require('./webpack.config');

const port = 3000;
const host = 'localhost';

const app = express();
const addMiddlewares = (app, webpackConfig) => {
  const webpack = require('webpack');
  const webpackDevMiddleware = require('webpack-dev-middleware');
  const compiler = webpack(webpackConfig);
  const middleware = webpackDevMiddleware(compiler, {
    publicPath: webpackConfig.output.publicPath,
    stats: {
      colors: true
    }
  });

  app.use(middleware);
  app.use(require('webpack-hot-middleware')(compiler));

  // Proxy the editor requests to the backend
  app.use('/handboker', (req, res) => {
    const url = `http://localhost:5600/handboker${req.url}`;
    req.pipe(request(url)).pipe(res);
  });

  // Since webpackDevMiddleware uses memory-fs internally to store build
  // artifacts, we use it instead
  const fs = middleware.fileSystem;
  app.get('*', (req, res) => {
    const folder = req.url.startsWith('/public') ? '/public' : '/editor';
    fs.readFile(
      path.join(compiler.outputPath, `${folder}/index.html`),
      (err, file) => {
        if (err) {
          console.error(err);
          res.sendStatus(404);
        } else {
          res.send(file.toString());
        }
      }
    );
  });
};

const inline = process.argv.includes('inline');
const analyzer = process.argv.includes('analyzer');
// Important that this comes after the proxy
addMiddlewares(app, config({ inline, analyzer }));

app.listen(port, err => {
  if (err) {
    return console.error(err.message);
  }

  console.log(`Server started! Listening at http://${host}:${port}`);
});
