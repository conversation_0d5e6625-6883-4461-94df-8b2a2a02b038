// @flow
import React from 'react';
import { connect } from 'react-redux';

import { Icon, Tree } from 'kf-bui';
import { setSelectedCentralItem } from '../../App/actions';
import CentralChapterNode from './CentralChapterNode';
import { makeSelectHandbookChildren } from '../selectors';

import type {
  CentralChapterType,
  CentralHandbookType,
  CentralTreeNodeType
} from '../../../../types';

type Props = {
  centralHandbook: CentralHandbookType,
  chapters: CentralChapterType[],
  setSelectedItem: CentralTreeNodeType => void,
  params: { itemId: string },
  moving: ?boolean
};

const CentralHandbookNode = ({
  centralHandbook,
  chapters,
  setSelectedItem,
  params,
  moving
}: Props) => {
  const sortedChapters = chapters.sort((c1, c2) => c1.sortOrder - c2.sortOrder);

  const items = sortedChapters.map(chapter => (
    <CentralChapterNode
      key={chapter.id}
      chapter={chapter}
      setSelectedItem={setSelectedItem}
      moving={moving}
      params={params}
    />
  ));

  if (moving) {
    return (
      <Tree.Item
        id={centralHandbook.id}
        key={centralHandbook.id}
        items={items}
        onClick={() => setSelectedItem(centralHandbook)}
      >
        <Icon icon="book" size="small" /> {centralHandbook.title}
      </Tree.Item>
    );
  }
  return (
    <Tree.ItemLink
      key={centralHandbook.id}
      exact
      items={items}
      to={`/central-editor/${centralHandbook.id}`}
      id={centralHandbook.id}
    >
      <Icon icon="book" size="small" /> {centralHandbook.title}
    </Tree.ItemLink>
  );
};

const mapStateToProps = (state, props) => {
  const selectChildren = makeSelectHandbookChildren(props.centralHandbook.id);
  return {
    chapters: selectChildren(state)
  };
};

export default connect(mapStateToProps, {
  setSelectedItem: setSelectedCentralItem
})(CentralHandbookNode);
