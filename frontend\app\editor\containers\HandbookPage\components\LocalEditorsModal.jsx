// @flow
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Label,
  Select,
  Help,
  Field,
  Checkbox,
  Tag,
  Icon,
  FormattedDate,
  Columns,
  Column
} from 'kf-bui';

import { FormattedMessage } from 'react-intl';
import injectT from '../../../../common/i18n';

type LocalEditor = {
  id?: string,
  handbookId: string,
  rightsHolder: string,
  addedDate: Date,
  addedBy: string
};

type OrgEditor = {
  email: string,
  fullName?: string,
  organizations: string[],
  language?: string,
  localUser: Boolean,
  localAdmin: Boolean,
  globalAdmin: Boolean
};

type Props = {
  handbookId: string,
  localEditorsForOrg: Set<OrgEditor>,
  localEditors: Set<LocalEditor>,
  deleteLocalEditorFun: Function,
  saveLocalEditorFun: Function,
  onHide: () => void,
  userEmail: string,
  t: Function
};

type EmailName = {
  email: string,
  displayName: string
};

const LocalEditorsModal = ({
  handbookId,
  localEditorsForOrg,
  localEditors,
  deleteLocalEditorFun,
  saveLocalEditorFun,
  onHide,
  userEmail,
  t
}: Props) => {
  const [userHasAccess, setUserHasAccess] = useState<boolean>(true);
  const [newEditors, setNewEditors] = useState<Set<LocalEditor>>(new Set());

  useEffect(() => {
    setNewEditors(new Set(localEditors));
  }, [localEditors]);

  const handleSubmit = event => {
    event.preventDefault();
    const toSave: LocalEditor[] = [...newEditors].filter(
      newEditor =>
        ![...localEditors].some(
          curEditor => curEditor.rightsHolder.toLowerCase() === newEditor.rightsHolder.toLowerCase()
        )
    );
    const toDelete: LocalEditor[] = [...localEditors].filter(
      curEditor =>
        ![...newEditors].some(
          newEditor => newEditor.rightsHolder.toLowerCase() === curEditor.rightsHolder.toLowerCase()
        )
    );
    toSave.forEach(editor => {
      saveLocalEditorFun(editor);
    });
    toDelete.forEach(editor => {
      deleteLocalEditorFun(editor);
    });
    onHide();
  };

  const onCancel = () => {
    setNewEditors(new Set(localEditors));
    onHide();
  };

  const filteredEditorPool = (): EmailName[] => {
    const editors = [...localEditorsForOrg]
      .filter(
        orgEditor =>
          ![...newEditors].some(
            newEditor => newEditor.rightsHolder.toLowerCase() === orgEditor.email.toLowerCase()
          )
      )
      .sort((a, b) => ((a.fullName ? a.fullName : '') > (b.fullName ? b.fullName : '') ? 1 : -1));
    return editors.map(editor => ({
      email: editor.email,
      displayName: editor.fullName ? `${editor.fullName} - ${editor.email}` : editor.email
    }));
  };

  return (
    <Modal onClose={onCancel}>
      <form onSubmit={handleSubmit}>
        <Modal.Header onClose={onCancel}>
          <Modal.Title>
            <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.title" />
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Field>
            <Label htmlFor="grantAccess">{t('accessTitle')}</Label>
            <Select
              id="grantAccess"
              aria-label="Gi tilgang"
              name="grantAccess"
              options={filteredEditorPool()}
              placeholder="Velg redaktør..."
              labelKey="displayName"
              maxMenuHeight="8em"
              onChange={e => {
                if (e.email === userEmail) setUserHasAccess(true);
                const updated = new Set(newEditors);
                updated.add({
                  handbookId,
                  rightsHolder: e.email,
                  addedBy: userEmail,
                  addedDate: new Date()
                });
                setNewEditors(updated);
              }}
            />
            <Help>{t('accessHelp')}</Help>
          </Field>
          <Label htmlFor="tilganger">
            {`${t('accessListTitle')} `}
            {!userHasAccess && (
              <Tag color="warning">
                <Icon icon="exclamation" size="small" />{' '}
                <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.selfDeleteWarning" />
              </Tag>
            )}
          </Label>
          {newEditors.size > 0 ? (
            [...newEditors].map(el => (
              <Field key={el.rightsHolder}>
                <Columns size gapless>
                  <Column>
                    <Checkbox
                      checked
                      name="hasAccess"
                      onChange={() => {
                        if (el.rightsHolder.toLowerCase() === userEmail.toLowerCase())
                          setUserHasAccess(false);
                        const updated = new Set(newEditors);
                        updated.delete(el);
                        setNewEditors(updated);
                      }}
                    >
                      {` ${el.rightsHolder}`}
                    </Checkbox>
                  </Column>
                  <Column style={{ fontSize: '0.8em' }}>
                    {`${el.addedBy} - `}
                    <FormattedDate value={el.addedDate} format="YYYY.MM.DD-HH:mm" />
                  </Column>
                </Columns>
              </Field>
            ))
          ) : (
            <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.noEditorsWarning" />
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={onCancel}>
            <FormattedMessage id="generic.cancel" />
          </Button>
          <Button onClick={handleSubmit} type="submit" color="success">
            <FormattedMessage id="generic.save" />
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default injectT('editor.containers.HandbookPage.components.LocalEditorsModal')(
  LocalEditorsModal
);
