// @flow
import React, { Fragment, useState } from 'react';
import { Modal, Field, Button, Icon, DatePicker } from 'kf-bui';

import { FormattedMessage } from 'react-intl';
import type { HandbookType } from '../../../../types';

type Props = {
  handbook: HandbookType,
  onHide: () => void
};

const ExportModal = ({ handbook, onHide }: Props) => {
  const [selectedDate, setSelectedDate] = useState<?Date>(undefined);

  const makeUrl = (fileType: string, id: string, date: Date) => {
    const BASE_URL = '/handboker/handbooks/download/local';
    return `${BASE_URL}/${fileType}/${id}/${date.toJSON()}`;
  };

  return (
    <Modal onClose={onHide}>
      <Modal.Header>
        <Modal.Title>
          <FormattedMessage id="editor.containers.HandbookPage.components.ExportModal.title" />
          {handbook.title}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div style={{ height: '10em' }}>
          <Field>
            {/* <Label htmlFor="handbookDate"><FormattedMessage
                  id="editor.containers.HandbookPage.components.ExportModal.dateTitle"/></Label> */}
            <FormattedMessage id="editor.containers.HandbookPage.components.ExportModal.dateTitle" />
            <DatePicker
              id="handbookDate"
              label="Velg dato for versjonen du vil eksportere"
              onChange={e => {
                setSelectedDate(e);
              }}
            />
          </Field>
          {selectedDate && (
            <Fragment>
              <p style={{ marginBottom: '5px' }}>
                <FormattedMessage id="editor.containers.HandbookPage.components.ExportModal.exportQuestion" />
              </p>
              <Button control href={makeUrl('pdf', handbook.id, selectedDate)}>
                <Icon icon="file-pdf-o" size="small" />
                <span>
                  <FormattedMessage id="editor.containers.HandbookPage.components.ExportModal.exportPDF" />
                </span>
              </Button>
            </Fragment>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onHide}>
          <FormattedMessage id="editor.containers.HandbookPage.components.ExportModal.close" />
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ExportModal;
