import React from 'react';
import PropTypes from 'prop-types';
import { Switch, Route } from 'react-router-dom';
import { connect } from 'react-redux';
import { FormattedMessage } from 'react-intl';
import { Button, Columns, Column, Icon, Section, Container, Hero, Title, ImageHero } from 'kf-bui';

import LocalTree from '../LocalTree';
import ClickableTree from '../LocalTree/components/ClickableTree';
import NoSelectionScreen from './components/NoSelectionScreen';
import HandbookPage from '../HandbookPage';
import { HandbookShape, SessionShape } from '../../shapes';
import { selectSession, selectBannerUrl } from '../App/selectors';
import CreateOrUpdateHandbook from '../CreateOrUpdateHandbook';
import { selectHandbookEntities } from './selectors';
import { fetchHandbooks as fetchCentralHandbooks } from '../CentralTree/actions';

class EditorPage extends React.Component {
  state = {
    showModal: false
  };

  componentDidMount() {
    const { fetchCentralHandbooksFun } = this.props;
    fetchCentralHandbooksFun();
  }

  componentWillReceiveProps(nextProps) {
    const { handbooks } = this.props;
    const { showModal } = this.state;
    if (showModal && nextProps.handbooks !== handbooks) {
      this.setState({ showModal: false });
    }
  }

  toggleModal = () => this.setState(state => ({ showModal: !state.showModal }));

  render() {
    const { session, match, bannerUrl } = this.props;
    const { showModal } = this.state;

    return (
      <div>
        <ImageHero color="primary" imageUrl={bannerUrl}>
          <Hero.Body>
            <Container>
              <Columns>
                <Column>
                  <Title>{session.organization.name}</Title>
                </Column>
                <Column narrow>
                  <Button onClick={this.toggleModal} inverted color="primary">
                    <Icon icon="plus" size="small" />
                    <FormattedMessage id="editor.components.NoSelection.createButton" />
                  </Button>
                  {showModal && <CreateOrUpdateHandbook onHide={this.toggleModal} />}
                </Column>
              </Columns>
            </Container>
          </Hero.Body>
        </ImageHero>
        <Section>
          <Container>
            <Columns>
              <Column size="1/3">
                <Switch>
                  <Route path={`${match.path}/:handbook/*/:id/move`} component={ClickableTree} />
                  <Route component={LocalTree} />
                </Switch>
              </Column>
              <Column size="2/3">
                <Switch>
                  <Route path={match.path} exact component={NoSelectionScreen} />
                  <Route path={`${match.path}/:handbookId`} component={HandbookPage} />
                </Switch>
              </Column>
            </Columns>
          </Container>
        </Section>
      </div>
    );
  }
}

EditorPage.propTypes = {
  session: SessionShape.isRequired,
  handbooks: PropTypes.objectOf(HandbookShape).isRequired,
  fetchCentralHandbooksFun: PropTypes.func.isRequired,
  match: PropTypes.shape({
    url: PropTypes.string.isRequired,
    path: PropTypes.string.isRequired
  }).isRequired,
  bannerUrl: PropTypes.string
};

EditorPage.defaultProps = {
  bannerUrl: undefined
};

export default connect(
  state => ({
    session: selectSession(state),
    handbooks: selectHandbookEntities(state),
    bannerUrl: selectBannerUrl(state)
  }),
  {
    fetchCentralHandbooksFun: fetchCentralHandbooks
  }
)(EditorPage);
