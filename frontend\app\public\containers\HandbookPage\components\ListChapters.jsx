import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { Menu } from 'kf-bui';
import { selectRootChapters } from '../selectors';
import { ChapterShape } from '../../../shapes';

const ListChapters = ({ chapters, match }) => (
  <Menu>
    <Menu.List>
      {chapters.map(c => (
        <Menu.Item key={c.id} as={Link} to={`${match.url}/chapter/${c.id}`}>
          {c.title}
        </Menu.Item>
      ))}
    </Menu.List>
  </Menu>
);

ListChapters.propTypes = {
  chapters: PropTypes.arrayOf(ChapterShape).isRequired,
  match: PropTypes.shape({
    url: PropTypes.string.isRequired
  }).isRequired
};

export default connect(state => ({ chapters: selectRootChapters(state) }))(ListChapters);
