import { createAction } from 'redux-actions';

// This does not include section text
export const fetchLocalHandbooks = createAction('EditorContainer/FETCH_LOCAL_HANDBOOKS_REQUEST');
export const fetchLocalHandbooksSuccess = createAction(
  'EditorContainer/FETCH_LOCAL_HANDBOOKS_SUCCESS'
);

export const fetchSubscriptions = createAction('EditorContainer/FETCH_SUBSCRIPTIONS_REQUEST');
export const fetchSubscriptionsSuccess = createAction(
  'EditorContainer/FETCH_SUBSCRIPTIONS_SUCCESS'
);

export const fetchLocalEditorsForOrganization = createAction(
  'EditorContainer/FETCH_LOCAL_EDITORS_FOR_ORGANIZATION_REQUEST'
);
export const fetchLocalEditorsForOrganizationSuccess = createAction(
  'EditorContainer/FETCH_LOCAL_EDITORS_FOR_ORGANIZATION_SUCCESS'
);

export const fetchLocalEditors = createAction('EditorContainer/FETCH_LOCAL_EDITORS_REQUEST');
export const fetchLocalEditorsSuccess = createAction('EditorContainer/FETCH_LOCAL_EDITORS_SUCCESS');
export const saveLocalEditor = createAction('EditorContainer/SAVE_LOCAL_EDITOR_REQUEST');
export const saveLocalEditorSuccess = createAction(
  'EditorContainer/SAVE_LOCAL_EDITOR_SUCCESS_SUCCESS'
);
export const deleteLocalEditor = createAction('EditorContainer/DELETE_LOCAL_EDITOR_REQUEST');
export const deleteLocalEditorSuccess = createAction('EditorContainer/DELETE_LOCAL_EDITOR_SUCCESS');

// This fetches the full section, including it's text
export const fetchSection = createAction('EditorContainer/FETCH_LOCAL_SECTION_REQUEST');
export const fetchSectionSuccess = createAction('EditorContainer/FETCH_LOCAL_SECTION_SUCCESS');
export const fetchChapter = createAction('EditorContainer/FETCH_LOCAL_CHAPTER_REQUEST');
export const fetchChapterSuccess = createAction('EditorContainer/FETCH_LOCAL_CHAPTER_SUCCESS');
export const fetchHandbook = createAction('EditorContainer/FETCH_LOCAL_HANDBOOK_REQUEST');
export const fetchHandbookSuccess = createAction('EditorContainer/FETCH_LOCAL_HANDBOOK_SUCCESS');

export const saveLocalHandbook = createAction('EditorContainer/SAVE_LOCAL_HANDBOOK_REQUEST');
export const saveLocalHandbookSuccess = createAction('EditorContainer/SAVE_LOCAL_HANDBOOK_SUCCESS');
export const deleteLocalHandbook = createAction('EditorContainer/DELETE_LOCAL_HANDBOOK_REQUEST');
export const deleteLocalHandbookSuccess = createAction(
  'EditorContainer/DELETE_LOCAL_HANDBOOK_SUCCESS'
);

export const subscribe = createAction('EditorContainer/SUBSCRIBE_REQUEST', (handbookId, bool) => ({
  handbookId,
  bool
}));
export const subscribeSuccess = createAction(
  'EditorContainer/SUBSCRIBE_SUCCESS',
  (handbookId, bool) => ({
    handbookId,
    bool
  })
);

export const saveLocalChapter = createAction('EditorContainer/SAVE_LOCAL_CHAPTER_REQUEST');
export const saveLocalChapterSuccess = createAction('EditorContainer/SAVE_LOCAL_CHAPTER_SUCCESS');
export const deleteLocalChapter = createAction('EditorContainer/DELETE_LOCAL_CHAPTER_REQUEST');
export const deleteLocalChapterSuccess = createAction(
  'EditorContainer/DELETE_LOCAL_CHAPTER_SUCCESS'
);

export const saveLocalSection = createAction('EditorContainer/SAVE_LOCAL_SECTION_REQUEST');
export const saveLocalSectionSuccess = createAction('EditorContainer/SAVE_LOCAL_SECTION_SUCCESS');
export const deleteLocalSection = createAction('EditorContainer/DELETE_LOCAL_SECTION_REQUEST');
export const deleteLocalSectionSuccess = createAction(
  'EditorContainer/DELETE_LOCAL_SECTION_SUCCESS'
);

export const sortItems = createAction('EditorContainer/SORT_LOCAL_ITEMS_REQUEST');
export const sortItemsSuccess = createAction('EditorContainer/SORT_LOCAL_ITEMS_SUCCESS');
