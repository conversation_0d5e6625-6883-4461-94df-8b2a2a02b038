// @flow

import React from 'react';
import { connect } from 'react-redux';
import { Tree as UiTree } from 'kf-bui';

import ChapterItem from './ChapterItem';
import { selectHandbook, selectRootChapters } from '../HandbookPage/selectors';

import type { ChapterType } from '../../../types';

type Props = {
  chapters: ChapterType[],
  externalOrgId: string,
  activeSections: string[],
  expandedChapters: string[],
  onCollapseClick: (item: ChapterType) => void,
  // eslint-disable-next-line react/require-default-props
  onClick?: () => void
};

const Tree = ({
  chapters,
  externalOrgId,
  onClick,
  activeSections,
  expandedChapters,
  onCollapseClick
}: Props) => {
  const items = chapters.map(chapter => (
    <ChapterItem
      onClick={onClick}
      chapter={chapter}
      externalOrgId={externalOrgId}
      key={chapter.id}
      activeSections={activeSections}
      expandedChapters={expandedChapters}
      onCollapseClick={onCollapseClick}
    />
  ));

  return <UiTree>{items}</UiTree>;
};

export default connect(state => ({
  chapters: selectRootChapters(state),
  handbook: selectHandbook(state)
}))(Tree);
