/**
 * Combine all reducers in this file and export the combined reducers.
 * If we were to do this in store.js, reducers wouldn't be hot reloadable.
 */

import { combineReducers } from 'redux';
import { reducer as formReducer } from 'redux-form';
import { reducer as toasterReducer } from 'kf-toaster';
import global from './containers/App/reducer';
import handbooks from './containers/EditorPage/reducer';
import centralAccess from './containers/CentralHandbooksPage/reducer';
import centralHandbooks from './containers/CentralTree/reducer';
import centralEditorHandbooks from './containers/CentralHandbookEditorPage/reducer';
import readingLinks from '../common/components/ReadingLink/reducer';
import ui from './containers/LocalTree/reducer';

/**
 * Creates the main reducer with the asynchronously loaded ones
 */
export default function createReducer(asyncReducers) {
  return combineReducers({
    form: formReducer,
    toaster: toasterReducer,
    global,
    handbooks,
    centralAccess,
    centralHandbooks,
    centralEditorHandbooks,
    readingLinks,
    ui,
    ...asyncReducers
  });
}
