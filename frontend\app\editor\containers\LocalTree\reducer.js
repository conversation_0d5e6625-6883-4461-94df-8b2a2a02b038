import { handleActions } from 'redux-actions';
import { selectElement } from './actions';
import { setOrganizationSuccess } from '../App/actions';

/* Note: Keeping this in Tree for now.
If we need to handle other ui stuff than tree in the store this should probably be moved */

export const initialState = {
  selected: null
};

export default handleActions(
  {
    [selectElement]: (state, action) => ({
      ...state,
      selected: action.payload
    }),

    [setOrganizationSuccess]: () => initialState
  },
  initialState
);
