import { createAction } from 'redux-actions';

export const retrieveReadingLink = createAction('RETRIEVE_READING_LINK_REQUEST');

export const retrieveReadingLinkSuccess = createAction('RETRIEVE_READING_LINK_SUCCESS');

export const persistReadingLink = createAction('PERSIST_READING_LINK_REQUEST');

export const persistReadingLinkSuccess = createAction('PERSIST_READING_LINK_SUCCESS');

export const deleteLink = createAction('DELETE_LINK');

export const deleteLinkSuccess = createAction('DELETE_LINK_SUCCESS');
