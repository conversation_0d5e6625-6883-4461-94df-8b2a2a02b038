import React from 'react';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import moment from 'moment';

import { Card, Icon, Section, Container, Title } from 'kf-bui';

import injectT from '../../../common/i18n';
import { selectPending } from '../EditorPage/selectors';

const PendingPage = ({ pending, t }) => {
  document.title = `${t('title')}  - KF <PERSON>`;
  return (
    <Section>
      <Container>
        <Title textCentered={pending.length === 0}>
          {pending.length > 0 ? (
            <FormattedMessage id="editor.containers.PendingPage.header" />
          ) : (
            <span>
              {' '}
              <FormattedMessage id="editor.containers.PendingPage.noChanges" />{' '}
              <Icon icon="smile-o" size="small" />
            </span>
          )}
        </Title>
        {pending.length && <Pending pending={pending} t={t} />}
      </Container>
    </Section>
  );
};

PendingPage.propTypes = {
  pending: PropTypes.array.isRequired,
  t: PropTypes.func.isRequired
};

const mapStateToProps = state => ({
  pending: selectPending(state)
});

export default compose(
  injectT('editor.containers.PendingPage'),
  connect(mapStateToProps)
)(PendingPage);

const Pending = ({ pending, t }) => {
  const handbooks = pending.map(handbook => (
    <Card key={handbook.id}>
      <Card.Content>
        <Icon icon="book" size="small" />{' '}
        {handbook.pendingChange ? (
          <Link to={`/merge/handbook/${handbook.id}/`}>{handbook.title}</Link>
        ) : (
          <span>{handbook.title}</span>
        )}
      </Card.Content>
      {handbook.chapters.length > 0 && (
        <Card.Content>
          {handbook.chapters.map(c => (
            <div key={c.id}>
              <Icon icon="bookmark-o" size="small" />{' '}
              <Link
                to={
                  c.pendingChange
                    ? `/merge/chapter/${c.id}/`
                    : `/editor/${c.handbookId}/chapter/${c.id}/`
                }
              >
                {c.title}
              </Link>
              <div className="pending-timestamp">
                {`${t('centralChange')} ${moment(c.pendingChangeUpdatedDate).format('DD.MM.YYYY')}`}
              </div>
            </div>
          ))}
        </Card.Content>
      )}
      {handbook.sections.length > 0 && (
        <Card.Content>
          {handbook.sections.map(s => (
            <div key={s.id}>
              <Icon icon="file-text-o" size="small" />{' '}
              <Link
                to={
                  s.pendingChange
                    ? `/merge/section/${s.id}/`
                    : `/editor/${s.handbookId}/section/${s.id}/`
                }
              >
                {s.title}
              </Link>
              <div className="pending-timestamp">
                {`${t('centralChange')} ${moment(s.pendingChangeUpdatedDate).format('DD.MM.YYYY')}`}
              </div>
            </div>
          ))}
        </Card.Content>
      )}
    </Card>
  ));
  return <div>{handbooks}</div>;
};

Pending.propTypes = {
  pending: PropTypes.array.isRequired,
  t: PropTypes.func.isRequired
};
