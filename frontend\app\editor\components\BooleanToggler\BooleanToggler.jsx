import React from 'react';
import PropTypes from 'prop-types';

export default class BooleanToggler extends React.Component {
  state = {
    show: false
  };

  handleToggle = () => this.setState(state => ({ show: !state.show }));

  render() {
    const { children } = this.props;
    const { show } = this.state;

    return children(this.handleToggle, show);
  }
}

BooleanToggler.propTypes = {
  children: PropTypes.func.isRequired
};
