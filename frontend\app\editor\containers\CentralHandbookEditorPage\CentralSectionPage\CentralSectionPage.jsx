// @flow
import React from 'react';
import { Route, Switch } from 'react-router-dom';
import MoveSection from '../MoveCentralChapterOrSection';
import EditCentralSection from './EditCentralSection';
import CentralSectionScreen from './CentralSectionScreen';

type Props = {
  match: {
    path: string
  }
};

const CentralSectionPage = ({ match }: Props) => (
  <Switch>
    <Route path={`${match.path}/edit/:sectionId`} component={EditCentralSection} />
    <Route path={`${match.path}/add-new/`} component={EditCentralSection} />
    <Route path={`${match.path}/move/:sectionId`} component={MoveSection} />
    <Route path={`${match.path}/:sectionId`} component={CentralSectionScreen} />
  </Switch>
);

export default CentralSectionPage;
