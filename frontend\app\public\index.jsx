// Needed for ES2015 generator support
/* eslint import/first: 0 */

import React from 'react';
import { render } from 'react-dom';
import { Provider } from 'react-redux';
import createHistory from 'history/createBrowserHistory';
import { ConnectedRouter } from 'connected-react-router';
import axios from 'axios';

import LanguageProvider from '../common/containers/LanguageProvider';
import { selectLocale } from './containers/App/selectors';
import ScrollToTop from '../common/components/ScrollToTop';
import App from './containers/App';

import configureStore from './store';

/* eslint-disable no-underscore-dangle */

// Get the basename (WebAppPath in .properties) from the HTML
// so we can configure the router correctly
// eslint-disable-next-line
let basename = window.__BASENAME__;
const history = createHistory({ basename });
// Allow it to be garbage-collected
delete window.__BASENAME__;

let baseUrl = basename;
if (process.env.NODE_ENV === 'development') {
  // If we are using the dev server, we want to proxy these calls to the backend
  // and set correct basename for the router.
  basename = '/public';
  baseUrl = '/handboker/public';
}

axios.defaults.baseURL = baseUrl;
const LanguageProviderWithLocaleSelector = LanguageProvider(selectLocale);

function renderApp(initialState) {
  render(
    <Provider store={configureStore(initialState, history)}>
      <LanguageProviderWithLocaleSelector>
        <ConnectedRouter history={history}>
          <ScrollToTop>
            <App />
          </ScrollToTop>
        </ConnectedRouter>
      </LanguageProviderWithLocaleSelector>
    </Provider>,
    document.getElementById('app')
  );
}

axios.get('').then(res => renderApp({ global: res.data }));
