// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { connect } from 'react-redux';
import { flatMap, orderBy } from 'lodash';

import { Switch, Route, Redirect } from 'react-router-dom';
import Sticky from 'react-sticky-el';

import { Columns, Column, Section, Container } from 'kf-bui';

import { fetchHandbook } from './actions';
import Tree from '../Tree';
import { selectChaptersArray, selectHandbook, selectFlatSortedChapterStructure } from './selectors';
import ChapterPage from '../ChapterPage';
import MobileTree from './components/MobileTree';
import SearchPage from '../SearchPage';

import type { SectionType, HandbookType, ChapterType } from '../../../types';

type Props = {
  fetchHandbookFun: (string, string) => void,
  handbook: ?HandbookType,
  chapters: ChapterType[],
  chapterStructure: SectionType[],
  match: {
    params: {
      externalOrgId: string,
      handbookId: string
    },
    url: string,
    path: string
  },
  location: {
    pathname: string,
    search: string
  }
};

type State = {
  showMobileTree: boolean,
  activeSections: string[],
  expandedChapters: string[],
  userHasScrolled: ?boolean,
  clickedSection: ?string,
  innerHeight: number
};

class HandbookPage extends React.Component<Props, State> {
  state = {
    showMobileTree: false,
    activeSections: [],
    expandedChapters: [],
    userHasScrolled: undefined,
    clickedSection: undefined,
    innerHeight: 500
  };

  componentDidMount() {
    const { match, fetchHandbookFun } = this.props;
    const { externalOrgId, handbookId } = match.params;
    fetchHandbookFun(externalOrgId, handbookId);
    window.addEventListener('scroll', this.onScroll, { passive: true });
    window.addEventListener('resize', this.onWindowResize);
  }

  componentWillReceiveProps(newProps) {
    const { location: newLocation } = newProps;
    const { location } = this.props;
    const { pathname, search } = location;

    if (
      (pathname !== newLocation.pathname || search !== newLocation.search) &&
      newLocation.pathname.includes('/chapter/')
    ) {
      const documentId = newLocation.search && newLocation.search.split('id=')[1];
      const idBelongsToChapter = newProps.chapters.find(chapter => chapter.id === documentId);
      const chapterId = idBelongsToChapter
        ? idBelongsToChapter.id
        : newLocation.pathname.split('/chapter/')[1].split('/')[0];
      const firstSectionInChapter = this.getFirstSectionUnderChapter(
        newProps.chapters,
        newProps.chapterStructure,
        chapterId
      );

      const clickedSection =
        documentId && !idBelongsToChapter
          ? documentId
          : firstSectionInChapter && firstSectionInChapter.id;

      this.setState({
        activeSections: clickedSection ? [clickedSection] : [],
        clickedSection,
        userHasScrolled: undefined
      });
    }
  }

  shouldComponentUpdate = (nextProps, nextState) => {
    const propsChanged = this.props !== nextProps;
    const showMobileTreeChanged = this.state.showMobileTree !== nextState.showMobileTree;
    const activeSectionChanged =
      this.state.activeSections[0] !== nextState.activeSections[0] &&
      nextState.activeSections[0] !== undefined;
    const expandedChaptersChanged = this.state.expandedChapters !== nextState.expandedChapters;

    return propsChanged || activeSectionChanged || expandedChaptersChanged || showMobileTreeChanged;
  };

  componentWillUnmount() {
    window.removeEventListener('scroll', this.onScroll);
    window.removeEventListener('resize', this.onWindowResize);
  }

  onWindowResize = () => {
    this.setState({
      innerHeight: window.innerHeight - 205 - 48
    });
    this.forceUpdate();
  };

  // On scroll is used to figure out, together with local state, if the user has scrolled or not.
  onScroll = () => {
    const { userHasScrolled: userHasScrolledBefore, clickedSection } = this.state;

    // userHasScrolled has three states: undefined, false and true.
    // - undefined tells that there has been no scroll events registered
    // - false tells that there has been one scroll event registered, which should be from the scrollIntoView on section
    // - true tells that there has been more than one scroll events registered, which should be from user scroll action
    const userHasScrolledNow = userHasScrolledBefore === false || userHasScrolledBefore === true;

    if (userHasScrolledNow !== userHasScrolledBefore)
      this.setState({
        userHasScrolled: userHasScrolledNow,
        clickedSection: !userHasScrolledNow ? clickedSection : undefined
      });
  };

  onCollapseClick = (item: ChapterType): void => {
    const { expandedChapters } = this.state;
    const childChapters = this.getChildChapters(item);
    const childChapterIds = childChapters.map(child => child.id);
    const filteredExpandedChapters = expandedChapters.filter(
      chapterId => !childChapterIds.includes(chapterId)
    );

    if (JSON.stringify(expandedChapters) !== JSON.stringify(filteredExpandedChapters)) {
      this.setState({
        expandedChapters: filteredExpandedChapters
      });
    }
  };

  getFirstSectionUnderChapter = (unsortedChapters, sections, chapterId) => {
    const chapters = orderBy(unsortedChapters, ['sortOrder', 'title'], ['asc']);
    const firstSectionInChapter = sections.filter(section => section.parentId === chapterId)[0];

    const firstChapterUnderChapter = chapters.find(chapter => chapter.parentId === chapterId);

    if (firstChapterUnderChapter !== undefined) {
      if (
        firstSectionInChapter === undefined ||
        (firstChapterUnderChapter.sortOrder || -1) < (firstSectionInChapter.sortOrder || -1)
      ) {
        return this.getFirstSectionUnderChapter(chapters, sections, firstChapterUnderChapter.id);
      }
    }

    return firstSectionInChapter;
  };

  getChildChapters = (item: ChapterType): ChapterType[] => {
    const { chapters } = this.props;

    const children = chapters.filter(chapter => chapter.parentId === item.id);
    if (children.length === 0) {
      return [item];
    }

    return [item, ...flatMap(children, c => this.getChildChapters(c))];
  };

  getParentChapters = (item: ChapterType | SectionType, acc: string[] = []): string[] => {
    if (!item || !item.parentId) {
      return acc;
    }

    const { chapters } = this.props;

    const parent = chapters.find(i => i.id === item.parentId);
    if (parent) {
      return this.getParentChapters(parent, [parent.id, ...acc]);
    }

    return acc;
  };

  addSection = (section: SectionType): void => {
    this.setState(({ activeSections, userHasScrolled }) => {
      const newActiveSections = [
        section.id,
        ...activeSections.filter(sectionId => sectionId !== section.id)
      ];
      const newSortedActiveSections = !userHasScrolled
        ? this.sortSections(newActiveSections)
        : newActiveSections;
      const updatedPath = this.updatePath(newSortedActiveSections);

      return {
        activeSections: newSortedActiveSections,
        expandedChapters: updatedPath
      };
    });
  };

  firstChapter = (matchUrl, chapters) => {
    const rootChapters = chapters.filter(chapter => chapter.parentId === undefined);
    const orderdBySortOrder = orderBy(rootChapters, ['sortOrder', 'title'], ['asc']);

    if (orderdBySortOrder[0]) {
      return `${matchUrl}/chapter/${orderdBySortOrder[0].id}`;
    }
    return `${matchUrl}`;
  };

  sortSections = (activeSections: string[]): string[] => {
    const { chapterStructure } = this.props;
    const { clickedSection } = this.state;

    const ids = chapterStructure.map(cas => cas.id);

    const sortedActiveSections = [
      ...new Set(activeSections.filter(sectionId => sectionId !== clickedSection))
    ].sort((a, b) => ids.indexOf(a) - ids.indexOf(b));
    if (clickedSection) {
      return [clickedSection, ...sortedActiveSections];
    }
    return sortedActiveSections;
  };

  updatePath = (activeSections: string[]): string[] => {
    const { chapterStructure } = this.props;
    const { expandedChapters } = this.state;

    const activeSectionId = activeSections.length > 0 && activeSections[0];
    const activeSection = chapterStructure.find(item => item.id === activeSectionId);

    if (activeSection) {
      const pathToActiveSection = this.getParentChapters(activeSection);
      const newChapters = !!pathToActiveSection.find(
        chapterId => !expandedChapters.includes(chapterId)
      );
      if (newChapters) {
        return [...new Set(expandedChapters.concat(pathToActiveSection))];
      }
    }
    return expandedChapters;
  };

  removeSection = (section: SectionType): void => {
    const { activeSections: oldActiveSections } = this.state;

    const activeSections = oldActiveSections.filter(id => id !== section.id);
    const expandedChapters = this.updatePath(activeSections);

    this.setState({
      activeSections,
      expandedChapters
    });
  };

  toggleMobileTree = () => this.setState(state => ({ showMobileTree: !state.showMobileTree }));

  render() {
    const { handbook, chapters, match } = this.props;
    const { activeSections, expandedChapters, showMobileTree, innerHeight } = this.state;
    if (!handbook) return null;

    const { handbookId, externalOrgId } = match.params;

    document.title = `${handbook.title} - KF Håndbøker`;

    return (
      <Section>
        <Container>
          <Switch boundaryElement=".footer" hideOnBoundaryHit={false}>
            <Route
              path={`${match.path}/search`}
              render={() => <SearchPage handbookId={handbookId} externalOrgId={externalOrgId} />}
            />
            <Route path={`${match.url}`}>
              <Columns>
                <MobileTree
                  toggleTree={this.toggleMobileTree}
                  showTree={showMobileTree}
                  activeSections={activeSections}
                  expandedChapters={expandedChapters}
                  onCollapseClick={this.onCollapseClick}
                />

                <Column size="1/3" hiddenMobile>
                  <Sticky
                    id="scrollable-tree"
                    style={{
                      overflowY: 'scroll',
                      maxHeight: innerHeight // calcStickyHeight(window.innerHeight) // window.innerHeight
                    }}
                  >
                    <Tree
                      activeSections={activeSections}
                      expandedChapters={expandedChapters}
                      onCollapseClick={this.onCollapseClick}
                      externalOrgId={externalOrgId}
                    />
                  </Sticky>
                </Column>

                {!showMobileTree && (
                  <Column size="2/3">
                    <Switch>
                      <Route
                        path={`${match.url}/chapter/:chapterId`}
                        render={({ match: innerMatch, location }) => (
                          <ChapterPage
                            match={innerMatch}
                            location={location}
                            setSection={this.addSection}
                            removeSection={this.removeSection}
                          />
                        )}
                      />
                      <Redirect to={this.firstChapter(match.url, chapters)} />
                    </Switch>
                  </Column>
                )}
              </Columns>
            </Route>
          </Switch>
        </Container>
      </Section>
    );
  }
}

export default connect(
  state => ({
    handbook: selectHandbook(state),
    chapters: selectChaptersArray(state),
    chapterStructure: selectFlatSortedChapterStructure()(state)
  }),
  { fetchHandbookFun: fetchHandbook }
)(HandbookPage);
