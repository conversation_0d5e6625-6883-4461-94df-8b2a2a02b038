version: "3"
services:
  # Service name
  webapp-1:
    # Container name
    container_name: handboker_1
    # Which image we are creating a container for
    image: kommuneforlaget/handboker:2.2.33
    build:
      context: .
      args:
        APPLICATION_NAME: handboker
    volumes:
      # Property volume
      - ./src/test/resources/handboker.properties:/usr/local/tomcat/conf/handboker.properties
      # Logging volume. Right side should match the LogStore in .properties. If path is relative in .properties the path is calculated from /usr/local/tomcat/
      - ./logs/:/usr/local/tomcat/target/application-work-files/
      # Example file volume.
      - ./src/test/resources/example-files/:/usr/local/tomcat/src/test/resources/example-files/
    ports:
      - 5600:8080
