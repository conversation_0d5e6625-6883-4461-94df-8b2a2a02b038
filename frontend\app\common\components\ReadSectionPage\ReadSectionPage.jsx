// @flow
import React, { useState, useEffect } from 'react';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Container, Section, Subtitle, Title } from 'kf-bui';
import RenderHtml from '../RenderHtml';
import retrieveReadingLinkData from './api';
import type { SectionType } from '../../../types';
import InvalidLinkPage from '../InvalidLinkPage';

type Props = {
  match: {
    params: {
      linkId: string
    }
  }
};

const ReadSectionPage = ({ match }: Props) => {
  const [section, setSection] = useState<?SectionType>(undefined);

  useEffect(() => {
    retrieveReadingLinkData(match.params.linkId).then(retrievedSection =>
      setSection(retrievedSection)
    );
  }, [match.params.linkId]);

  if (!section) {
    return <InvalidLinkPage />;
  }

  return (
    <Section>
      <Container>
        <Title>{section.title}</Title>
        <Subtitle>
          <RenderHtml html={section.text} />
        </Subtitle>
      </Container>
    </Section>
  );
};

export default compose(withRouter)(ReadSectionPage);
