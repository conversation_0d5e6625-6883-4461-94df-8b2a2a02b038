import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Redirect, Route, Switch } from 'react-router-dom';

import { selectHandbookEntities } from '../EditorPage/selectors';
import { HandbookShape } from '../../shapes';
import LinkPage from '../LinkPage';
import HandbookScreen from './components/HandbookScreen';
import ChapterPage from '../ChapterPage';
import SectionPage from '../SectionPage';

class HandbookPage extends React.Component {
  getChildContext() {
    const { handbooks, match } = this.props;
    return {
      handbook: handbooks[match.params.handbookId]
    };
  }

  render() {
    const { match, handbooks } = this.props;
    const handbook = handbooks[match.params.handbookId];

    if (!handbook) {
      // If we have handbooks, but cannot find the one we are looking for, redirect to 404 page
      return Object.values(handbooks).length > 0 ? <Redirect to="/404" /> : null;
    }

    document.title = `${handbook.title}  - <PERSON><PERSON>`;

    return (
      <div>
        <Switch>
          <Route path={match.path} exact component={HandbookScreen} />
          <Route path={`${match.path}/chapter`} component={ChapterPage} />
          <Route path={`${match.path}/section`} component={SectionPage} />
          <Route path={`${match.path}/links`} component={LinkPage} />
        </Switch>
      </div>
    );
  }
}

HandbookPage.propTypes = {
  handbooks: PropTypes.objectOf(HandbookShape).isRequired,
  match: PropTypes.shape({
    path: PropTypes.string.isRequired,
    params: PropTypes.shape({
      handbookId: PropTypes.string.isRequired
    }).isRequired
  }).isRequired
};

HandbookPage.childContextTypes = {
  handbook: HandbookShape
};

export default connect(state => ({ handbooks: selectHandbookEntities(state) }))(HandbookPage);
