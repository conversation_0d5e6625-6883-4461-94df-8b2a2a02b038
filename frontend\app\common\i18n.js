import React from 'react';
import { addLocaleData, intlShape } from 'react-intl';

import nbLocaleData from 'react-intl/locale-data/nb';
import nnLocaleData from 'react-intl/locale-data/nn';

import nbTranslationMessages from './translations/nb.json';
import nnTranslationMessages from './translations/nn.json';

addLocaleData(nbLocaleData);
addLocaleData(nnLocaleData);

export const appLocales = ['nb', 'nn'];

export const formatTranslationMessages = (locale, messages) =>
  Object.keys(messages).reduce((formattedMessages, key) => {
    const formattedMessage = messages[key];
    return Object.assign(formattedMessages, { [key]: formattedMessage });
  }, {});

export const translationMessages = {
  nb: formatTranslationMessages('nb', nbTranslationMessages),
  nn: formatTranslationMessages('nn', nnTranslationMessages)
};

/* eslint-disable react/jsx-filename-extension */
function createInjectT(prefix) {
  return WrappedComponent => {
    function getDisplayName(component) {
      return component.displayName || component.name || 'Component';
    }

    const InjectT = (props, context) => (
      <WrappedComponent
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...props}
        t={(id, value = {}) =>
          context.intl.formatMessage({ id: prefix ? `${prefix}.${id}` : id }, value)
        }
      />
    );

    InjectT.contextTypes = {
      intl: intlShape
    };

    InjectT.displayName = `InjectT(${getDisplayName(WrappedComponent)})`;

    return InjectT;
  };
}

export default createInjectT;
