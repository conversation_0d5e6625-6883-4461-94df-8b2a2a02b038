import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Switch, Route, withRouter } from 'react-router-dom';
import Toaster from 'kf-toaster';

import './custom.css'; // Custom css for the HTML in Sections - could some of this be moved into kf-bui?
import injectT from '../../../common/i18n';
import { selectOrganization, selectLogoUrl, selectBannerUrl } from './selectors';
import { selectHandbook, selectLinkCollections } from '../HandbookPage/selectors';
import NotFoundPage from '../../../common/components/NotFoundPage';
import NoAccessPage from '../../../common/components/NoAccessPage';
import WelcomePage from '../WelcomePage';
import HandbookPage from '../HandbookPage';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import { HandbookShape } from '../../shapes';
import ReadSectionPage from '../../../common/components/ReadSectionPage/ReadSectionPage';
import OptOutModal from '../../../common/components/OptOutModal/OptOutModal';

// The inline styling here is for a sticky footer
// The most outer div here isn't supposed to be needed,
// but it works around a bug in IE 10/11 so we get our sticky footer
// See https://github.com/philipwalton/flexbugs#workaround-2

const App = ({ organization, handbook, logoUrl, bannerUrl, linkCollections, t }) => {
  const [showOptOutModal, setShowOptOutModal] = useState(false);

  useEffect(() => {
    try {
      const isVisited = JSON.parse(localStorage.getItem('hasVisited'));
      if (isVisited === null || isVisited === false) {
        localStorage.setItem('hasVisited', true);
        setShowOptOutModal(true);
      } else {
        setShowOptOutModal(false);
      }
    } catch (error) {
      console.error(error);
      localStorage.setItem('hasVisited', true);
      setShowOptOutModal(true);
    }
  }, []);

  document.title = 'KF Håndbøker';
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <div style={{ flex: '1 0 auto' }}>
          <Navbar
            logoUrl={logoUrl}
            bannerUrl={bannerUrl}
            organization={organization}
            handbook={handbook}
            linkCollections={linkCollections}
          />
          {showOptOutModal && <OptOutModal toggleHide={() => setShowOptOutModal(false)} />}
          <Switch>
            <Route path="/readinglink/:linkId" component={ReadSectionPage} />,
            <Route path="/" exact component={WelcomePage} />
            <Route path="/:externalOrgId/:handbookId" component={HandbookPage} />
            <Route path="/forbidden" component={NoAccessPage} />
            <Route path="*" component={NotFoundPage} />
          </Switch>
        </div>
        <Toaster />
        <Footer application={t('application')} showOptOutModal={() => setShowOptOutModal(true)} />
      </div>
    </div>
  );
};

App.propTypes = {
  t: PropTypes.func.isRequired,
  organization: PropTypes.shape({
    name: PropTypes.string
  }),
  handbook: HandbookShape,
  logoUrl: PropTypes.string,
  bannerUrl: PropTypes.string,
  linkCollections: PropTypes.array
};

App.defaultProps = {
  organization: null,
  handbook: null,
  logoUrl: null,
  bannerUrl: null,
  linkCollections: []
};

const mapStateToProps = state => ({
  organization: selectOrganization(state),
  handbook: selectHandbook(state),
  logoUrl: selectLogoUrl(state),
  bannerUrl: selectBannerUrl(state),
  linkCollections: selectLinkCollections(state)
});

// withRouter to fix the issue with blocking updates https://reacttraining.com/react-router/web/guides/dealing-with-update-blocking
export default compose(withRouter, connect(mapStateToProps), injectT('public.containers.App'))(App);
