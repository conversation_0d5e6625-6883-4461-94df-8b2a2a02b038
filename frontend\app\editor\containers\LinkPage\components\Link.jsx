// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { Addons, Icon, Button } from 'kf-bui';
import styled from 'styled-components';
import injectT from '../../../../common/i18n';
import type { LinkType } from '../../../../types';
import LinkForm from './LinkForm';

const GrowingDiv = styled.div`
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
`;

type Props = {
  // Her burde vi i stedet ta imot link: LinkType
  ...LinkType,
  onDelete: string => void,
  onSave: LinkType => void,
  t: string => string
};
type State = {
  isEditing: boolean
};

class Link extends React.Component<Props, State> {
  state = {
    isEditing: false
  };

  componentWillReceiveProps(nextProps: Props) {
    const { title, url } = this.props;
    if (nextProps.title !== title || nextProps.url !== url) {
      this.setState({ isEditing: false });
    }
  }

  toggleEditing = () => this.setState(state => ({ isEditing: !state.isEditing }));

  render() {
    const { isEditing } = this.state;
    const { id, title, url, onDelete, sortOrder, onSave, t } = this.props;

    return isEditing ? (
      <LinkForm
        id={id}
        title={title}
        url={url}
        sortOrder={sortOrder}
        onCancel={this.toggleEditing}
        onSave={onSave}
      />
    ) : (
      <GrowingDiv>
        <div style={{ flexShrink: '11' }}>
          <span style={{ marginLeft: 10 }}>{title}</span>
          <small style={{ marginLeft: 10 }}>
            <a href={url} target="_blank" rel="noopener noreferrer">
              {url}
            </a>
          </small>
        </div>
        <Addons className="is-pulled-right field has-addons">
          <Button size="small" onClick={this.toggleEditing}>
            <Icon icon="pencil" size="small" /> <span>{t('edit')}</span>
          </Button>
          <Button className="is-danger" size="small" control onClick={() => onDelete(id || '')}>
            <Icon icon="trash" size="small" /> <span>{t('delete')}</span>
          </Button>
        </Addons>
      </GrowingDiv>
    );
  }
}
export default injectT('editor.containers.LinkPage.components.Link')(Link);
