// @flow
import React from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Button, Column, Columns, Control, Group, Icon, Menu, Title } from 'kf-bui';
import { toast } from 'kf-toaster';
import PropTypes from 'prop-types';
import injectT from '../../../common/i18n';
import PendingChangeWarning from '../../components/PendingChangeWarning';
import DeleteButton from '../../components/DeleteButton';
import NoSelectionScreen from '../EditorPage/components/NoSelectionScreen';

import EditCentralHandbookButton from './util/EditCentralHandbookButton';
import SortChildrenScreen from '../../components/SortChildrenScreen/SortChildrenScreen';
import PubliseringsModal from './util/PubliseringsModal';
import EksporteringsModal from './util/EksporteringsModal';

import { deleteCentralHandbook, fetchAllReadingLinks, sortCentralItems } from './actions';
import {
  makeSelectCentralHandbookById,
  makeSelectHandbookChildren,
  makeSelectReadingLinksById,
  makeSelectHandbookSections
} from './selectors';

import type {
  CentralHandbookType,
  CentralChapterType,
  CentralSectionType,
  ReadingLinkType
} from '../../../types';
import { pendingPublications } from './api';

type Props = {
  centralHandbook: CentralHandbookType,
  chapters: CentralChapterType[],
  centralSections: CentralSectionType[],
  readingLinks: ReadingLinkType[],
  match: {
    path: string,
    params: {
      handbookId: string
    }
  },
  t: (string, ?{}) => string,
  sortChildrenFunction: (string[]) => void,
  deleteCentralHandbookFunction: (bookId: string) => void,
  fetchAllReadingLinksFunction: () => void,
  successToast: (message: string) => void
};

type State = {
  isSorting: boolean,
  showPubliseringsModal: boolean,
  showEksporteringsModal: boolean,
  isPendingPublication: boolean
};

class CentralHandbookScreen extends React.Component<Props, State> {
  state = {
    isSorting: false,
    showPubliseringsModal: false,
    showEksporteringsModal: false,
    isPendingPublication: false
  };

  pollingInterval = null;

  componentDidMount() {
    /* eslint-disable react/destructuring-assignment */
    this.props.fetchAllReadingLinksFunction();
    /* eslint-enable react/destructuring-assignment */
    this.fetchPendingPublications();
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.centralHandbook &&
      this.props.centralHandbook &&
      prevProps.centralHandbook.id !== this.props.centralHandbook.id
    ) {
      this.stopPolling();
      this.setState({ isPendingPublication: false });
      this.fetchPendingPublications();
    }
  }

  componentWillUnmount() {
    this.stopPolling();
    this.setState({ isPendingPublication: false });
  }

  startPolling = () => {
    this.pollingInterval = setInterval(this.fetchPendingPublications, 10000);
  };

  stopPolling = () => {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  };

  toggleSort = () => this.setState(state => ({ isSorting: !state.isSorting }));

  // Fetch pending publications and update state
  fetchPendingPublications = async () => {
    const {
      match: {
        params: { handbookId }
      },
      centralHandbook
    } = this.props;

    try {
      const isPending = await pendingPublications(handbookId);

      if (this.state.isPendingPublication && !isPending) {
        this.props.successToast(`Endringer publisert: ${centralHandbook && centralHandbook.title}`);
        this.setState({ isPendingPublication: false });
        this.stopPolling();
      } else if (isPending && !this.state.isPendingPublication) {
        this.setState({ isPendingPublication: true });
        this.startPolling();
      }
    } catch (error) {
      console.error('Error fetching pending publications:', error);
    }
  };

  handlePublishHandbook = () => {
    this.setState({ showPubliseringsModal: false });
    this.fetchPendingPublications();
  };

  renderChildren() {
    const { chapters } = this.props;
    const sortedChapters = chapters.sort((c1, c2) => c1.sortOrder - c2.sortOrder);

    return sortedChapters.length > 0 ? (
      <Menu>
        <Menu.List>
          {sortedChapters.map(chapter => (
            <Menu.Item
              key={chapter.id}
              as={Link}
              to={`/central-editor/${chapter.centralHandbookId}/chapter/${chapter.id}/`}
            >
              <Icon icon="bookmark-o" size="small" />
              {chapter.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    ) : (
      'Denne håndboka har ingen kapitler'
    );
  }

  render() {
    const {
      chapters,
      centralHandbook,
      centralSections,
      readingLinks,
      t,
      match,
      sortChildrenFunction,
      ...props
    } = this.props;
    const {
      isSorting,
      showPubliseringsModal,
      showEksporteringsModal,
      isPendingPublication
    } = this.state;

    if (!centralHandbook) {
      return <NoSelectionScreen />;
    }
    if (isSorting) {
      return (
        <SortChildrenScreen
          items={chapters}
          sortFunction={toSortIds => sortChildrenFunction(toSortIds)}
          onCancel={this.toggleSort}
        />
      );
    }

    return (
      <>
        <Columns>
          <Column>
            <Title>
              <Icon icon="book" size="small" style={{ marginRight: '1rem' }} />
              <span>{centralHandbook.title}</span>
            </Title>
          </Column>
        </Columns>
        {centralHandbook.id && (
          <Columns>
            <Column>
              <Group>
                <EditCentralHandbookButton centralHandbook={centralHandbook} t={t} />{' '}
                <Button
                  control
                  as={Link}
                  to={`/central-editor/${centralHandbook.id}/chapter/add-new`}
                  size="small"
                >
                  <Icon icon="plus" size="small" />
                  <span>{t('newChapter')}</span>
                </Button>
                <Button
                  control
                  size="small"
                  disabled={isPendingPublication}
                  onClick={() => this.setState({ showPubliseringsModal: true })}
                >
                  {isPendingPublication ? (
                    <>
                      <Icon icon="spinner" size="small" />
                      <span>Venter publisering</span>
                    </>
                  ) : (
                    <>
                      <Icon icon="check" size="small" />
                      <span>Publiser sentral håndbok</span>
                    </>
                  )}
                </Button>
                {showPubliseringsModal && (
                  <PubliseringsModal
                    handbook={centralHandbook}
                    toggleHide={() => this.setState({ showPubliseringsModal: false })}
                    handlePublish={this.handlePublishHandbook}
                  />
                )}
                <Button
                  control
                  size="small"
                  onClick={() => this.setState({ showEksporteringsModal: true })}
                >
                  <span>Eksporter</span>
                </Button>
                {showEksporteringsModal && (
                  <EksporteringsModal
                    centralHandbook={centralHandbook}
                    onHide={() => this.setState({ showEksporteringsModal: false })}
                  />
                )}
              </Group>
            </Column>
            <Column narrow>
              <Group>
                <Control>
                  <PendingChangeWarning
                    element={centralHandbook}
                    mergeLink={`/merge/central-handbook/${centralHandbook.id}/`}
                  />
                </Control>
                <Button
                  control
                  active={isSorting}
                  onClick={this.toggleSort}
                  disabled={chapters.length <= 1}
                  title="Sorter kapitlene til Håndboken"
                  size="small"
                >
                  {t('sortButton')}
                </Button>
                <DeleteButton
                  t={t}
                  toDelete={centralHandbook}
                  onDelete={() => props.deleteCentralHandbookFunction(centralHandbook.id)}
                  readlinkExists={readingLinks && readingLinks.length > 0}
                />
              </Group>
            </Column>
          </Columns>
        )}
        <hr />
        {chapters !== undefined && this.renderChildren()}
      </>
    );
  }
}

const mapStateToProps = (state, props) => {
  const {
    match: {
      params: { handbookId }
    }
  } = props;
  const selectCentralHandbookById = makeSelectCentralHandbookById(handbookId);
  const selectCentralChaptersByHandbookId = makeSelectHandbookChildren(handbookId);
  const selectReadingLinksByHandbookId = makeSelectReadingLinksById(handbookId);
  const selectCentralSectionByHandbookId = makeSelectHandbookSections(handbookId);
  return {
    centralHandbook: selectCentralHandbookById(state),
    chapters: selectCentralChaptersByHandbookId(state),
    readingLinks: selectReadingLinksByHandbookId(state),
    centralSections: selectCentralSectionByHandbookId(state)
  };
};

const mapDispatchToProps = {
  sortChildrenFunction: sortCentralItems,
  deleteCentralHandbookFunction: deleteCentralHandbook,
  fetchAllReadingLinksFunction: fetchAllReadingLinks,
  successToast: toast.success
};

CentralHandbookScreen.propTypes = {
  successToast: PropTypes.func.isRequired
};

export default compose(
  injectT('editor.containers.HandbookSelection'),
  connect(mapStateToProps, mapDispatchToProps),
  withRouter
)(CentralHandbookScreen);
