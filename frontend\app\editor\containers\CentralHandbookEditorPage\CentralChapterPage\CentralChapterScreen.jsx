// @flow
import React from 'react';
import { Link } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';

import { Button, Control, Column, Icon, Columns, Title, Group, Menu } from 'kf-bui';
import injectT from '../../../../common/i18n';

import type { CentralChapterType, CentralChildrenType, ReadingLinkType } from '../../../../types';
import PendingChangeWarning from '../../../components/PendingChangeWarning';
import DeleteButton from '../../../components/DeleteButton/DeleteButton';
import Metadata from '../../../components/Metadata';
import SortChildrenScreen from '../../../components/SortChildrenScreen';
import {
  makeSelectCentralChapterById,
  makeSelectChapterChildren,
  makeSelectReadingLinksByChapterId
} from '../selectors';
import { deleteCentralChapter, fetchAllReadingLinks, sortCentralItems } from '../actions';

type Props = {
  centralChapter: ?CentralChapterType,
  children: CentralChildrenType[],
  readingLinks: ReadingLinkType[],
  t: (string, ?{}) => string,
  match: {
    params: {
      chapterId: string
    }
  },
  sortChildrenFunction: (string[]) => void,
  deleteCentralChapterFunction: (chapterId: string) => void,
  fetchAllReadingLinksFunction: () => void
};

type State = {
  isSorting: boolean
};

class CentralChapterScreen extends React.Component<Props, State> {
  state = {
    isSorting: false
  };

  componentDidMount() {
    /* eslint-disable-next-line react/destructuring-assignment */
    this.props.fetchAllReadingLinksFunction();
  }

  toggleSort = () => this.setState(state => ({ isSorting: !state.isSorting }));

  render() {
    const {
      centralChapter,
      t,
      children,
      sortChildrenFunction,
      deleteCentralChapterFunction,
      readingLinks
    } = this.props;
    const { isSorting } = this.state;
    if (!centralChapter) return null;

    const sortedChildren = children.sort((c1, c2) => c1.sortOrder - c2.sortOrder);

    return (
      <div>
        <Columns>
          <Column>
            <Title>
              <Icon icon="bookmark-o" size="small" style={{ marginRight: '1rem' }} />
              <span>{centralChapter.title}</span>
            </Title>
          </Column>
        </Columns>
        <Columns responsive="desktop">
          <Column>
            <Group>
              <Button
                control
                as={Link}
                to={`/central-editor/${centralChapter.centralHandbookId}/chapter/edit/${centralChapter.id}/`}
                size="small"
              >
                <Icon icon="pencil" size="small" />
                <span>{t('editButton')}</span>
              </Button>
              <Button
                control
                as={Link}
                to={{
                  pathname: `/central-editor/${centralChapter.centralHandbookId}/chapter/add-new`,
                  search: `?parent=${centralChapter.id}`
                }}
                size="small"
              >
                <Icon icon="plus" size="small" />
                <span>{t('newChapter')}</span>
              </Button>
              <Button
                control
                as={Link}
                to={{
                  pathname: `/central-editor/${centralChapter.centralHandbookId}/section/add-new/`,
                  search: `?parent=${centralChapter.id}`
                }}
                size="small"
              >
                <Icon icon="plus" size="small" />
                <span>{t('newSection')}</span>
              </Button>
            </Group>
          </Column>
          <Column narrow>
            <Group>
              <Control>
                <PendingChangeWarning
                  element={centralChapter}
                  mergeLink={`/merge/chapter/${centralChapter.id}/`}
                />
              </Control>
              <Button
                control
                active={isSorting}
                disabled={sortedChildren.length <= 1}
                onClick={this.toggleSort}
                size="small"
              >
                {t('sortButton')}
              </Button>
              <Button
                control
                as={Link}
                to={`/central-editor/${centralChapter.centralHandbookId}/chapter/move/${centralChapter.id}`}
                size="small"
              >
                {t('moveButton')}
              </Button>
              <DeleteButton
                t={t}
                toDelete={centralChapter}
                onDelete={() => deleteCentralChapterFunction(centralChapter.id)}
                readlinkExists={readingLinks && readingLinks.length > 0}
              />
            </Group>
          </Column>
        </Columns>
        <Columns>
          <Column>
            <hr />
            <Metadata element={centralChapter} />
            <hr />
            {isSorting ? (
              <SortChildrenScreen
                items={sortedChildren}
                sortFunction={sortChildrenFunction}
                onCancel={this.toggleSort}
              />
            ) : (
              <Menu>
                <Menu.List>
                  {sortedChildren &&
                    sortedChildren.map(child =>
                      child.type === 'SECTION' ? (
                        <Menu.Item
                          key={child.id}
                          as={Link}
                          to={`/central-editor/${child.centralHandbookId}/section/${child.id}/`}
                        >
                          <Icon icon="file-text-o" /> {child.title}
                        </Menu.Item>
                      ) : (
                        <Menu.Item
                          key={child.id}
                          as={Link}
                          to={`/central-editor/${child.centralHandbookId}/chapter/${child.id}/`}
                        >
                          <Icon icon="bookmark-o" /> {child.title}
                        </Menu.Item>
                      )
                    )}
                </Menu.List>
              </Menu>
            )}
          </Column>
        </Columns>
      </div>
    );
  }
}

const mapStateToProps = (state, props) => {
  const { chapterId } = props.match.params;

  const selectCentralChapter = makeSelectCentralChapterById(chapterId);
  const selectChildren = makeSelectChapterChildren(chapterId);
  const selectReadingLinksByChapterId = makeSelectReadingLinksByChapterId(chapterId);
  return {
    centralChapter: selectCentralChapter(state),
    children: selectChildren(state),
    readingLinks: selectReadingLinksByChapterId(state)
  };
};

const mapDispatchToProps = {
  sortChildrenFunction: sortCentralItems,
  deleteCentralChapterFunction: deleteCentralChapter,
  fetchAllReadingLinksFunction: fetchAllReadingLinks
};

export default compose(
  injectT('editor.containers.ChapterSelection'),
  connect(mapStateToProps, mapDispatchToProps)
)(CentralChapterScreen);
