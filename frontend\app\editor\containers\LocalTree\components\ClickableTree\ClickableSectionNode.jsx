import React from 'react';
import { Icon, Tree } from 'kf-bui';
import { SectionShape } from '../../../../shapes';

// Sections are always disabled when moving stuff
const ClickableSectionNode = ({ section }) => (
  <Tree.Item disabled id={section.id}>
    <Icon icon="file-text-o" size="small" /> {section.title}
  </Tree.Item>
);

ClickableSectionNode.propTypes = {
  section: SectionShape.isRequired
};

export default ClickableSectionNode;
