import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Button, Container, Control, Group, Section, Title, Subtitle, Select } from 'kf-bui';
import { SessionShape } from '../shapes';
import { selectSession } from './App/selectors';
import { setOrganization } from './App/actions';
import injectT from '../../common/i18n';

class SelectExternalOrganization extends React.Component {
  state = {
    organizationId: this.props.session.organization ? this.props.session.organization.id : null,
    isPending: false,
    // Add the external org Id to to the label
    orgsWithLabel: this.props.session.userOrgsWithAccess.map(org => ({
      id: org.id,
      label: `${org.name} (${org.id})`
    }))
  };

  componentWillReceiveProps(nextProps) {
    const { session, history, location } = this.props;
    if (nextProps.session.organization !== session.organization) {
      const { from } = location.state || { from: { pathname: '/' } };
      history.push(from.pathname);
    }
  }

  onSelectChange = organizationId => this.setState({ organizationId });

  handleSubmit = event => {
    event.preventDefault();
    this.setState({ isPending: true });
    this.props.setOrganization(this.state.organizationId);
  };

  render() {
    const {
      session: { organization },
      t
    } = this.props;
    const { orgsWithLabel, organizationId, isPending } = this.state;
    const hasPreviousOrg = Boolean(organization);

    return (
      <Section>
        <Container>
          <Title>{hasPreviousOrg ? t('changeOrganizationTitle') : t('setOrganizationTitle')}</Title>
          {!hasPreviousOrg && <Subtitle>{t('selectOrganization')}</Subtitle>}
          <form onSubmit={this.handleSubmit}>
            <Group>
              <Control expanded>
                <Select
                  aria-label="Organisasjon"
                  options={orgsWithLabel}
                  valueKey="id"
                  onChange={this.onSelectChange}
                  matchProp="label"
                  name="organization"
                  simpleValue
                  value={organizationId}
                />
              </Control>
              <Button color="primary" loading={isPending} type="submit">
                {hasPreviousOrg ? t('changeOrganizationButton') : t('setOrganizationButton')}
              </Button>
            </Group>
          </form>
        </Container>
      </Section>
    );
  }
}

SelectExternalOrganization.propTypes = {
  history: PropTypes.object.isRequired,
  location: PropTypes.object.isRequired,
  setOrganization: PropTypes.func.isRequired,
  session: SessionShape.isRequired,
  t: PropTypes.func.isRequired
};

export default compose(
  injectT('editor.containers.SelectOrganizationPage'),
  connect(state => ({ session: selectSession(state) }), { setOrganization })
)(SelectExternalOrganization);
