// @flow
import React from 'react';
import { connect } from 'react-redux';
import { FormattedMessage } from 'react-intl';
import { Title, Subtitle, Columns, Column, Button } from 'kf-bui';

import { selectSelectedCentralItem } from '../App/selectors';
import { makeSelectCentralChapterById, makeSelectCentralSectionById } from './selectors';
import { saveCentralChapter, saveCentralSection } from './actions';

import type { CentralChapterType, CentralSectionType, CentralTreeNodeType } from '../../../types';

type Props = {
  match: {},
  history: { goBack: () => void },
  selectedCentralItem: CentralTreeNodeType,
  chapter: CentralChapterType,
  section: CentralSectionType,
  saveCentralChapterFun: CentralChapterType => void,
  saveCentralSectionFun: CentralSectionType => void
};

const sameLocation = (
  selectedItem: CentralTreeNodeType,
  movedItem: CentralChapterType | CentralSectionType
) => {
  if (selectedItem.type === 'HANDBOOK') {
    return !movedItem.parentId && movedItem.centralHandbookId === selectedItem.id;
  }
  return selectedItem.id === movedItem.parentId;
};

const differentHandbook = (
  selectedItem: CentralTreeNodeType,
  movedItem: CentralChapterType | CentralSectionType
) => {
  if (selectedItem.type === 'HANDBOOK') {
    return selectedItem.id !== movedItem.centralHandbookId;
  }
  return selectedItem.centralHandbookId !== movedItem.centralHandbookId;
};

const MoveCentralChapterOrSection = ({
  history,
  selectedCentralItem,
  chapter,
  section,
  saveCentralChapterFun,
  saveCentralSectionFun
}: Props) => {
  const movedItem = chapter || section;
  if (!movedItem) {
    return <div />;
  }

  const sameLoc = selectedCentralItem && sameLocation(selectedCentralItem, movedItem);
  const diffHandbook = selectedCentralItem && differentHandbook(selectedCentralItem, movedItem);
  const sectionToHandbook =
    selectedCentralItem && selectedCentralItem.type === 'HANDBOOK' && movedItem.type === 'SECTION';

  return (
    <div>
      <Title>Flytt</Title>
      <Subtitle>
        <FormattedMessage
          id="editor.containers.MoveChapterOrSelection.moveElement"
          values={{
            title: <em>{movedItem.title}</em>
          }}
        />
      </Subtitle>
      <hr />
      {selectedCentralItem ? (
        <Subtitle>Ny plassering valgt: {selectedCentralItem.title}</Subtitle>
      ) : (
        undefined
      )}
      {sectionToHandbook && (
        <Subtitle>Du kan ikke plassere en seksjon direkte under en håndbok</Subtitle>
      )}
      {sameLoc && (
        <Subtitle>
          {movedItem.title} er alleredet i {selectedCentralItem.title}{' '}
        </Subtitle>
      )}
      {diffHandbook && <Subtitle>Du kan ikke flytte mellom forskjellige håndbøker</Subtitle>}
      <Columns responsive="mobile">
        <Column>
          <Button onClick={history.goBack} size="medium">
            Avbryt
          </Button>
        </Column>
        {selectedCentralItem && !sameLoc && !diffHandbook && !sectionToHandbook ? (
          <Column narrow>
            <Button
              color="primary"
              onClick={() => {
                const parentIsHandbook = selectedCentralItem.id === movedItem.centralHandbookId;
                const updatedItem = {
                  ...movedItem,
                  parentId: parentIsHandbook ? undefined : selectedCentralItem.id
                };
                if (updatedItem.type === 'SECTION') {
                  saveCentralSectionFun(updatedItem);
                } else {
                  saveCentralChapterFun(updatedItem);
                }
                history.goBack();
              }}
              size="medium"
            >
              Flytt
            </Button>
          </Column>
        ) : (
          undefined
        )}
      </Columns>
    </div>
  );
};

const mapStateToProps = (state, ownProps) => {
  const {
    match: { params }
  } = ownProps;

  const selectChapter = makeSelectCentralChapterById(params.chapterId);
  const selectSection = makeSelectCentralSectionById(params.sectionId);

  return {
    selectedCentralItem: selectSelectedCentralItem(state),
    chapter: selectChapter(state),
    section: selectSection(state)
  };
};

export default connect(mapStateToProps, {
  saveCentralChapterFun: saveCentralChapter,
  saveCentralSectionFun: saveCentralSection
})(MoveCentralChapterOrSection);
