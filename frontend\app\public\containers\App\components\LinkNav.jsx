// @flow
/* eslint-disable react/prop-types */

import React from 'react';
import { Nav, Icon } from 'kf-bui';
import styled from 'styled-components';
import type { LinkType } from '../../../../types';

type Props = {
  id: string,
  title: string,
  links: LinkType[]
};

const ColorForcedDropdown = styled(Nav.Dropdown)`
  color: #fff !important;

  .navbar-link:focus {
    border-bottom-color: white;
  }

  .navbar-item:hover {
    border: 1px solid transparent;
    border-radius: unset;
  }

  :hover .navbar-link {
    background-color: rgba(10, 10, 10, 0.1) !important;
  }
  .navbar-dropdown {
    border: None;
  }

  .navbar-link::after {
    right: 5px;
  }

  .navbar-link {
    padding-right: 25px;
  }

  .navbar-dropdown {
    color: #050037;
  }
`;

const LinkNav = ({ links, title, id }: Props) => (
  <ColorForcedDropdown as={Nav.Item} hoverable title={title} id={id}>
    {links
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(link => (
        <Nav.Item key={link.id} target="_blank" href={link.url}>
          <span>{link.title}</span>
          <Icon style={{ marginLeft: 10 }} size="small" icon="external-link" />
        </Nav.Item>
      ))}
  </ColorForcedDropdown>
);

export default LinkNav;
