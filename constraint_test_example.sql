-- Constraint Verification Test for welcome_page_version table
-- Run this after Migration 28 to verify constraint behavior

-- Assume we have a test handbook
-- INSERT INTO handbook (id, title, external_org_id, ...) VALUES ('test-hb-1', 'Test Handbook', 'test-org', ...);

-- Test 1: Insert first active ARCHIVED version (should succeed)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v1', 'test-hb-1', 'ARCHIVED', 1000, 1000, 'user1', 0, NULL);
-- ✅ SUCCESS: First active ARCHIVED version

-- Test 2: Try to insert second active ARCHIVED version (should fail)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v2', 'test-hb-1', 'ARCHIVED', 2000, 2000, 'user2', 0, NULL);
-- ❌ EXPECTED ERROR: duplicate key value violates unique constraint "welcome_page_version_handbook_status_active"

-- Test 3: Insert soft-deleted ARCHIVED version (should succeed)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v3', 'test-hb-1', 'ARCHIVED', 3000, 3000, 'user3', 1, 3000);
-- ✅ SUCCESS: Soft-deleted records don't violate constraint

-- Test 4: Insert another soft-deleted ARCHIVED version (should succeed)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v4', 'test-hb-1', 'ARCHIVED', 4000, 4000, 'user4', 1, 4000);
-- ✅ SUCCESS: Multiple soft-deleted records allowed

-- Test 5: Insert PUBLISHED version for same handbook (should succeed)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v5', 'test-hb-1', 'PUBLISHED', 5000, 5000, 'user5', 0, NULL);
-- ✅ SUCCESS: Different status, no conflict

-- Test 6: Insert DRAFT version for same handbook (should succeed)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by, deleted, deleted_date) 
VALUES ('v6', 'test-hb-1', 'DRAFT', 6000, 6000, 'user6', 0, NULL);
-- ✅ SUCCESS: Different status, no conflict

-- Verification Query 1: Check active records (what application sees)
SELECT 'Active Records' as view_type, handbook_id, status, id, deleted, deleted_date
FROM welcome_page_version 
WHERE handbook_id = 'test-hb-1' AND deleted = 0
ORDER BY status;
-- Expected: 1 ARCHIVED, 1 PUBLISHED, 1 DRAFT

-- Verification Query 2: Check all records (audit view)
SELECT 'All Records' as view_type, handbook_id, status, id, deleted, deleted_date
FROM welcome_page_version 
WHERE handbook_id = 'test-hb-1'
ORDER BY status, deleted, created_at;
-- Expected: 3 ARCHIVED (1 active, 2 soft-deleted), 1 PUBLISHED, 1 DRAFT

-- Test 7: Simulate publishing workflow
-- Soft delete existing active ARCHIVED
UPDATE welcome_page_version 
SET deleted = 1, deleted_date = 7000 
WHERE handbook_id = 'test-hb-1' AND status = 'ARCHIVED' AND deleted = 0;

-- Now we can archive the PUBLISHED version (no constraint violation)
UPDATE welcome_page_version 
SET status = 'ARCHIVED' 
WHERE handbook_id = 'test-hb-1' AND status = 'PUBLISHED' AND deleted = 0;

-- And promote DRAFT to PUBLISHED
UPDATE welcome_page_version 
SET status = 'PUBLISHED' 
WHERE handbook_id = 'test-hb-1' AND status = 'DRAFT' AND deleted = 0;

-- Final verification
SELECT 'After Publishing' as view_type, handbook_id, status, id, deleted, deleted_date
FROM welcome_page_version 
WHERE handbook_id = 'test-hb-1'
ORDER BY status, deleted, created_at;
-- Expected: 3 ARCHIVED (1 active, 3 soft-deleted), 1 PUBLISHED, 0 DRAFT

-- Cleanup (optional)
-- DELETE FROM welcome_page_version WHERE handbook_id = 'test-hb-1';
