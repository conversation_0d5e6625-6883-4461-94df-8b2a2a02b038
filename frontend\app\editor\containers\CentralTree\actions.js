import { createAction } from 'redux-actions';

export const fetchHandbooks = createAction('CentralTree/FETCH_HANDBOOKS_REQUEST');
export const fetchHandbooksSuccess = createAction('CentralTree/FETCH_HANDBOOKS_SUCCESS');

export const fetchHandbookContent = createAction('CentralTree/FETCH_HANDBOOK_CONTENT');
export const fetchHandbookContentSuccess = createAction(
  'CentralTree/FETCH_HANDBOOK_CONTENT_SUCCESS'
);
